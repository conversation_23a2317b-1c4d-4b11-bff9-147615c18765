package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum SubPayWayType implements BaseEnum<Integer, String> {

    /**
     * 商家扫码
     */
    SUB_PAYWAY_BARCODE(1, "商家扫码"),

    /**
     * 二维码
     */
    SUB_PAYWAY_QRCODE(2, "二维码"),

    /**
     * 顾客扫码
     */
    SUB_PAYWAY_WAP(3, "顾客扫码"),

    /**
     * 小程序
     */
    SUB_PAYWAY_MINI(4, "小程序");



    private int code;

    private String desc;

    private SubPayWayType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
