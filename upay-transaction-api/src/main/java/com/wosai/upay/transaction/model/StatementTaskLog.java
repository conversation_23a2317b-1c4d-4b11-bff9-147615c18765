package com.wosai.upay.transaction.model;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 对账单下载任务申请日志表
 */

public class StatementTaskLog {
    /**
     * 任务类型，1: 对账单
     */
    public static final int TYPE_TRANSACTION = 1;
    /**
     * 任务类型，2: 订单
     */
    public static final int TYPE_ORDER = 2;
    /**
     * 任务类型，3: 集团对账单
     */
    public static final int TYPE_TRANSACTION_GROUP = 3;
    /**
     * 任务类型，4: 流水明细导出
     */
    public static final int TYPE_TRANSACTION_QUERY = 4;
    /**
     * 任务类型，5: 余额明细对账单
     */
    public static final int TYPE_BALANCE = 5;
    /**
     * 任务类型，6: 分账明细导出
     */
    public static final int TYPE_SHARING_BOOK = 6;
    /**
     * 任务类型，7: 花呗分期对账单
     */
    public static final int TYPE_HBFQ_TRANSACTION = 7;
    /**
     * 任务类型，8 结算记录导出
     */
    public static final int TYPE_WITHDRAW = 8;
    /**
     * 任务类型，9 门店码批量下载
     */
    public  static final int TYPE_QRCODE_IMG = 9;
    /**
     * 任务类型，10 分账账单下载
     */
    public  static final int TYPE_SHARING_BILL = 10;

    /**
     * 任务类型，11表单二维码图片下载
     */
    public static final int TYPE_FORM_QRCODE_IMG = 11;

    /**
     * 任务类型，12表单收款明细下载
     */
    public static final int TYPE_FORMPAY_DETAIL = 12;
    /**
     * 任务类型 13 疫情流调订单下载
     */
    public static final int TYPE_SURVEY = 13;

    /**
     * 任务类型，14 储值对账单（充值明细，核销明细，商户|门店 汇总记录下载
     */
    public static final int TYPE_PREPAIDBOOK = 14;

    /**
     * 任务类型，15 交易报表下载
     */
    public static final int TYPE_TRANSACTION_REPORT = 15;

    /**
     * 任务类型，16 顾客报表下载
     */
    public static final int TYPE_CUSTOMER_REPORT = 16;


    /**
     * 任务类型, 100: 商户的历史对账单
     */
    public static final int TYPE_HISTORICAL_TRANSACTION = 100;
    /**
     * 任务类型, 300: 集团的历史对账单
     */
    public static final int TYPE_HISTORICAL_GROUP_TRANSACTION = 300;

    /**
     * 商户平台
     */
    public static final int APPLY_SYSTEM_MSP = 1;
    /**
     * SP运营平台
     */
    public static final int APPLY_SYSTEM_SP = 2;
    /**
     * CRM
     */
    public static final int APPLY_SYSTEM_CRM = 3;
    /**
     * 服务商平台
     */
    public static final int APPLY_SYSTEM_VSP = 4;
    /**
     * 推广者平台
     */
    public static final int APPLY_SYSTEM_SSP = 5;
    /**
     * OSP1.0
     */
    public static final int APPLY_SYSTEM_OSP_V1 = 6;
    /**
     * /MSP1.0
     */
    public static final int APPLY_SYSTEM_MSP_V1 = 7;
    /**
     * 其他内部服务
     */
    public static final int APPLY_SYSTEM_OTHER = 9;
    /**
     * 未知
     */
    public static final int APPLY_SYSTEM_UNKNOWN = 10;
    /**
     * 收钱吧APP
     */
    public static final int APPLY_SYSTEM_APP = 11;

    /**
     * 任务申请状态，0：新申请
     */
    public static final int APPLY_STATUS_CREATED = 0;
    /**
     * 任务申请状态，0：执行中
     */
    public static final int APPLY_STATUS_OPERATING = 1;
    /**
     * 任务申请状态，11：执行中阶段1 ->提交进入线程池
     */
    public static final int APPLY_STATUS_IN_POOLING = 11;
    /**
     * 任务申请状态，12：执行中阶段2 ->进入执行方法
     */
    public static final int APPLY_STATUS_IN_SERVICE = 12;
    /**
     * 任务申请状态，13：执行中阶段4 ->压缩前
     */
    public static final int APPLY_STATUS_BEFORE_ZIP = 13;
    /**
     * 任务申请状态，14：执行中阶段4 ->上传前
     */
    public static final int APPLY_STATUS_BEFORE_UPLOAD = 14;
    /**
     * 任务申请状态，0：执行成功
     */
    public static final int APPLY_STATUS_SUCCESS = 2;
    /**
     * 任务申请状态，0：执行失败
     */
    public static final int APPLY_STATUS_FAIL = 3;

    /**
     * 正在处理的状态
     */
    public static final List<Integer> APPLY_STATUS_RUNNING = Arrays.asList(
            APPLY_STATUS_OPERATING,
            APPLY_STATUS_IN_POOLING,
            APPLY_STATUS_IN_SERVICE,
            APPLY_STATUS_BEFORE_ZIP,
            APPLY_STATUS_BEFORE_UPLOAD
    );


    /**
     * 支付网关的收款，储值核销和储值核销退款，不包括储值充值和储值充值退款
     */
    public static final int INCLUDE_PAY = 1;
    /**
     * 包含记账类（不包含储值核销，储值核销退款）
     */
    public static final int INCLUDE_CHARGE = 2;

    /**
     * 包含储值充值，储值退款
     */
    public static final int INCLUDE_STORE_IN = 3;

    /**
     * 包含收银台类交易
     */
    public static final int INCLUDE_CASH_DESK = 4;


    /**
     * 任务状态描述
     */
    public static final Map APPLY_STATUSES_DESC = new HashMap() {{
        put(APPLY_STATUS_CREATED, "新申请");
        put(APPLY_STATUS_OPERATING, "执行中");
        put(APPLY_STATUS_IN_POOLING, "执行中阶段1 ->提交进入线程池");
        put(APPLY_STATUS_IN_SERVICE, "执行中阶段2 ->进入执行方法");
        put(APPLY_STATUS_BEFORE_ZIP, "执行中阶段3 ->压缩前");
        put(APPLY_STATUS_BEFORE_UPLOAD, "执行中阶段4 ->上传前");
        put(APPLY_STATUS_SUCCESS, "执行成功");
        put(APPLY_STATUS_FAIL, "执行失败");
    }};


    /**
     * 任务类型述
     */
    public static final Map TYPES_DESC = new HashMap() {{
        put(TYPE_TRANSACTION, "对账单");
        put(TYPE_ORDER, "订单");
        put(TYPE_TRANSACTION_GROUP, "集团对账单");
        put(TYPE_TRANSACTION_QUERY, "流水明细");
        put(TYPE_BALANCE, "余额明细对账单");
        put(TYPE_SHARING_BOOK, "分账明细");
        put(TYPE_HBFQ_TRANSACTION, "分期收款对账单");
        put(TYPE_HISTORICAL_TRANSACTION, "历史对账单");
        put(TYPE_HISTORICAL_GROUP_TRANSACTION, "集团历史对账单");
        put(APPLY_SYSTEM_OTHER, "门店码下载");
        put(TYPE_SHARING_BILL, "分账账单下载");
        put(TYPE_FORM_QRCODE_IMG, "收款单二维码图片下载");
        put(TYPE_FORMPAY_DETAIL, "收款单收款明细下载");
    }};

    /**
     * 任务平台描述
     */
    public static final Map APPLY_SYSTEMS_DESC = new HashMap() {{
        put(APPLY_SYSTEM_MSP, "商户平台");
        put(APPLY_SYSTEM_SP, "SP运营平台");
        put(APPLY_SYSTEM_CRM, "CRM平台");
        put(APPLY_SYSTEM_VSP, "服务商平台");
        put(APPLY_SYSTEM_SSP, "推广者平台");
        put(APPLY_SYSTEM_OSP_V1, "OSP1.0");
        put(APPLY_SYSTEM_MSP_V1, "MSP1");
        put(APPLY_SYSTEM_OTHER, "其他内部服务");
        put(APPLY_SYSTEM_UNKNOWN, "未知");
    }};


    /**
     * 申请任务类型，1：商户对账单下载;2：集团对账单下载;3：商户大容量对账单下载;4：集团大容量对账单下载
     */
    public static final String TYPE = "type";

    /**
     * 任务类型描述
     */
    public static final String TYPE_DESC = "type_desc";

    /**
     * 对账单导出包含的账类型
     */
    public static final String INCLUDES = "includes";

    /**
     * 请求用户id
     */
    public static final String APPLY_USER_ID = "apply_user_id";

    /**
     * 请求系统，1:商户平台; 2:SP运营平台; 3:CRM; 4: 服务商平台; 5:推广者平台; 6:OSP1.0; 7:MSP1.0; 9:其他内部服务, 10: 未知'
     */
    public static final String APPLY_SYSTEM = "apply_system";
    /**
     * 请求系统描述
     */
    public static final String APPLY_SYSTEM_DESC = "apply_system_desc";
    /**
     * 导出任务标题
     */
    public static final String TITLE = "title";
    /**
     * 导出任务数字签名md5(类型+条件)
     */
    public static final String TASK_SIGNATURE = "task_signature";
    /**
     * 申请参数（JSON）
     */
    public static final String PAYLOAD = "payload";
    /**
     * 申请用户id
     */
    public static final String USER_ID = "user_id";
    /**
     * 任务申请状态，0：新申请，1：执行中，2：执行成功，3：执行失败
     */
    public static final String APPLY_STATUS = "apply_status";
    /**
     * 状态集合
     */
    public static final String APPLY_STATUSES = "apply_statuses";
    /**
     * 任务状态描述
     */
    public static final String APPLY_STATUS_DESC = "apply_status_desc";
    /**
     * 申请日期
     */
    public static final String APPLY_DATE = "apply_date";
    /**
     * 申请结果
     */
    public static final String APPLY_RESULT = "apply_result";
    /**
     * 执行时间, 毫秒数
     */
    public static final String OPERATE_TIME = "operate_time";
    /**
     * 文件大小，字节数
     */
    public static final String STATEMENT_SIZE = "statement_size";
    /**
     * 对账行数'
     */
    public static final String STATEMENT_ROW = "statement_row";
    /**
     * 对账单预估行数
     */
    public static final String STATEMENT_ROW_ESTIMATE = "statement_row_estimate";
    /**
     * 当前已导出行数
     */
    public static final String STATEMENT_ROW_NOW = "statement_row_now";
    /**
     * 剩余时间描述文案
     */
    public static final String EXPORT_REMAIN_TIME = "export_remain_time";
    /**
     * 任务结束后的操作
     */
    public static final String FINISH_OPERATION = "finish_operation";
    /**
     * 任务结束后操作的参数
     */
    public static final String OPERATION_EXTRA = "operation_extra"; // "{"to":["<EMAIL>"],"status":1,"error_msg":""}"
    /**
     * 任务结束后 无任何操作
     */
    public static final int FINISH_OPERATION_NULL = 0;
    /**
     * 任务结束后 发送邮件
     */
    public static final int FINISH_OPERATION_EMAIL = 1;
    /**
     * 扩展字段 发送邮箱
     */
    public static final String EXTRA_TO = "to";
    /**
     * 扩展字段 执行状态
     */
    public static final String EXTRA_STATUS = "status";
    /**
     * 扩展字段 错误信息
     */
    public static final String EXTRA_ERROR_MSG = "error_msg";
    /**
     * 扩展字段 状态 未执行
     */
    public static final Integer EXTRA_STATUS_INIT = 0;
    /**
     * 扩展字段 状态 进行中
     */
    public static final Integer EXTRA_STATUS_ONGOING = 1;
    /**
     * 扩展字段 状态 执行成功
     */
    public static final Integer EXTRA_STATUS_SUCCESS = 2;
    /**
     * 扩展字段 状态 执行失败
     */
    public static final Integer EXTRA_STATUS_FAIL = 3;


    public static final List<Integer> MERCHANT_EXPORT_TYPES = Arrays.asList(1, 3);

    //针对对账单、订单、流水明细、分账明细、分期收款对账单、历史对账单和分账账单下载需做数据权限的管控
    public static final List<Integer> EXPORT_DATA_PERMISSION = Arrays.asList(StatementTaskLog.TYPE_TRANSACTION,
            StatementTaskLog.TYPE_ORDER, StatementTaskLog.TYPE_TRANSACTION_QUERY, StatementTaskLog.TYPE_BALANCE, StatementTaskLog.TYPE_SHARING_BOOK,
            StatementTaskLog.TYPE_HBFQ_TRANSACTION, StatementTaskLog.TYPE_SHARING_BILL, StatementTaskLog.TYPE_HISTORICAL_TRANSACTION,
            StatementTaskLog.TYPE_TRANSACTION_REPORT, StatementTaskLog.TYPE_CUSTOMER_REPORT);

}
