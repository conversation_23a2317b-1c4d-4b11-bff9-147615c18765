package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum TerminalType implements BaseEnum<Integer, String> {

    /**
     * Android应用
     */
    ANDROID_APP(10, "Android应用"),

    /**
     * iOS应用
     */
    IOS_APP(11, "iOS应用"),

    /**
     * Windows桌面应用
     */
    WINDOWS_APP(20, "Windows桌面应用"),

    /**
     * 专用设备
     */
    SPECIAL_EQUIP(30, "专用设备"),

    /**
     * 门店码
     */
    STORE_CODE(40, "门店码"),

    /**
     * 服务
     */
    SERVICE(50, "服务");

    private int code;

    private String desc;

    private TerminalType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
