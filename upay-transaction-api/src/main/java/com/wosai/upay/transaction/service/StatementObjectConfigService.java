package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.PropNotEmpty;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.model.StatementObjectConfig;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/statementObjectConfig")
public interface StatementObjectConfigService {


    /**
     * {
     *     "language":"",
     *     "split_type":0,
     *     "sheet_params":{
     *      "sheet_type":0,
     *         "cro":["transaction_no","transaction_amount","refund_no","refund_amount","merchant_discount","wosai_discount","payway_type_discount",...],
     *         "payway":{
     *
     *         }
     *     }
     * }
     * @param config
     */
    void insertOrUpdate(@PropNotEmpty.List({
            @PropNotEmpty(value = StatementObjectConfig.OBJECT_ID, message = "{value}对象id不能为空"),
            @PropNotEmpty(value = StatementObjectConfig.LANGUAGE, message = "{value}语言配置不能为空不能为空"),
    }) Map config);

    Map getConfigByObjectId(@NotNull(message = "对象id不能为空") String objectId);


    /**
     *
     * @param groupSn
     * @return
     */
    Map getLanguageByGroupSn(String groupSn);


}
