package com.wosai.upay.transaction.model;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class TTaskInfo {

    /**
     * 阻塞个数
     */
    public int blockNum;


    /**
     * 任务个数
     */
    public int taskNum;

    /**
     * 任务类型
     */
    public Integer type;



    /**
     * 任务id
     */

    public static final String TASK_ID = "taskId";


    /**
     * 阻塞时长
     */
    public static final String BLOCK_TIME = "blockTime";


    /**
     * 执行时长
     */
    public static final String EXECUTE_TIME = "executeTime";


    /**
     * 任务行数
     */
    public static final String TOTAL_ROWS = "totalRows";





    /**
     * 任务detail信息
     */
    public List<Map<String,Object>> taskDetails = new ArrayList<>();


    public int getBlockNum() {
        return blockNum;
    }

    public void setBlockNum(int blockNum) {
        this.blockNum = blockNum;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<Map<String, Object>> getTaskDetails() {
        return taskDetails;
    }

    public Integer getTaskNum() {
        return taskNum;
    }

    public void setTaskNum(Integer taskNum) {
        this.taskNum = taskNum;
    }

    @Override
    public String toString() {
        return "blockNum=" + blockNum +
                ", taskNum=" + taskNum +
                ", type=" + type;

    }
}
