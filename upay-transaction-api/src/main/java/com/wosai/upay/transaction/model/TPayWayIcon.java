package com.wosai.upay.transaction.model;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class TPayWayIcon {

    /**
     * 支付源
     */
    private String payWay;

    /**
     * 支付源图标
     */
    private String payWayIcon;

    /**
     * 支付源灰度图标
     */
    private String payWayGreyIcon;

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public String getPayWayIcon() {
        return payWayIcon;
    }

    public void setPayWayIcon(String payWayIcon) {
        this.payWayIcon = payWayIcon;
    }

    public String getPayWayGreyIcon() {
        return payWayGreyIcon;
    }

    public void setPayWayGreyIcon(String payWayGreyIcon) {
        this.payWayGreyIcon = payWayGreyIcon;
    }

    public TPayWayIcon(String payWay, String payWayIcon, String payWayGreyIcon) {
        this.payWay = payWay;
        this.payWayIcon = payWayIcon;
        this.payWayGreyIcon = payWayGreyIcon;
    }

    public TPayWayIcon() {
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TPayWayIcon that = (TPayWayIcon) o;
        return Objects.equals(payWay, that.payWay) &&
                Objects.equals(payWayIcon, that.payWayIcon) &&
                Objects.equals(payWayGreyIcon, that.payWayGreyIcon);
    }

    @Override
    public int hashCode() {

        return Objects.hash(payWay, payWayIcon, payWayGreyIcon);
    }
}
