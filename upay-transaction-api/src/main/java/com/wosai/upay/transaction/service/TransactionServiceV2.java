package com.wosai.upay.transaction.service;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.annotation.*;


@CommonTransactionValidated
@CommonTransactionService
@DBSelectService
@JsonRpcService(value = "rpc/transaction_v2")
public interface TransactionServiceV2 {
	 /**
     * 查询流水列表
     *
     * @param pageInfo
     * @param queryFilter terminal_id
     *                    terminal_sn
     *                    terminal_name
     *                    store_id
     *                    store_sn
     *                    store_name
     *                    merchant_id
     *                    merchant_sn
     *                    merchant_name
     *                    merchant_alias
     *                    tenant_id
     *                    provider
     *                    payway
     *                    sub_payway
     *                    status
     *                    order_sn
     *                    trade_no
     *                    device_fingerprint
     *                    min_total_amount
     *                    max_total_amount
     *                    type
	 *                    open_id
     * @return
     */
	 @DBSelectMethod
	ListResult getTransactionList(
            @NotNull(message = "分页参数信息不能为空")
            PageInfo pageInfo,@DBSelectParam(value = "upayQueryType") Map<String, Object> queryFilter);

	/**
	 * 查询流水详情v2
	 *
	 * @param pageInfo
	 * @param queryFilter
	 * @return
	 */

	@DBSelectMethod
	ListResult getTransactionListV2(
			@NotNull(message = "分页参数信息不能为空")
			PageInfo pageInfo, @DBSelectParam(value = "upayQueryType") Map<String, Object> queryFilter);

	@DBSelectMethod
    List getTransactionListByOrderSn(@NotNull(message = "订单号编号不能为空")  @DBSelectParam String orderSn);


	/**
	 * 根据订单号和创建时间 查询流水信息
	 *
	 * @param orderSn
	 * @param ctime
	 * @return
	 */
	@DBSelectMethod
	List getTransactionListByOrderSn(Map<String, Object> queryFilter);

    List<Map<String, Object>> getTransactionListByTsns(@NotNull(message = "商户id不能为空") List<String> merchantIds, @NotNull(message = "流水编号不能为空") List<String> tsns, long startTime, long endTime);

	/**
	 * 查询流水详情信息
	 *
	 * @param queryFilter
	 * @return
	 */
	@DBSelectMethod
	Map<String, Object>  getTransactionDetail(Map<String, Object> queryFilter);



}

