package com.wosai.upay.transaction.model.param;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@ToString
@Data
public class BaseParam {

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 分页时间
     */
    private Date lastRecordTime;

    /**
     * 分页查询页码，从 1 开始
     */
    private Integer pageNum;

    /**
     * 每页笔数
     */
    private Integer pageSize;

    /**
     * 排序类型：1表示按金额降序、2表示按金额升序、3表示按时间降序、4表示按时间升序
     */
    private Integer orderBy;
}
