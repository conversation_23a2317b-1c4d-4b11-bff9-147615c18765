package com.wosai.upay.transaction.model;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
public class Transaction {
    public static final String TN = "transaction";

    /**
     * 支付
     */
    public static final int TYPE_PAYMENT = 30;
    /**
     * 退款撤销
     */
    public static final int TYPE_REFUND_REVOKE = 31;
    /**
     * 退款
     */
    public static final int TYPE_REFUND = 11;
    /**
     * 撤销
     */
    public static final int TYPE_CANCEL = 10;
    /**
     * 预授权
     */
    public static final int TYPE_DEPOSIT_FREEZE = 32;
    /**
     * 预授权撤销
     */
    public static final int TYPE_DEPOSIT_CANCEL = 12;
    /**
     * 预授权完成
     */
    public static final int TYPE_DEPOSIT_CONSUME = 13;
    /**
     * 预授权完成撤销 银行卡刷卡交易存在，扫码，预授权没有这个流水
     */
    public static final int TYPE_DEPOSIT_CONSUME_CANCEL = 14;
    /**
     * 记账
     */
    public static final int TYPE_CHARGE = 15;

    /**
     * 记账退款
     */
    public static final int TYPE_CHARGE_REFUND = 21;

    /**
     * 点单外卖
     */
    public static final int TYPE_ORDER_TAKE = 16;

    /**
     * 点单外卖退款
     */
    public static final int TYPE_ORDER_TAKE_REFUND = 22;

    /**
     * 储值核销
     */
    public static final int TYPE_STORE_PAY = 17;
    /**
     * 储值核销退款
     */
    public static final int TYPE_STORE_REFUND = 20;

    /**
     * 储值充值
     */
    public static final int TYPE_STORE_IN = 18;

    /**
     * 储值充值退款
     * 储值余额退款
     */
    public static final int TYPE_STORE_IN_REFUND = 19;


    public static final int STATUS_CREATED = 0;

    /**
     * 成功
     */
    public static final int STATUS_SUCCESS = 2000;
    /**
     * 失败撤单
     */
    public static final int STATUS_FAIL_CANCELED = 2001;
    /**
     * 失败
     */
    public static final int STATUS_ABORTED = 2002;
    /**
     * 预授权核销失败
     */
    public static final int STATUS_CONSUME_ERROR = 2110;
    /**
     * 通讯失败
     */
    public static final int STATUS_FAIL_PROTOCOL_1 = 2101;
    /**
     * 网络问题导致的失败
     */
    public static final int STATUS_FAIL_IO_1 = 2102;
    /**
     * 通讯失败
     */
    public static final int STATUS_FAIL_PROTOCOL_2 = 2103;
    /**
     * 网络问题导致的失败
     */
    public static final int STATUS_FAIL_IO_2 = 2104;
    /**
     * 通讯失败
     */
    public static final int STATUS_FAIL_PROTOCOL_3 = 2105;
    /**
     * 网络问题导致的失败
     */
    public static final int STATUS_FAIL_IO_3 = 2106;
    /**
     * 交易出错失败
     */
    public static final int STATUS_FAIL_ERROR = 2107;
    /**
     * 撤单失败
     */
    public static final int STATUS_CANCEL_ERROR = 2108;
    /**
     * 退款失败
     */
    public static final int STATUS_REFUND_ERROR = 2109;
    /**
     * 处理中
     */
    public static final int STATUS_IN_PROG = 1001;
    /**
     * 失败后恢复原状态
     */
    public static final int STATUS_ERROR_RECOVERY = 1002;
    /**
     * 取消中
     */
    public static final int STATUS_ABORTING = 1003;

    public static final int PRODUCT_APP = 1;
    public static final int PRODUCT_SDK = 2;
    public static final int PRODUCT_POS = 3;


    /**
     * 收钱吧 折扣(立减优惠)
     */
    public static final String PAYMENT_TYPE_DISCOUNT_SQB = "DISCOUNT_SQB";
    /**
     * 收钱吧商户 折扣(立减优惠)
     */
    public static final String PAYMENT_TYPE_DISCOUNT_SQB_MCH = "DISCOUNT_SQB_MCH";
    /**
     * 支付通道 折扣(立减优惠)
     */
    public static final String PAYMENT_TYPE_DISCOUNT_CHANNEL = "DISCOUNT_CHANNEL";
    /**
     * 折扣(立减优惠) 支付通道商户
     */
    public static final String PAYMENT_TYPE_DISCOUNT_CHANNEL_MCH = "DISCOUNT_CHANNEL_MCH";


    /**
     * 红包 收钱吧
     */
    public static final String PAYMENT_TYPE_HONGBAO_SQB = "HONGBAO_SQB";
    /**
     * 红包 收钱吧商户
     */
    public static final String PAYMENT_TYPE_HONGBAO_SQB_MCH = "HONGBAO_SQB_MCH";
    /**
     * 支付通道红包
     */
    public static final String PAYMENT_TYPE_HONGBAO_CHANNEL = "HONGBAO_CHANNEL";
    /**
     * 支付通道商户红包
     */
    public static final String PAYMENT_TYPE_HONGBAO_CHANNEL_MCH = "HONGBAO_CHANNEL_MCH";


    /**
     * 支付通道商户预付卡
     */
    public static final String PAYMENT_TYPE_CARD_CHANNEL_MCH_PRE = "CARD_PRE";
    /**
     * 支付通道商户储值卡
     */
    public static final String PAYMENT_TYPE_CARD_CHANNEL_MCH_BALANCE = "CARD_BALANCE";

    /**
     * /信用卡 银行卡
     */
    public static final String PAYMENT_TYPE_BANKCARD_CREDIT = "BANKCARD_CREDIT";
    /**
     * 储蓄卡 银行卡
     */
    public static final String PAYMENT_TYPE_BANKCARD_DEBIT = "BANKCARD_DEBIT";

    /**
     * 余额 支付宝钱包
     */
    public static final String PAYMENT_TYPE_WALLET_ALIPAY = "WALLET_ALIPAY";
    /**
     * 余额 余额宝
     */
    public static final String PAYMENT_TYPE_WALLET_ALIPAY_FINANCE = "WALLET_ALIPAY_FINANCE";
    /**
     * 余额 微信钱包
     */
    public static final String PAYMENT_TYPE_WALLET_WEIXIN = "WALLET_WEIXIN";
    /**
     * 支付宝 花呗
     */
    public static final String PAYMENT_TYPE_CUSTOM_ALIPAY_HUABEI = "ALIPAY_HUABEI";
    /**
     * 支付宝 集分宝
     */
    public static final String PAYMENT_TYPE_CUSTOM_ALIPAY_POINT = "ALIPAY_POINT";

    /*EXTRA_PARAMS 中cancel_type常量定义*/
    /**
     * 设备自动撤单
     */
    public static final String CANCEL_TYPE_DEVICE = "device";
    /**
     * 收银员撤单
     */
    public static final String CANCEL_TYPE_CASHIER = "cashier";
    /**
     * 勾兑撤单
     */
    public static final String CANCEL_TYPE_RECONCILE = "reconcile";

    /**
     * 交易流水 id
     */
    public static final String ID = "id";

    /**
     * 交易流水号（按规则自动生成） VARCHAR(20)
     */
    public static final String TSN = "tsn";
    /**
     * 商户流水号（商户下唯一）VARCHAR(32)
     */
    public static final String CLIENT_TSN = "client_tsn";

    /**
     * 类型 INT
     */
    public static final String TYPE = "type";
    /**
     * 标题 VARCHAR(45)
     */
    public static final String SUBJECT = "subject";
    /**
     * 详情 VARCHAR(255)
     */
    public static final String BODY = "body";
    /**
     * 状态 INT UNSIGNED
     */
    public static final String STATUS = "status";
    /**
     * 原始金额 BIGINT
     */
    public static final String ORIGINAL_AMOUNT = "original_amount";
    /**
     * 向支付通道请求的金额 BIGINT
     */
    public static final String EFFECTIVE_AMOUNT = "effective_amount";
    /**
     * 消费者实际支付金额
     */
    public static final String PAID_AMOUNT = "paid_amount";
    /**
     * 收钱吧或正式商户在支付通道的实际收款金额
     */
    public static final String RECEIVED_AMOUNT = "received_amount";
    /**
     * 明细 BLOB
     */
    public static final String ITEMS = "items";


    /**
     * 付款人在支付通道的用户ID VARCHAR(45)
     */
    public static final String BUYER_UID = "buyer_uid";
    /**
     * 付款人在支付通道的登录账户 VARCAR(45)
     */
    public static final String BUYER_LOGIN = "buyer_login";
    /**
     * 服务商UUID
     */
    public static final String TENANT_ID = "tenant_id";
    /**
     * 销售或者代理的UUID
     */
    public static final String SELLER_ID = "seller_id";

    public static final String PID = "pid";

    /**
     * 支付宝门店号
     */
    public static final String ALIPAY_STORE_ID = "alipay_store_id";
    /**
     * 支付宝门店号 （授权里面获取到的）
     */
    public static final String APP_AUTH_SHOP_ID = "app_auth_shop_id";
    /**
     * 支付源商户号
     */
    public static final String PAYWAY_MERCHANT_ID = "payway_merchant_id";
    /**
     * 间连收单机构商户号
     */
    public static final String PROVIDER_MCH_ID = "provider_mch_id";
    /**
     * 销售或者代理的路径
     */
    public static final String SELLER_PATH = "seller_path";
    /**
     * 商户ID VARCHAR(37)  商户记录的UUID
     */
    public static final String MERCHANT_ID = "merchant_id";

    /**
     * 商户ID VARCHAR(37)  商户记录的UUID
     */
    public static final String MERCHANTID = "merchantId";

    /**
     * 门店ID VARCHAR(37) 门店记录的UUID
     */
    public static final String STORE_ID = "store_id";
    /**
     * VARCHAR(37) 终端记录的UUID
     */
    public static final String TERMINAL_ID = "terminal_id";
    /**
     * VARCHAR(45) 操作员姓名或其它ID
     */
    public static final String OPERATOR = "operator";

    /**
     * 订单号 VARCHAR(20)
     */
    public static final String ORDER_SN = "order_sn";
    /**
     * 订单ID VARCHAR(37)
     */
    public static final String ORDER_ID = "order_id";

    /**
     * 支付通道 INT 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉
     */
    public static final String PROVIDER = "provider";
    /**
     * 支付通道 INT UNSIGNED
     */
    public static final String PAYWAY = "payway";
    /**
     * 支付方式 INT UNSIGNED
     */
    public static final String SUB_PAYWAY = "sub_payway";
    /**
     * 支付通道返回的交易凭证号 VARCHAR(128)
     */
    public static final String TRADE_NO = "trade_no";

    /**
     * 产品标志 INT
     */
    public static final String PRODUCT_FLAG = "product_flag";

    /**
     * 可选参数（poi, notify_url, remark, barcode, client_terminal_id, client_store_id) BLOB
     */
    public static final String EXTRA_PARAMS = "extra_params";
    public static final String EXTRA_PARAMS_CROSS_MCH_REFUND = "extra_params.cross_mch_refund";

    /**
     * extra_params 中的 分账信息
     */
    public static final String PROFIT_SHARING = "profit_sharing";

    /**
     * EXTRA_PARAMS
     */
    public static final String POI = "poi";
    /**
     * poi
     */
    public static final String LONGITUDE = "longitude";
    /**
     * poi
     */
    public static final String LATITUDE = "latitude";
    /**
     * EXTRA_PARAMS
     */
    public static final String NOTIFY_URL = "notify_url";
    /**
     * EXTRA_PARAMS
     */
    public static final String REMARK = "remark";
    /**
     * EXTRA_PARAMS
     */
    public static final String BARCODE = "barcode";
    /**
     * EXTRA_PARAMS
     */
    public static final String DEVICE_ID = "device_id";
    /**
     * EXTRA_PARAMS
     */
    public static final String PAYER_UID = "payer_uid";
    /**
     * EXTRA_PARAMS
     */
    public static final String CANCEL_TYPE = "cancel_type";

    /**
     * EXTRA_PARAMS
     */
    public static final String SQB_WALLET_NAME = "sqb_wallet_name";

    /**
     * EXTRA_PARAMS
     */
    public static final String SQB_PAY_SOURCE = "sqb_pay_source";
    public static final String SQB_PAY_SOURCE_NAME = "sqb_pay_source_name";

    /**
     * EXTRA_PARAMS
     */
    public static final String SQB_PAY_PATH = "sqb_pay_path";



    public static final String SQB_PAY_PATH_NAME = "sqb_pay_path_name";

    /**
     * EXTRA_PARAMS
     */
    public static final String SQB_CHARGE_SOURCE = "sqb_charge_source";

    /**
     * EXTRA_PARAMS
     */
    public static final String SQB_USER_ID = "sqb_user_id";

    /**
     * EXTRA_PARAMS
     */
    public static final String SQB_MARK_UP_AMOUNT = "sqb_mark_up_amount";


    /**
     * 可选的交易流水的返回字段（qrcode)BLOB
     */
    public static final String EXTRA_OUT_FIELDS = "extra_out_fields";
    /**
     * 支付方式
     */
    public static final String PAY_TYPE = "pay_type";
    /**
     * 系统参考号
     */
    public static final String REFER_NUMBER = "refernumber";

    /**
     * 订单信息
     */
    public static final String ORDER_INFO = "order_info";
    /**
     * 授权号
     */
    public static final String BATCH_BILL_NO = "batchbillno";

    /**
     * 凭证号
     */
    public static final String SYS_TRACE_NO = "systraceno";
    /**
     * 付款银行
     */
    public static final String BANK_TYPE = "bank_type";
    /**
     * 授权码
     */
    public static final String AUTH_NO = "authno";

    /**
     * EXTRA_OUT_FIELDS
     */
    public static final String BANK_TRADE_NO = "bank_trade_no";
    /**
     * EXTRA_OUT_FIELDS
     */
    public static final String QRCODE = "qrcode";
    /**
     * EXTRA_OUT_FIELDS 支付通道的订单号
     */
    public static final String CHANNEL_TRADE_NO = TRADE_NO;
    /**
     * EXTRA_OUT_FIELDS 支付通道的订单号
     */
    public static final String EXTRA_CHANNEL_TRADE_NO = "channel_trade_no";
    /**
     * EXTRA_OUT_FIELDS 针对支付通道生成的wap支付请求。里面的内容由wap支付前端和支付通道之间约定。
     */
    public static final String WAP_PAY_REQUEST = "wap_pay_request";
    /**
     * EXTRA_OUT_FIELDS 消费者付款方式明细 List
     */
    public static final String PAYMENTS = "payments";

    /**
     * 是否是默认经纬度
     */
    public static final String IS_DEFAULT_POI = "is_default_poi";
    /**
     * EXTRA_OUT_FIELDS 额度包标示 String
     */
    public static final String QUOTA_FEE_RATE_TAG = "quota_fee_rate_tag";
    /**
     * EXTRA_OUT_FIELDS 是否为勾兑成功 boolean
     */
    public static final String IS_FIX = "is_fix";
    /**
     * EXTRA_OUT_FIELDS 是否为预授权 boolean
     */
    public static final String IS_DEPOIST = "is_deposit";

    /**
     * PAYMENT 付款方式
     */
    public static final String PAYMENT_TYPE = "type";
    /**
     * PAYMENT 原始付款方式
     */
    public static final String PAYMENT_ORIGIN_TYPE = "origin_type";
    /**
     * PAYMENT 付款金额
     */
    public static final String PAYMENT_AMOUNT = "amount";
    /**
     * EXTRA_OUT_FIELDS 微信交易buyer_uid对应的公众号id
     */
    public static final String WEIXIN_APPID = "weixin_appid";
    public static final String OVERSEAS = "overseas";
    public static final String COMBO_ID = "combo_id";
    /**
     * EXTRA_OUT_FIELDS 币种转换方式，edc或dcc
     */
    public static final String WILD_CARD_TYPE = "wild_card_type"; 
    /**
     * 支付的钱包类型(ALIPAYCN ALPAYHK ...)
     */
    public static final String PAYMENT_INST = "payment_inst";
    /**
     * 交易货币类型，符合ISO 4217标准的三位字母代码
     */
    public static final String CURRENCY = "currency";
    public static final String MERCHANT_DEFAULT_CURRENCY = "merchant_default_currency";

    /**
     * 透传到支付通道的参数(goods_details)，由商户和支付通道约定，我们不做解析 BLOB
     */
    public static final String EXTENDED_PARAMS = "extended_params";

    /**
     * EXTENDED_PARAMS sub_appid
     */
    public static final String SUB_APPID = "sub_appid";

    /**
     * 商户上传的附加字段，保存在订单中。终端查询的时候原样返回。
     */
    public static final String REFLECT = "reflect";

    /**
     * 配置参数快照 BLOB
     */
    public static final String CONFIG_SNAPSHOT = "config_snapshot";
    public static final String TERMINAL_VENDOR_APP_APPID = "terminal_vendor_app_appid";

    public static String SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY = "provider_trade_params_key";
    public static final String LAKALA_TRADE_PARAMS = "lakala_trade_params";
    public static final String LAKALA_MERC_ID = "lakala_merc_id";
    public static final String LAKALA_TERM_ID = "lakala_term_id";

    /**
     * nfc支付的时候的银行卡
     */
    public static final String NFC_CARD = "nfc_card";
    /**
     * nfc支付的时候的银行卡
     */
    public static final String NFC_CARDSN = "nfc_cardsn";
    /**
     * nfc支付的时候的银行卡
     */
    public static final String NFC_NFC = "nfc_nfc";
    /**
     * nfc支付的时候的银行卡
     */
    public static final String NFC_TRACK2 = "nfc_track2";
    /**
     * nfc支付的时候的银行卡
     */
    public static final String NFC_PIN = "nfc_pin";

    /**
     * 第三方支付通道的原始返回内容
     */
    public static final String PROVIDER_RESPONSE = "provider_response";
    /**
     * 交易完成时间（从支付通道得到）BIGINT
     */
    public static final String CHANNEL_FINISH_TIME = "channel_finish_time";
    /**
     * 交易完成时间（收钱吧系统时间）BIGINT
     */
    public static final String FINISH_TIME = "finish_time";
    /**
     * 业务错误码，参考com.wosai.upay.workflow.UpayBizError
     */
    public static final String BIZ_ERROR_CODE = "biz_error_code";
    /**
     * 支付通道返回的错误信息 BLOB
     */
    public static final String PROVIDER_ERROR_INFO = "provider_error_info";

    /**
     * 存放在transaction.items.payments里折扣立减信息，把这些字段拆开为
     */
    /**
     * wosai红包金额
     */
    public static final String HONGBAO_WOSAI_AMOUNT = "hongbao_wosai_amount";
    /**
     * wosai商户红包金额
     */
    public static final String HONGBAO_WOSAI_MCH_AMOUNT = "hongbao_wosai_mch_amount";
    /**
     * 支付通道返回的错误信息 BLOB
     */
    public static final String DISCOUNT_WOSAI_AMOUNT = "discount_wosai_amount";
    /**
     * 支付通道返回的错误信息 BLOB
     */
    public static final String DISCOUNT_WOSAI_MCH_AMOUNT = "discount_wosai_mch_amount";
    /**
     * wosai商户折扣类型
     */
    public static final String DISCOUNT_WOSAI_MCH_TYPE = "discount_wosai_mch_type";

    /**
     * 支付通道返回的错误信息 BLOB
     */
    public static final String DISCOUNT_CHANNEL_AMOUNT = "discount_channel_amount";
    /**
     * 支付通道返回的错误信息 BLOB
     */
    public static final String DISCOUNT_CHANNEL_MCH_AMOUNT = "discount_channel_mch_amount";
    /**
     * 支付通道返回的错误信息 BLOB
     */
    public static final String DISCOUNT_CHANNEL_MCH_TOP_UP_AMOUNT = "discount_channel_mch_top_up_amount";
    /**
     * 支付通道返回的错误信息 BLOB
     */
    public static final String HONGBAO_CHANNEL_AMOUNT = "hongbao_channel_amount";
    /**
     * 支付通道返回的错误信息 BLOB
     */
    public static final String HONGBAO_CHANNEL_MCH_AMOUNT = "hongbao_channel_mch_amount";
    /**
     * 支付通道返回的错误信息 BLOB
     */
    public static final String HONGBAO_CHANNEL_MCH_TOP_UP_AMOUNT = "hongbao_channel_mch_top_up_amount";
    /**
     * 实收金额
     */
    public static final String ACTUAL_RECEIVE_AMOUNT = "actual_receive_amount";
    /**
     * 结算金额
     */
    public static final String CLEARING_AMOUNT = "clearing_amount";

    /**
     * 扩展计算的字段
     */
    /**
     * 商户优惠
     */
    public static final String MCH_FAVORABLE_AMOUNT = "mch_favorable_amount";
    /**
     * 收钱吧优惠
     */
    public static final String WOSAI_FAVORABLE_AMOUNT = "wosai_favorable_amount";
    /**
     * 收款通道优惠
     */
    public static final String CHANNEL_FAVORABLE_AMOUNT = "channel_favorable_amount";
    /**
     * 收款通道机构优惠
     */
    public static final String CHANNEL_AGENT_FAVORABLE_AMOUNT = "channel_agent_favorable_amount";
    /**
     * 收款通道商户免充值优惠
     */
    public static final String CHANNEL_MCH_FAVORABLE_AMOUNT = "channel_mch_favorable_amount";
    /**
     * 收款通道商户充值优惠
     */
    public static final String CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT = "channel_mch_top_up_favorable_amount";

    /**
     * 终端名称
     */
    public static final String TERMINAL_NAME = "terminal_name";

    /**
     * 收银台id
     */
    public static final String CASH_DESK_ID = "cash_desk_id";
    /**
     * 收银台名称
     */
    public static final String CASH_DESK_NAME = "cash_desk_name";
    /**
     * 门店名称
     */
    public static final String STORE_NAME = "store_name";
    /**
     * 商户名称
     */
    public static final String MERCHANT_NAME = "merchant_name";
    /**
     * 门店sn
     */
    public static final String STORE_SN = "store_sn";
    /**
     * 手续费
     */
    public static final String FEE = "fee";
    /**
     * 费率
     */
    public static final String FEE_RATE = "fee_rate";

    /**
     * 备注
     */
    public static final String TRADE_MEMO = "trade_memo";

    public static final String PAID_COUNT = "paid_count";

    public static final String REFLECT_DEPOSIT_TYPE = "deposit";
    public static final String REFLECT_DEPOSIT_TYPE_KEY = "type";

    public static final String REFLECT_DEPOSIT_CELLPHONE = "cellphone";
    public static final String REFLECT_DEPOSIT_REMARK = "remark";
    public static final String DEPOSIT_AMOUNT = "deposit_amount";
    public static final String DEPOSIT_COUNT = "deposit_count";
    public static final String REFUNDED_AMOUNT = "refunded_amount";
    public static final String REFUNDED_COUNT = "refunded_count";
    public static final String DEPOSIT_CANCELED_AMOUNT = "deposit_canceled_amount";
    public static final String DEPOSIT_CANCELED_COUNT = "deposit_canceled_count";
    public static final String CANCELED_AMOUNT = "canceled_amount";
    public static final String CANCELED_COUNT = "canceled_count";

    /**
     * 记账
     */
    public static final String CHARGE_AMOUNT = "charge_amount";
    public static final String CHARGE_COUNT = "charge_count";
    /**
     * 记账退款
     */
    public static final String CHARGE_REFUND_AMOUNT = "charge_refund_amount";
    public static final String CHARGE_REFUND_COUNT = "charge_refund_count";
    /**
     * 点单外卖
     */
    public static final String ORDER_TAKE_AMOUNT = "order_take_amount";
    public static final String ORDER_TAKE_COUNT = "order_take_count";
    /**
     * 点单外卖退款
     */
    public static final String ORDER_TAKE_REFUND_AMOUNT = "order_take_refund_amount";
    public static final String ORDER_TAKE_REFUND_COUNT = "order_take_refund_count";

    /**
     * 储值核销
     */
    public static final String STORE_PAY_AMOUNT = "store_pay_amount";
    public static final String STORE_PAY_COUNT = "store_pay_count";


    /**
     * 储值核销退款
     */
    public static final String STORE_REFUND_AMOUNT = "store_refund_amount";
    public static final String STORE_REFUND_COUNT = "store_refund_count";

    /**
     * 储值充值
     */
    public static final String STORE_IN_AMOUNT = "store_in_amount";
    public static final String STORE_IN_COUNT = "store_in_count";
    /**
     * 储值充值退款
     */
    public static final String STORE_IN_REFUND_AMOUNT = "store_in_refund_amount";
    public static final String STORE_IN_REFUND_COUNT = "store_in_refund_count";

    /**
     * 预授权
     */
    public static final String DEPOSIT_FREEZE_AMOUNT = "deposit_freeze_amount";
    public static final String DEPOSIT_FREEZE_COUNT = "deposit_freeze_count";
    /**
     * 预授权撤销
     */
    public static final String DEPOSIT_FREEZE_CANCELED_AMOUNT = "deposit_freeze_canceled_amount";
    public static final String DEPOSIT_FREEZE_CANCELED_COUNT = "deposit_freeze_canceled_count";


    public static final String CLIENT_IP = "client_ip";
    public static final String SQB_CSB_TO_WAP_SN = "sqb_csb_to_wap_sn";
    public static final String VENDOR_APP_APPID = "vendor_app_appid";

    /**
     * 分账金额
     */
    public static final String SHARING_AMOUNT = "sharing_amount";

    /**
     * 技术服务费
     */
    public static final String TRADE_SERVICE_SHARING_AMOUNT = "trade_service_sharing_amount";

    /**
     * 分期商户贴息金额
     */
    public static final String FQ_MCH_DISCOUNT_AMOUNT = "fq_mch_discount_amount";

    /**
     * 未回退分账金额
     */
    public static final String NOT_RESTITUTE_SHARING_AMOUNT = "not_restitute_sharing_amount";

    /**
     * 分期类型
     */
    public static final String HB_FQ_TYPE = "hb_fq_type";
    public static final String FQ_TYPE = "fq_type";
    public static final int FQ_TYPE_SELLER = 1;
    public static final int FQ_TYPE_BUYER = 2;

    public static final String TRADE_APP  = "trade_app";

    public static final String TRADE_APP_NAME = "trade_app_name";
    public static final String SQB_BIZ_MODEL  = "sqb_biz_model";

    public static final String SQB_BIZ_MODEL_NAME = "sqb_biz_model_name";

    public static final String LOCATION = "location";

    public static final String PROVIDER_TERMINAL_ID = "provider_terminal_id";

    public static final String TERM_ID = "term_id";


    // 银联开放平台
    public static final String UNION_PAY_OPEN_TRADE_PARAMS = "upo_trade_params";

    public static final String UNION_PAY_OPEN_TERM_ID = LAKALA_TERM_ID;


    //银联商务通道
    public static final String CHINAUMS_TRADE_PARAMS = "chinaums_trade_params";

    public static final String CHINAUMS_TERM_CODE = "term_code";


    //索迪斯
    public static final String SODEXO_TRADE_PARAMS = "sodexo_trade_params";

    public static final String SODEXO_TID = "tid";


    //通联银联交易参数配置
    public static final String UNION_PAY_TL_TRADE_PARAMS = "up_tl_trade_params";


    public static final String UNION_PAY_TL_UNION_TERM_ID = "term_id";                                    //UNION_PAY_TL_UNION_TERM_ID商户终端号


    //澳门极易付通道交易参数
    public static final String UEPAY_TRADE_PARAMS = "uepay_trade_params";

    public static final String UEPAY_TERMINAL_CODE = "terminal_code";  //极易付终端号


    //广发通道交易参数
    public static final String CGBBANK_TRADE_PARAMS = "cgbbank_trade_params";

    public static final String CGBBANK_TERMINAL_ID = "terminal_id";             //被扫终端id
    public static final String CGBBANK_PRE_TERMINAL_ID = "pre_terminal_id";     //主扫终端id


    //富士康交易参数
    public static final String FOXCONN_TRADE_PARAMS = "foxconn_trade_params";

    public static final String FOXCONN_EQUIPMENT_SN = "equipment_sn";  //机具序列号


    //华夏银行交易参数
    public static final String HXBANK_TRADE_PARAMS = "hxbank_trade_params";

    public static final String HXBANK_PROVIDER_TERM_ID = "provider_term_id";  // 华夏端商户的虚拟终端号


    //建行通道交易参数
    public static final String CCB_TRADE_PARAMS = "ccb_trade_params";

    public static final String CCB_TERMINAL_NO = "terminal_no"; //建行终端号


    //grabpay通道交易参数
    public static final String GRABPAY_TRADE_PARAMS = "grabpay_trade_params";

    public static final String GRABPAY_TERMINAL_ID = "terminal_id"; //grabpay 提供给终端的唯一标识


    //拉卡拉开发平台v3
    public static final String LAKALA_UNION_PAY_OPEN_TRADE_PARAMS = "lakala_open_trade_params";

    public static final String LAKALA_UNION_PAY_OPEN_TERM_ID = "term_id";


    //通联收银宝
    public static final String TL_SYB_TRADE_PARAMS = "tl_syb_trade_params";

    public static final String TL_SYB_TERM_ID = "term_id";


    //富友交易参数配置
    public static final String FUYOU_TRADE_PARAMS = "fuyou_trade_params";


    public static final String FUYOU_BANK_TERM_ID = "term_id"; //富友终端号


    //福建农信交易参数
    public static final String FJNX_TRADE_PARAMS = "fjnx_trade_params";

    public static final String FJNX_TERMINAL_ID = "fjnx_terminal_id"; //农信终端id


    public static final String SPDB_UP_TRADE_PARAMS = "spdb_up_trade_params";  //浦发银行对接参数

    public static final String SPDB_PROVIDER_TERM_ID = "spdb_terminal_id";  // 浦发银行提供的终端号


    public static final String WX_INSTALLMENT_INFO = "wx_installment_info"; //EXTRA_OUT_FIELDS 微信分付 微信分付详情
    public static final String WX_INSTALLMENT_INFO_SUBSIDY = "use_subsidy"; //EXTRA_OUT_FIELDS 微信分付 微信分付详情
    public static final String WX_INSTALLMENT_INFO_NUM = "selected_installment_number"; //EXTRA_OUT_FIELDS 微信分付 微信分付详情


    // 交易参数终端信息map
    public static HashMap<String, String> TERMINAL_MAP = new HashMap() {{
        put(UNION_PAY_OPEN_TRADE_PARAMS, UNION_PAY_OPEN_TERM_ID);
        put(CHINAUMS_TRADE_PARAMS, CHINAUMS_TERM_CODE);
        put(SODEXO_TRADE_PARAMS, SODEXO_TID);
        put(UNION_PAY_TL_TRADE_PARAMS, UNION_PAY_TL_UNION_TERM_ID);
        put(UEPAY_TRADE_PARAMS, UEPAY_TERMINAL_CODE);
        put(FOXCONN_TRADE_PARAMS, FOXCONN_EQUIPMENT_SN);
        put(HXBANK_TRADE_PARAMS, HXBANK_PROVIDER_TERM_ID);
        put(CCB_TRADE_PARAMS, CCB_TERMINAL_NO);
        put(GRABPAY_TRADE_PARAMS, GRABPAY_TERMINAL_ID);
        put(LAKALA_UNION_PAY_OPEN_TRADE_PARAMS, LAKALA_UNION_PAY_OPEN_TERM_ID);
        put(TL_SYB_TRADE_PARAMS, TL_SYB_TERM_ID);
        put(FUYOU_TRADE_PARAMS, FUYOU_BANK_TERM_ID);
        put(FJNX_TRADE_PARAMS, FJNX_TERMINAL_ID);
        put(SPDB_UP_TRADE_PARAMS, SPDB_PROVIDER_TERM_ID);
    }};

}
