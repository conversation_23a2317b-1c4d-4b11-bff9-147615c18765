package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 缓存相关服务
 *
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/cache")
public interface CacheService {

    /**
     * 删除缓存
     *
     * @param pre
     * @param key
     * @return
     */
    void deleteCache(String pre, String key);

    /**
     * 模糊删除缓存
     *
     * @param like
     * @return
     */
    void deleteCacheLike(String like);

    /**
     * 是否有缓存
     *
     * @param pre
     * @param key
     * @return
     */
    boolean hasCache(String pre, String key);

    /**
     * 重置缓存
     *
     * @param pre
     * @param key
     * @param value
     * @return
     */
    void setCache(String pre, String key, Object value);

    /**
     * 重置缓存
     *
     * @param pre
     * @param key
     * @param value
     * @param timeUnit
     * @param time
     * @return
     */
    void setCache(String pre, String key, Object value, TimeUnit timeUnit, long time);

    /**
     * 获取缓存
     *
     * @param pre
     * @param key
     * @return
     */
    Map getCacheMap(String pre, String key);

    /**
     * 获取缓存
     *
     * @param pre
     * @param key
     * @return
     */
    String getCacheString(String pre, String key);

    /**
     * 获取缓存
     *
     * @param pre
     * @param key
     * @return
     */
    List getCacheList(String pre, String key);

    /**
     * 获取缓存
     *
     * @param pre
     * @param key
     * @return
     */
    boolean getCacheBoolean(String pre, String key);

    /**
     * 获取缓存
     *
     * @param pre
     * @param key
     * @return
     */
    Integer getCacheInteger(String pre, String key);

    /**
     * 获取缓存
     *
     * @param pre
     * @param key
     * @return
     */
    Long getCacheLong(String pre, String key);



    /**
     * 获取缓存对象
     * @param redisKey
     * @return
     */
    Object getObjectRedisCache(String redisKey);


    /**
     * 设置缓存对象
     * @param redisKey
     * @param value
     * @param timeout
     * @param unit
     */
    void setObjectRedisCache(String redisKey, Object value ,long timeout, final TimeUnit unit);


    /**
     * 获取缓存对象
     * @param redisKey
     * @return
     */
    Map getHashRedisCache(String redisKey);

    /**
     * 设置缓存对象
     * @param redisKey
     * @param redisValue
     * @param timeout
     * @param unit
     */
    void setHashRedisCache(String redisKey, Map redisValue ,long timeout, final TimeUnit unit);
}
