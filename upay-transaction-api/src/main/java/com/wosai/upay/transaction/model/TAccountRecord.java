package com.wosai.upay.transaction.model;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TAccountRecord {

    /**
     * 交易id
     */
    private String id;

    /**
     * 交易sn
     */
    private String transactionSn;

    /**
     * 订单sn
     */
    private String orderSn;

    /**
     * 交易金额,原始订单金额
     */
    private Long totalFee;

    /**
     * 自己红包优惠
     */
    private Long sqbDiscount;

    /**
     * 商户自助发红包优惠
     */
    private Long outerDiscount;

    /**
     * 实收金额
     */
    private Long actualReceiveAmount;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店sn
     */
    private String storeSn;

    /**
     * 折扣图标
     */
    private List<String> discountIconList;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 流水状态，1：成功；404：失败
     */
    private Integer status;

    /**
     * 流水类型， 30：支付；10：撤单；11：退款;32: 预授权;12:预授权撤销;13:预授权完成
     */
    private Integer type;

    /**
     * 操作终端或人员名称
     */
    private String operatorName;

    /**
     * 支付源
     */
    private String payWay;

    /**
     * 支付方式
     */
    private Integer subPayWay;

    /**
     * 完成时间
     */
    private Long finishTime;

    /**
     * 创建时间
     */
    private Long ctime;

    /**
     * 终端id
     */
    private String terminalId;

    /**
     * 终端sn
     */
    private String terminalSn;

    /**
     * 终端名称
     */
    private String terminalName;

    private String detailUrl;


    private String payId;
    /**
     * 付款人
     */
    private String payAccount;

    /**
     * 备注
     */
    private String reflect;

    /**
     * 花呗分期标示
     */
    private Boolean isInstalment = false;

    /**
     * 分期商家费用
     */
    private Long instalmentMerchantCharge = 0L;

    /**
     * 消费者实际支付金额
     */
    private Long paidAmount;

    /**
     * 向支付通道请求的金额
     */
    private Long effectiveAmount;

    /**
     * 是否预授权完成退款
     */
    private Boolean isDepositConsumeRefund;

    /**
     * 系统参考号
     */
    private String refernumber;

    /**
     * 授权码
     */
    private String authNo;

    /**
     * 支付源订单号
     */
    private String payWayTradeNo;

    /**
     * 凭证号
     */
    private String sysTraceNo;

    /**
     * 批次号
     */
    private String batchBillNo;


    /**
     * 流水类别
     * {@link com.wosai.upay.transaction.enums.TransactionCategory}
     */
    private Integer category;

    /**
     * 账本列表展示（收款，会员储值，美团外卖 等等等）
     */
    private String tag;

    /**
     * 异常交易标识 （跨日、勾兑 - 此处的勾兑特指隔日勾兑）
     */
    private String abnormalTag;

    /**
     * 交易完成时间
     */
    private Long channelFinishTime;

    private Long woSaiMchFavorableAmount;

    /**
     * 花呗分期标示
     */
    private Boolean isRefund;

    /**
     * 退款金额
     */
    private Long refundAmount;

    public Boolean getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(Boolean isRefund) {
        this.isRefund = isRefund;
    }

    public Long getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Long refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getAbnormalTag() {
        return abnormalTag;
    }

    public void setAbnormalTag(String abnormalTag) {
        this.abnormalTag = abnormalTag;
    }


    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Boolean getInstalment() {
        return isInstalment;
    }

    public void setInstalment(Boolean instalment) {
        isInstalment = instalment;
    }

    public Long getInstalmentMerchantCharge() {
        return instalmentMerchantCharge;
    }

    public void setInstalmentMerchantCharge(Long instalmentMerchantCharge) {
        this.instalmentMerchantCharge = instalmentMerchantCharge;
    }


    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public Integer getSubPayWay() {
        return subPayWay;
    }

    public void setSubPayWay(Integer subPayWay) {
        this.subPayWay = subPayWay;
    }

    public Long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Long finishTime) {
        this.finishTime = finishTime;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getTransactionSn() {
        return transactionSn;
    }

    public void setTransactionSn(String transactionSn) {
        this.transactionSn = transactionSn;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public Long getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Long totalFee) {
        this.totalFee = totalFee;
    }


    public Long getActualReceiveAmount() {
        return actualReceiveAmount;
    }

    public void setActualReceiveAmount(Long actualReceiveAmount) {
        this.actualReceiveAmount = actualReceiveAmount;
    }


    public List<String> getDiscountIconList() {
        return discountIconList;
    }

    public void setDiscountIconList(List<String> discountIconList) {
        this.discountIconList = discountIconList;
    }

    public Long getSqbDiscount() {
        return sqbDiscount;
    }

    public void setSqbDiscount(Long sqbDiscount) {
        this.sqbDiscount = sqbDiscount;
    }

    public Long getOuterDiscount() {
        return outerDiscount;
    }

    public void setOuterDiscount(Long outerDiscount) {
        this.outerDiscount = outerDiscount;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getStoreSn() {
        return storeSn;
    }

    public void setStoreSn(String storeSn) {
        this.storeSn = storeSn;
    }

    public String getDetailUrl() {
        return detailUrl;
    }

    public void setDetailUrl(String detailUrl) {
        this.detailUrl = detailUrl;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getPayAccount() {
        return payAccount;
    }

    public void setPayAccount(String payAccount) {
        this.payAccount = payAccount;
    }

    public String getReflect() {
        return reflect;
    }

    public void setReflect(String reflect) {
        this.reflect = reflect;
    }

    public Long getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Long paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Long getEffectiveAmount() {
        return effectiveAmount;
    }

    public void setEffectiveAmount(Long effectiveAmount) {
        this.effectiveAmount = effectiveAmount;
    }

    public String getAuthNo() {
        return authNo;
    }

    public void setAuthNo(String authNo) {
        this.authNo = authNo;
    }

    public String getPayWayTradeNo() {
        return payWayTradeNo;
    }

    public void setPayWayTradeNo(String payWayTradeNo) {
        this.payWayTradeNo = payWayTradeNo;
    }

    public String getSysTraceNo() {
        return sysTraceNo;
    }

    public void setSysTraceNo(String sysTraceNo) {
        this.sysTraceNo = sysTraceNo;
    }

    public String getBatchBillNo() {
        return batchBillNo;
    }

    public void setBatchBillNo(String batchBillNo) {
        this.batchBillNo = batchBillNo;
    }

    public Long getChannelFinishTime() {
        return channelFinishTime;
    }

    public void setChannelFinishTime(Long channelFinishTime) {
        this.channelFinishTime = channelFinishTime;
    }

    public Long getWoSaiMchFavorableAmount() {
        return woSaiMchFavorableAmount;
    }

    public void setWoSaiMchFavorableAmount(Long woSaiMchFavorableAmount) {
        this.woSaiMchFavorableAmount = woSaiMchFavorableAmount;
    }

    public TAccountRecord() {

    }

    public String getTerminalSn() {
        return terminalSn;
    }

    public void setTerminalSn(String terminalSn) {
        this.terminalSn = terminalSn;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public Boolean getIsDepositConsumeRefund() {
        return isDepositConsumeRefund;
    }

    public void setIsDepositConsumeRefund(Boolean isDepositConsumeRefund) {
        this.isDepositConsumeRefund = isDepositConsumeRefund;
    }

    public String getRefernumber() {
        return refernumber;
    }

    public void setRefernumber(String refernumber) {
        this.refernumber = refernumber;
    }
}
