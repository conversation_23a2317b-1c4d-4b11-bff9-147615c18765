package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.annotation.*;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@DBSelectService
@JsonRpcService(value = "rpc/order")
public interface OrderService {

    /**
     * 查询订单列表（兼容backend-upay）
     *
     * @param pageInfo
     * @param queryFilter terminal_id
     *                    terminal_sn
     *                    terminal_name
     *                    store_idTPayWayIcon
     *                    store_sn
     *                    store_name
     *                    merchant_id
     *                    merchant_sn
     *                    merchant_name
     *                    merchant_alias
     *                    seller_id
     *                    seller_path
     *                    tenant_id
     *                    provider
     *                    payway
     *                    sub_payway
     *                    status
     *                    order_sn
     *                    trade_no
     *                    device_fingerprint
     *                    min_total_amount
     *                    max_total_amount
     * @return
     */
    @DBSelectMethod
    ListResult getOrderList(
            @NotNull(message = "分页参数信息不能为空")
                    PageInfo pageInfo,@DBSelectParam(value = "upayQueryType") Map<String, Object> queryFilter);

    /**
     * 查询订单列表（兼容backend-upay）
     * @param pageInfo
     * @param queryFilter
     * @return
     */

    @DBSelectMethod
    ListResult getOrderListV2(
            @NotNull(message = "分页参数信息不能为空")
            PageInfo pageInfo,@DBSelectParam(value = "upayQueryType") Map<String, Object> queryFilter);

    /**
     * 获取订单详情，包含费率，是否二清，终端，门店，商户编号，名称等信息
     *
     * @param merchantId
     * @param orderSn    可为支付通道的订单号
     * @return
     */
    @DBSelectMethod
    Map<String, Object> getOrderDetailsByMerchantIdAndOrderSn(String merchantId,@DBSelectParam String orderSn);

    /**
     * 获取订单原始信息(不包含扩展字段)
     *
     * @param merchantId
     * @param orderSn
     * @return
     */
    @DBSelectMethod
    Map<String, Object> getOrderByMerchantIdAndOrderSn(String merchantId,@DBSelectParam String orderSn);

    /**
     * 订单状态用户信息查询
     * @param param merchant_id 商户id clientSn 可为支付通道的订单号
     * @return
     */
    Map<String, Object> getOrderByMerchantIdAndClientSn(Map<String, Object> param);

    /**
     * 获取订单详情（兼容backend-upay）
     *
     * @param orderSn
     * @return
     */
    @DBSelectMethod
    Map getOrderDetailByOrderSn(@DBSelectParam String orderSn);

}
