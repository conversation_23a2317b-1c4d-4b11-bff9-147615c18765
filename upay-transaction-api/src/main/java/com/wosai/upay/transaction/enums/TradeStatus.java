package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum TradeStatus implements BaseEnum<Integer, String> {

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 失败
     */
    FAIL(404, "失败");

    private int code;

    private String desc;

    private TradeStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
