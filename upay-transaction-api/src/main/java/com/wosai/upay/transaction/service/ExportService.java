package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.model.TTaskInfo;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 导出对账单相关接口
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/export")
public interface ExportService {
    /**
     * 生成对账单导出任务
     *
     * @param taskInfo
     * @param queryFilter
     * @return
     */
    Map createExportStatementTask(Map<String, Object> taskInfo, Map<String, Object> queryFilter);

    /**
     * 提交任务到线程池
     *
     * @param taskLogId
     * @return
     */
    void submitToExecutor(String taskLogId);



    /**
     * 获取 task -> future 的映射关系
     *
     * @return Map
     */
    Map getTaskFutureMap();

    /**
     * 删除future 对象里的记录
     * @param taskLogId
     */
    void deleteTaskInTaskFutureMap(String taskLogId);

    /**
     * 清空监控任务列表
     */
    void clearTaskFutureMap();

    /**
     * 后门接口 - 根据任务id导出订单.
     *
     * @param taskLogId
     * @throws IOException
     */
    void doExportOrder(String taskLogId) throws IOException;

    /**
     * 后门接口 - 根据任务id导出对账单.
     * @param taskLogId
     * @throws Exception
     */
    void doExportStatement(String taskLogId) throws Exception;

    /**
     * 后门接口 - 根据任务id导出集团对账单.
     *
     * @param taskLogId
     * @throws IOException
     */
    void doGroupStatementExportStatement(String taskLogId) throws IOException;




    /**
     * 获取任务执行信息
     * @param isSimple
     * @return
     */
    Collection<TTaskInfo> getTaskInfo(boolean isSimple);

    /**
     * 根据商户id判断是否是KA对账单
     * @param merchantId
     * @return
     */
    boolean isKAStatement(String merchantId);

    /**
     * 根据商户ids判断是否是KA对账单
     * @param groupSn
     * @param merchantIds
     * @return
     */
    boolean isGroupKAStatement(String groupSn, List<String> merchantIds);

}

