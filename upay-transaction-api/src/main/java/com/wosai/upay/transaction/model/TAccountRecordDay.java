package com.wosai.upay.transaction.model;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
public class TAccountRecordDay {

    /**
     * 交易日期
     */
    private String day;

    /**
     * 交易金额(分)
     */
    private long salesAmount;

    /**
     * 成功交易笔数
     */
    private int salesCount;


    /**
     * 储值总笔数
     */
    private long storeInTotalCount;


    /**
     * 储值总金额
     */
    private long storeInTotalAmount;



    public TAccountRecordDay() {
    }


    public long getStoreInTotalCount() {
        return storeInTotalCount;
    }

    public void setStoreInTotalCount(long storeInTotalCount) {
        this.storeInTotalCount = storeInTotalCount;
    }

    public long getStoreInTotalAmount() {
        return storeInTotalAmount;
    }

    public void setStoreInTotalAmount(long storeInTotalAmount) {
        this.storeInTotalAmount = storeInTotalAmount;
    }

    public TAccountRecordDay(long salesAmount, int salesCount,long storeInTotalAmount,long storeInTotalCount) {
        this.salesAmount = salesAmount;
        this.salesCount = salesCount;
        this.storeInTotalAmount = storeInTotalAmount;
        this.storeInTotalCount = storeInTotalCount;
    }

    public TAccountRecordDay(String day, long salesAmount, int salesCount,long storeInTotalAmount,long storeInTotalCount) {
        this(salesAmount, salesCount,storeInTotalAmount,storeInTotalCount);
        this.day = day;
    }

    /**
     * 账本明细
     */
    private List<TAccountRecord> transactions = new ArrayList<>();

    public String getDay() {
        return day;
    }

    public TAccountRecordDay setDay(String day) {
        this.day = day;
        return this;
    }

    public long getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(Long salesAmount) {
        this.salesAmount = salesAmount;
    }

    public int getSalesCount() {
        return salesCount;
    }

    public void setSalesCount(int salesCount) {
        this.salesCount = salesCount;
    }

    public List<TAccountRecord> getTransactions() {
        return transactions;
    }

    public void setTransactions(List<TAccountRecord> transactions) {
        this.transactions = transactions;
    }

    public void setSalesAmount(long salesAmount) {
        this.salesAmount = salesAmount;
    }

    @Override
    public String toString() {
        return "TAccountRecordDay{" +
                "day='" + day + '\'' +
                ", salesAmount=" + salesAmount +
                ", salesCount=" + salesCount +
                ", transactions=" + transactions +
                '}';
    }
}
