package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;
import com.wosai.upay.transaction.model.TAccountRecordDay;
import com.wosai.upay.transaction.model.TAccountRecordDetail;
import com.wosai.upay.transaction.model.TAccountSumV;
import com.wosai.upay.transaction.model.TResultData;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/account_book_open_v4")
public interface IAccountBookServiceOpen {

    /**
     * 账本筛选
     */
    TResultData<List<TAccountRecordDay>> getMoreAccountBookRecords(Map<String, Object> param);


    /**
     * 账本汇总
     */
    TAccountSumV getAccountSum(Map<String, Object> param);


    /**
     * 账本分组汇总
     */
    List<TAccountSumV> getAccountSumGroupBy(Map<String, Object> param);


    /**
     * 流水明细
     */
    TAccountRecordDetail getRecordForDetail(Map<String, Object> param);

}
