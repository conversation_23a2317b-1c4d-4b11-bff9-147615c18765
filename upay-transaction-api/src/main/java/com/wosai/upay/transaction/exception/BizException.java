package com.wosai.upay.transaction.exception;

import com.wosai.upay.common.exception.CommonException;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SuppressWarnings("serial")
public class BizException extends CommonException {
    private int code;

    public static final int CODE_EXCEED_MAX_PAGE_SIZE = 20802;
    public static final int CODE_FORBIDDEN_OVER_PAGE = 20803;
    public static final int CODE_TRANSACTION_QUERY_FAIL = 20804;
    public static final int CODE_LANGUAGE_NOT_SUPPORT = 20901;


    public static final Map<Integer, String> CODES_DESC_MAP = new LinkedHashMap<Integer, String>() {{
        putAll(COMMON_CODES_DESC_MAP); //将常用异常取过来
        
        put(CODE_EXCEED_MAX_PAGE_SIZE, "超过最大查询条数");
        put(CODE_TRANSACTION_QUERY_FAIL, "订单查询失败");
    }};

    public BizException(int code) {
        super(getCodeDesc(code));
        this.code = code;
    }

    public static String getCodeDesc(int code) {
        return CODES_DESC_MAP.get(code);
    }

    public BizException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public BizException(int code, String message) {
        super(message);
        this.code = code;
    }

    public BizException(int code, Throwable cause) {
        super(cause);
        this.code = code;
    }

    public BizException(String message) {
        super(message);
    }

    @Override
    public int getCode() {
        return this.code;
    }
}
