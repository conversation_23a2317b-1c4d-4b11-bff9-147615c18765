package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum UpayQueryType implements BaseEnum<Integer, String> {

    /**
     * UPAY
     */
    UPAY(0, "UPAY"),

    /**
     * UPAY_SWIPE
     */
    UPAY_SWIPE(1, "UPAY_SWIPE"),

    /**
     * UPAY_ALL
     */
    UPAY_ALL(2, "UPAY_ALL");

    private int code;

    private String desc;

    UpayQueryType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
