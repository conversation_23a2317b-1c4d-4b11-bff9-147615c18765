package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.transaction.annotation.*;
import com.wosai.upay.transaction.model.TAccountRecordDay;
import com.wosai.upay.transaction.model.TAccountRecordDetail;
import com.wosai.upay.transaction.model.TAccountSumV;
import com.wosai.upay.transaction.model.TResultData;
import com.wosai.upay.transaction.model.param.AccountRecordParam;
import com.wosai.upay.transaction.model.param.GetTransactionDetailByIdParam;
import com.wosai.upay.transaction.model.param.TransactionDetailParam;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@DBSelectService
@JsonRpcService(value = "rpc/account_book_v4")
public interface IAccountBookService {

    /**
     * 看当日账本
     */
    @DBSelectMethod
    TResultData<List<TAccountRecordDay>> getAccountBookRecords(@DBSelectParam AccountRecordParam param);

    /**
     * 看当日账本 集团与部门用户
     */
    @DBSelectMethod
    TResultData<List<TAccountRecordDay>> getAccountBookRecordsForGroup(@DBSelectParam AccountRecordParam param);

    /**
     * 账本筛选
     */
    @DBSelectMethod
    TResultData<List<TAccountRecordDay>> getMoreAccountBookRecords(@DBSelectParam AccountRecordParam param);

    /**
     * 账本筛选,各业务方使用,跟app账本业务区分开便于限流
     */
    @DBSelectMethod
    TResultData<List<TAccountRecordDay>> getMoreAccountBookRecordsWithSource(AccountRecordParam param, String source);

    /**
     * 账本筛选
     */
    @DBSelectMethod
    TResultData<List<TAccountRecordDay>> getMoreAccountBookRecordsForGroup(@DBSelectParam AccountRecordParam param);


    /**
     * 账本汇总
     */
    @DBSelectMethod
    TAccountSumV getAccountSum(@DBSelectParam AccountRecordParam param);

    /**
     * 账本汇总,各业务方使用,跟app账本业务区分开便于限流
     */
    @DBSelectMethod
    TAccountSumV getAccountSumWithSource(AccountRecordParam param, String source);

    /**
     * 分组汇总账本汇总
     */
    @DBSelectMethod
    List<TAccountSumV> getAccountSumGroupBy(@DBSelectParam AccountRecordParam param);

    /**
     * 流水明细
     */
    @DBSelectMethod
    TAccountRecordDetail getRecordForDetail(@NotEmpty(message = "流水单号不能为空") @DBSelectParam String transactionSn, @NotEmpty(message = "商户id不能为空") String merchantId);

    /**
     * 流水明细查询，仅可查询 2018-01-26 之后的交易，比如用于点金计划中微信小票详情展示
     */
    @Deprecated
    @DBSelectMethod
    TAccountRecordDetail getTransactionDetail(@NotEmpty(message = "商户id不能为空") String merchantId,
                                              @NotNull(message = "ctime 不能为空") Long ctime,
                                              @NotEmpty(message = "流水单号不能为空") @DBSelectParam String transactionSn);

    /**
     * 流水明细查询，仅可查询 2018-01-26 之后的交易，比如用于点金计划中微信小票详情展示
     *
     * @param merchantId     商户id
     * @param ctime          交易创建时间戳
     * @param transactionSn  交易sn
     * @param needRefundInfo 对于正向收款交易，是否需要查询有关退款信息（不限于退款列表，比如分账退款等）
     *                       true 表示是，false表示否，默认为 true
     * @return TAccountRecordDetail
     */
    @DBSelectMethod
    TAccountRecordDetail getTransactionDetail(@NotEmpty(message = "商户id不能为空") String merchantId,
                                              @NotNull(message = "ctime 不能为空") Long ctime,
                                              @NotEmpty(message = "流水单号不能为空") @DBSelectParam String transactionSn,
                                              Boolean needRefundInfo);

    /**
     * 流水明细
     * <br/>
     * （相较于 {@link #getRecordForDetail} 是第二版 api）by id
     */
    @DBSelectMethod
    TAccountRecordDetail getTransactionDetailById(@Valid @DBSelectParam @NotNull(message = "查询参数不能为空") GetTransactionDetailByIdParam param);

    /**
     * 流水明细
     * <br/>
     * （相较于 {@link #getTransactionDetailById} 是第三版 api）只根据tsn 或 tradeNo 查询 （会很耗时哟）
     */
    TAccountRecordDetail getTransactionDetailByTsnOrTradeNo(TransactionDetailParam param);

    /**
     * 预授权最新明细
     *
     * @param transactionSn
     * @param merchantId
     * @return
     */
    @DBSelectMethod
    TAccountRecordDetail getTransactionDetailByDepositTsn(@NotEmpty(message = "流水单号不能为空") @DBSelectParam String transactionSn,
                                                          @NotEmpty(message = "商户id不能为空") String merchantId);
}
