package com.wosai.upay.transaction.model;

/**
 * <AUTHOR>
 *
 */
public class TAccountSumV {

    /**
     * 交易数量
     */
    protected Integer salesCount = 0;

    /**
     * 交易金额
     */
    protected Long salesAmount = 0L;

    /**
     * 收款笔数
     */
    protected Integer paidCount = 0;

    /**
     * 收款金额
     */
    protected Long paidAmount = 0L;

    /**
     * 退款笔数
     */
    protected Integer refundedCount = 0;

    /**
     * 退款金额
     */
    protected Long refundedAmount = 0L;

    /**
     * 撤销笔数
     */

    protected Integer canceldCount = 0;

    /**
     * 撤销金额
     */
    protected Long canceldAmount = 0L;


    /**
     * 预授权完成笔数
     */
    protected Integer depositCount = 0;

    /**
     * 预授权完成金额
     */
    protected Long depositAmount = 0L;


    /**
     * 预授权完成撤销金额
     */
    protected Long depositCancelAmount = 0L;


    /**
     * 预授权完成撤销笔数
     */
    protected Integer depositCancelCount = 0;

    /**
     * 储值交易笔数（储值充值，储值退款）
     */
    protected Long storeInTotalCount = 0L;

    /**
     * 储值交易金额（储值充值，储值退款）
     */
    protected Long storeInTotalAmount = 0L;

    /**
     * 储值充值笔数
     */
    protected Long storeInCount = 0L;

    /**
     * 储值充值金额
     */
    protected Long storeInAmount = 0L;

    /**
     * 储值退款笔数
     */
    protected Long storeInRefundCount = 0L;

    /**
     * 储值退款金额
     */
    protected Long storeInRefundAmount = 0L;

    /**
     * 预授权数量
     */
    protected Integer depositFreezeCount = 0;

    /**
     * 预授权金额
     */
    protected Long depositFreezeAmount = 0L;

    /**
     * 预授权撤销金额
     */
    protected Long depositFreezeCanceledAmount = 0L;

    /**
     * 预授权撤销笔数
     */
    protected Integer depositFreezeCanceledCount = 0;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 支付源
     */
    private Integer payWay;


    public Integer getSalesCount() {
        return salesCount;
    }

    public void setSalesCount(Integer salesCount) {
        this.salesCount = salesCount;
    }

    public Long getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(Long salesAmount) {
        this.salesAmount = salesAmount;
    }

    public Integer getPaidCount() {
        return paidCount;
    }

    public void setPaidCount(Integer paidCount) {
        this.paidCount = paidCount;
    }

    public Long getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Long paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Integer getRefundedCount() {
        return refundedCount;
    }

    public void setRefundedCount(Integer refundedCount) {
        this.refundedCount = refundedCount;
    }

    public Long getRefundedAmount() {
        return refundedAmount;
    }

    public void setRefundedAmount(Long refundedAmount) {
        this.refundedAmount = refundedAmount;
    }

    public Integer getCanceldCount() {
        return canceldCount;
    }

    public void setCanceldCount(Integer canceldCount) {
        this.canceldCount = canceldCount;
    }

    public Long getCanceldAmount() {
        return canceldAmount;
    }

    public void setCanceldAmount(Long canceldAmount) {
        this.canceldAmount = canceldAmount;
    }


    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public Integer getPayWay() {
        return payWay;
    }

    public void setPayWay(Integer payWay) {
        this.payWay = payWay;
    }

    public Integer getDepositCount() {
        return depositCount;
    }

    public void setDepositCount(Integer depositCount) {
        this.depositCount = depositCount;
    }

    public Long getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(Long depositAmount) {
        this.depositAmount = depositAmount;
    }


    public Long getDepositCancelAmount() {
        return depositCancelAmount;
    }

    public void setDepositCancelAmount(Long depositCancelAmount) {
        this.depositCancelAmount = depositCancelAmount;
    }

    public Integer getDepositCancelCount() {
        return depositCancelCount;
    }

    public void setDepositCancelCount(Integer depositCancelCount) {
        this.depositCancelCount = depositCancelCount;
    }

    public Long getStoreInTotalCount() {
        return storeInTotalCount;
    }

    public void setStoreInTotalCount(Long storeInTotalCount) {
        this.storeInTotalCount = storeInTotalCount;
    }

    public Long getStoreInTotalAmount() {
        return storeInTotalAmount;
    }

    public void setStoreInTotalAmount(Long storeInTotalAmount) {
        this.storeInTotalAmount = storeInTotalAmount;
    }

    public Long getStoreInCount() {
        return storeInCount;
    }

    public void setStoreInCount(Long storeInCount) {
        this.storeInCount = storeInCount;
    }

    public Long getStoreInAmount() {
        return storeInAmount;
    }

    public void setStoreInAmount(Long storeInAmount) {
        this.storeInAmount = storeInAmount;
    }

    public Long getStoreInRefundCount() {
        return storeInRefundCount;
    }

    public void setStoreInRefundCount(Long storeInRefundCount) {
        this.storeInRefundCount = storeInRefundCount;
    }

    public Long getStoreInRefundAmount() {
        return storeInRefundAmount;
    }

    public void setStoreInRefundAmount(Long storeInRefundAmount) {
        this.storeInRefundAmount = storeInRefundAmount;
    }

    public Integer getDepositFreezeCount() {
        return depositFreezeCount;
    }

    public void setDepositFreezeCount(Integer depositFreezeCount) {
        this.depositFreezeCount = depositFreezeCount;
    }

    public Long getDepositFreezeAmount() {
        return depositFreezeAmount;
    }

    public void setDepositFreezeAmount(Long depositFreezeAmount) {
        this.depositFreezeAmount = depositFreezeAmount;
    }

    public Long getDepositFreezeCanceledAmount() {
        return depositFreezeCanceledAmount;
    }

    public void setDepositFreezeCanceledAmount(Long depositFreezeCanceledAmount) {
        this.depositFreezeCanceledAmount = depositFreezeCanceledAmount;
    }

    public Integer getDepositFreezeCanceledCount() {
        return depositFreezeCanceledCount;
    }

    public void setDepositFreezeCanceledCount(Integer depositFreezeCanceledCount) {
        this.depositFreezeCanceledCount = depositFreezeCanceledCount;
    }
}
