package com.wosai.upay.transaction.model;

import com.wosai.upay.common.util.ConstantUtil;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class StatementObjectConfig {

    public static final String ID = ConstantUtil.KEY_ID;

    public static final String MERCHANT_ID = ConstantUtil.KEY_MERCHANT_ID;

    public static final String MERCHANT_IDS = "merchant_ids";
    public static final String RECEIVER_MERCHANT_SNS = "receiver_merchant_sns";


    public static final String OBJECT_ID = "object_id";
    public static final String LANGUAGE = "language";
    public static final String SPLIT_TYPE = "split_type";
    public static final String SHEET_TYPE = "sheet_type";
    public static final String TERMINAL_TYPE = "terminal_type";
    public static final String SHEET_PARAMS = "sheet_params";
    public static final String SHEET_DETAIL_TYPE = "sheet_detail_type";
    public static final String SHEET_DETAIL_PARAMS = "sheet_detail_params";

    public static final String CRO = "cro";
    public static final String PAYWAY = "payway";

    /**
     * 部门
     */
    public static final String DEPARTMENT_IDS = "department_ids";
    /**
     * 管理员
     */
    public static final String MERCHANT_USER_ID = "merchant_user_id";


    /**
     * 交易笔数
     */
    public static final String TRANSACTION_NO = "transaction_no";
    /**
     * 交易金额
     */
    public static final String TRANSACTION_AMOUNT = "transaction_amount";
    /**
     * 退款笔数
     */
    public static final String REFUND_NO = "refund_no";
    /**
     * 退款金额
     */
    public static final String REFUND_AMOUNT = "refund_amount";
    /**
     * 收款净额
     */
    public static final String TRANSACTION_NET_AMOUNT = "transaction_net_amount";
    /**
     * /商户优惠
     */
    public static final String MERCHANT_DISCOUNT = "merchant_discount";
    /**
     * 收钱吧优惠
     */
    public static final String WOSAI_DISCOUNT = "wosai_discount";
    /**
     * 收款通道机构优惠
     */
    public static final String PAYMENT_TYPE_DISCOUNT = "payment_type_discount";
    /**
     * 收款通道商户预充值优惠
     */
    public static final String MERCHANT_DISCOUNT_PREPAID_DISCOUNT = "merchant_discount_prepaid_mode";
    /**
     * 收款通道商户免充值优惠
     */
    public static final String MERCHANT_DISCOUNT_NON_PREPAID_DISCOUNT = "merchant_discount_non_prepaid_mode";
    /**
     * 实收金额
     */
    public static final String PAID_AMOUNT = "paid_amount";
    /**
     * 手续费
     */
    public static final String CHANRGE = "charge";
    /**
     * 结算金额
     */
    public static final String SETTLEMENT_AMOUNT = "settlement_amount";
    /**
     * 成交到账金额
     */
    public static final String DEAL_TO_ACCOUNT_AMOUNT = "deal_to_account_amount";
    /**
     * 分账金额
     */
    public static final String SHARING_AMOUNT = "sharing_amount";

    /**
     * 商户自主分账金额
     */
    public static final String MERCHANT_SHARING_AMOUNT = "merchant_sharing_amount";

    /**
     * 技术服务费
     */
    public static final String TRADE_SERVICE_SHARING_AMOUNT = "trade_service_sharing_amount";

    /**
     * 跨主体退货笔数
     */
    public static final String CROSS_MERCHANT_REFUND_NO = "cross_merchant_refund_no";
    /**
     * 跨主体退货金额
     */
    public static final String CROSS_MERCHANT_REFUND_AMOUNT = "cross_merchant_refund_amount";


    /**
     * 支付宝1.0
     */
    public static final int ALIPAY_OLD = 1;
    /**
     * 支付宝2.0
     */
    public static final int ALIPAY_NEW = 2;
    public static final int WEIXIN = 3;
    /**
     * 百度钱包
     */
    public static final int BAIPAY = 4;
    /**
     * 京东钱包
     */
    public static final int JDPAY = 5;
    /**
     * qq钱包
     */
    public static final int QQPAY = 6;
    /**
     * NFC支付
     */
    public static final int NFCPAY = 7;
    /**
     * 拉卡拉钱包
     */
    public static final int LKLPAY = 8;
    /**
     * 和包支付
     */
    public static final int CMCCPAY = 9;
    /**
     * 拉卡拉微信
     */
    public static final int LKL_WEIXIN = 15;
    /**
     * 招商银行
     */
    public static final int ZSYHPAY = 16;
    /**
     * 银联云闪付
     */
    public static final int YLCODEPAY = 17;
    /**
     * 翼支付
     */
    public static final int YIPAY = 18;
    /**
     * 微信支付-香港
     */
    public static final int WEIXIN_HK = 19;
    /**
     * 支付宝国际版
     */
    public static final int ALIPAY_INTL = 20;
    /**
     * 银行卡
     */
    public static final int BANK_CARD = 21;

    /**
     * 数字人民币
     */
    public static final int PAYWAY_DCEP = 23;

    /**
     * 支付宝-香港跨境
     */
    public static final int ALIPAY_HK = 99;
    /**
     * 支付宝-香港本地
     */
    public static final int ALIPAY_HK_LOCAL = 98;
    /**
     * 储值卡（原名称："礼品卡"，原储值卡 100 弃用并合并至 101）
     */
    public static final int GIFT_CARD = 101;
    /**
     * 现金
     */
    public static final int CASH = 102;
    /**
     * 饿了么
     */
    public static final int ELE = 103;
    /**
     * 美团
     */
    public static final int MEI_TUAN = 104;
    /**
     * 储值(核销,核销退款)
     */
    public static final int STORE = 105;

    /**
     * 储值（充值，退款）
     */
    public static final int STORE_FOR_GATEWAY = 105;

    /**
     * 大众点评
     */
    public static final int DIAN_PING = 107;
    /**
     * 支付宝记账
     */
    public static final int ALI_CHG = 302;
    /**
     * 微信记账
     */
    public static final int WX_CHG = 303;
    /**
     * 抖音券记账
     */
    public static final int DY_QUAN = 112;

    /**
     * 福利卡
     */
    public static final int WELFARE_CARD = 111;
    /**
     * 其他记账方式
     */
    public static final int OTH_CHG = 108;

    public static final List<Integer> NOT_GATEWAY_DEF_PAYWAY_LIST =
            Arrays.asList(ALIPAY_HK, ALIPAY_HK_LOCAL, CASH, ELE, MEI_TUAN, STORE);

    /**币种相关常量*/
    /**
     * 支付宝香港-境外
     */
    public static final String ALIPAYCN = "ALIPAYCN";
    /**
     * 支付宝香港-本地
     */
    public static final String ALIPAYHK = "ALIPAYHK";
    /**
     * 人民币
     */
    public static final String CURRENCY_CNY = "CNY";
    /**
     * 港币
     */
    public static final String CURRENCY_HKD = "HKD";

}
