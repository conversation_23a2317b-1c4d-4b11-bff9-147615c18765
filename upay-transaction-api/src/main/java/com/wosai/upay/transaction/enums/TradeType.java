package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum TradeType implements BaseEnum<Integer, String> {

    /**
     * 收款成功
     */
    PAID(1, "收款成功"),

    /**
     * 退款
     */
    REFUNDED(2, "退款"),

    /**
     * 收款失败
     */
    PAY_ERROR(404, "收款失败"),

    //------------------------预授权

    /**
     * 预授权
     */
    DEPOSIT_FREEZE(32, "预授权"),

    /**
     * 预授权撤销
     */
    DEPOSIT_CANCEL(12, "预授权撤销"),

    /**
     * 预授权完成
     */
    DEPOSIT_CONSUME(13, "预授权完成"),

    /**
     * 预授权完成撤销交易
     */
    DEPOSIT_CONSUME_CANCEL(14, "预授权完成撤销交易");





    private int code;

    private String desc;

    private TradeType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
