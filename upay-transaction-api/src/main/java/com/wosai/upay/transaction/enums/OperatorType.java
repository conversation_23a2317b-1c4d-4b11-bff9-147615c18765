package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum OperatorType implements BaseEnum<Integer, String> {

    /**
     * 终端
     */
    TERMINAL(1, "终端"),

    /**
     * 收款码
     */
    QR_CODE(2, "收款码"),

    /**
     * 店员
     */
    CASHIER(3, "店员");



    private int code;

    private String desc;

    private OperatorType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
