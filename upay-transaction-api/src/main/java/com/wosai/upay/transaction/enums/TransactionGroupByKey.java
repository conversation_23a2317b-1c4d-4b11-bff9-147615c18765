package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum TransactionGroupByKey implements BaseEnum<String, String> {

    /**
     * 支付源
     */
    payWay("payway", "支付源");

    private String code;

    private String desc;

    private TransactionGroupByKey(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getDesc() {
        return desc;
    }





}
