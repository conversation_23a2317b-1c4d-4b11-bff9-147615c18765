package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.transaction.annotation.*;
import java.util.List;
import java.util.Map;


/**
 * Created by kay on 2017/7/10.
 */

@CommonTransactionValidated
@CommonTransactionService
@DBSelectService
@JsonRpcService(value = "rpc/transaction")
public interface TransactionService {
    /**
     * 通过订单号获取交易流水详情（兼容backend-upay）
     *
     * @param orderSn
     * @return
     */
    @DBSelectMethod
    List getTransactionListByOrderSn(@NotEmpty(message = "订单号order_sn不能为空") @DBSelectParam String orderSn);

    /**
     * 获取商户的交易流水
     *
     * @param merchantId
     * @param storeId
     * @param terminalId
     * @param pageInfo
     * @param queryFilter status
     *                    seller_id
     *                    seller_path
     * @return
     */
    @DBSelectMethod
    List<Map<String, Object>> getMerchantTransactionList(
//            @NotEmpty(message = "商户id不能为空")
            String merchantId, String merchantUserId, List<String> departmentIds, String storeId, String terminalId, PageInfo pageInfo,@DBSelectParam(value = "upayQueryType") Map<String, Object> queryFilter);

    /**
     * 获取商户的交易流水总数
     *
     * @param merchantId
     * @param storeId
     * @param terminalId
     * @param pageInfo
     * @param queryFilter status
     *                    seller_id
     *                    seller_path
     * @return
     */
    @DBSelectMethod
    long getMerchantTransactionCount(
            String merchantId, String accountId, List<String> departmentIds,String storeId, String terminalId, PageInfo pageInfo,@DBSelectParam(value = "upayQueryType") Map<String, Object> queryFilter);
}

