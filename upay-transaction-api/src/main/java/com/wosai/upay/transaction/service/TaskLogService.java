package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.annotation.CommonTransactionService;
import com.wosai.upay.transaction.annotation.CommonTransactionValidated;

import java.util.List;
import java.util.Map;

/**
 * 对账单导出日志相关接口
 * <AUTHOR>
 */
@CommonTransactionValidated
@CommonTransactionService
@JsonRpcService(value = "rpc/statementTask")
public interface TaskLogService {

    long HEARTBEAT_MONITOR_INTERVAL_TIME = 30 * 1000L;

    /**
     * 创建任务申请日志.
     *
     * @param taskApplyLog
     */
    Map createTaskApplyLog(Map taskApplyLog);

    /**
     * 根据taskApplyLogId删除任务申请日志.
     *
     * @param taskApplyLogId
     */
    void deleteTaskApplyLog(String taskApplyLogId);

    /**
     * 修改任务申请日志.
     * @param taskApplyLog
     * @return
     */
    Map updateTaskApplyLog(Map taskApplyLog);

    /**
     * 修改任务日志状态.
     *
     * @param taskApplyLogId
     * @param status
     */
    void updateTaskApplyLogStatus(String taskApplyLogId, int status);

    /**
     * 根据taskApplyLogId获取任务申请日志.
     *
     * @param taskApplyLogId
     * @return
     */
    Map getTaskApplyLog(String taskApplyLogId);

    /**
     * 分页查询任务申请日志.
     *
     * @param pageInfo
     * @param queryFilter type                申请任务类型，1：对账单下载，2：订单下载，3：渠道分润报表下载
     *                    apply_system        请求系统，1:未知; 2:OSP; 3:服务商; 4:商户服务; 5:推广者服务 9:其他内部服务
     *                    user_id             用户id
     *                    apply_status        任务申请状态，0：新申请，1：执行中，2：执行成功，3：执行失败
     *                    apply_date          申请日期
     *                    deleted
     * @return
     */
    ListResult findTaskApplyLogs(PageInfo pageInfo, Map queryFilter);

    /**
     * 查询是否有相同查询条件的成功任务，并设置任务结果
     *
     * @param taskLogId
     * @return
     */
    Boolean getAndSetResultIfSameSuccSignature(String taskLogId);


    /**
     *  查询是否有相同查询条件的结果路径url
     * @param taskLogId
     * @return
     */
    String getSameTaskFileUrl(String taskLogId);


    /**
     * 批量更新 task 的 mtime
     * @param taskLogIds
     * @param mtime
     */
    void updateTasksMtime(List<String> taskLogIds, Long mtime);

    /**
     * 获取心跳停止的任务
     * @param start
     * @return
     */
    List getHeartbeatStopTaskList(long start);
}
