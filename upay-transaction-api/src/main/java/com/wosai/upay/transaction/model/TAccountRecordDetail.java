package com.wosai.upay.transaction.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class TAccountRecordDetail {

    /**
     * 支付源
     */
    private Integer payWay;

    /**
     * 门店sn
     */
    private String storeSn;

    /**
     * 流水状态，1：成功；404：失败
     */
    private Integer status;

    private String failText;

    /**
     * 流水类型， 30：支付；11：退款；10：撤单
     */
    private Integer type;

    /**
     * 交易时间
     */
    private Long ctime;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 流水交易终端或人员名称
     */
    private String operatorName;

    /**
     * 操作人
     */
    private String operatorOrigin;

    /**
     * 终端类型
     */
    private String terminalType;

    /**
     * 终端id
     */
    private String terminalId;

    /**
     * 流水交易金额
     */
    private Long totalFee;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 商户红包优惠金额
     */
    private Long hongBaoWosaiMchTotal;

    /**
     * 商户立减优惠金额
     */
    private Long discountWosaiMchTotal;

    /**
     * 商户立减优惠类型
     */
    private String discountWoSaiMchType;


    /**
     * 收钱吧优惠金额
     */
    private Long wosaiFavorableAmount;

    /**
     * 收款通道商户免充值优惠
     */
    private Long channelMchFavorableAmount;

    /**
     * 有收款通道承担的优惠金额
     */
    private Long channelAgentFavorableAmount;


    /**
     * 收款通道商户充值优惠
     */
    private Long channelMchTopUpFavorableAmount;


    private String payId;

    /**
     * 实收金额
     */
    private Long actualReceiveAmount;

    /**
     * 订单sn
     */
    private String orderSn;

    /**
     * 银行单号
     */
    private String bankTradeNo;

    /**
     * 收单机构单号
     */
    private String providerTradeNo;

    /**
     * 支付源单号
     */
    private String payWayTradeNo;

    /**
     * 流水单号
     */
    private String transactionSn;

    /**
     * 清算金额
     */
    private Long clearingAmount;

    /**
     * 交易费率
     */
    private Double tradeFeeRate;

    private String tradeMemo;

    /**
     * type 流水类型， 30：支付；11：退款；10：撤单
     * refundFee  退款金额
     * transactionId  退款单id
     * transactionSn  退款单号
     * originalTransactionSn	原收款流水sn
     * ctime	创建时间毫秒时间戳
     */
    private List<Map> refundList;

    /**
     * 原始交易流水id
     */
    private String originalTransactionId;

    /**
     * 原始交易流水号
     */
    private String originalTransactionSn;

    /**
     * 原始交易流水ctime
     * <br/>
     * 查询详情用
     */
    private Long originalTransactionCtime;

    /**
     * 交易勾兑状态
     *
     * @see com.wosai.upay.transaction.constant.BlendingStatusConst
     */
    private Integer blendingStatus;

    /**
     * 交易手续费
     */
    private Long tradeFee;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 付款人
     */
    private String buyerAccount;

    /**
     * 付款人 id
     */
    private String buyerUid;

    /**
     * 付款人名称
     */
    private String buyerName;

    /**
     * 付款人头像
     */
    private String buyerIcon;

    /**
     * 是否直连
     */
    private Boolean direct;

    /**
     * 判断是否口碑商户 非空为口碑商户
     */
    private String appAuthShopId;

    /**
     * 原交易手续费
     */
    private Long tradeFeeOriginal;


    /**
     * 原商户有授权订单号
     */
    private String tradeNoOriginalDeposit;

    /**
     * 交易费率
     */
    private Double feeRateOriginal;

    /**
     * 花呗分期标示
     */
    private Boolean isInstalment = false;

    /**
     * 分期商家费用
     */
    private Long instalmentMerchantCharge = 0L;


    /**
     * 消费者实际支付金额
     */
    private Long paidAmount;

    /**
     * 向支付通道请求的金额
     */
    private Long effectiveAmount;

    /**
     * 终端sn
     */
    private String terminalSn;

    /**
     * 终端名称
     */
    private String terminalName;

    /**
     * 是否预授权完成退款
     */
    private Boolean isDepositConsumeRefund;

    /**
     * 系统参考号
     */
    private String refernumber;

    /**
     * 授权码
     */
    private String authNo;

    /**
     * 凭证号
     */
    private String sysTraceNo;

    /**
     * 批次号
     */
    private String batchBillNo;

    /**
     * 交易完成时间（从支付通道得到）
     */
    private Long channelFinishTime;


    /**
     * 流水类别
     * {@link com.wosai.upay.transaction.enums.TransactionCategory}
     */
    private Integer category;

    /**
     * 账本列表展示（收款，会员储值，美团外卖 等等等）
     */
    private String tag;

    /**
     * 可退交易金额
     */
    private Long netRefundAmount;

    /**
     * 可退实收金额
     */
    private Long netReceivedRefundAmount;

    /**
     * 是否全额退款
     */
    private boolean allRefund;

    private Integer sharingFlag;

    private Long sharingAmount;


    /**
     * 商户自助发红包优惠
     */
    private Long outerDiscount;

    /**
     * 图标
     */
    private String discountIcon;

    /**
     * 商户优惠类型
     */
    private String mchDiscountOriginType;

    /**
     * 商户优惠金额
     */
    private long woSaiMchFavorableAmount;

    private List<Map<String,Object>> woSaiMchFavorableAmountList;

    /**
     * 产品标签（比如：九九折、蚂蚁花呗、电饱饱等）
     */
    private String productFlag;

    /**
     * 产品/付款类型的图标（比如：电饱饱的icon）（首次使用在分账交易中）
     */
    private String productIcon;

    /**
     * 分账列表
     */
    private List<SharingBook> sharingBookList;

    /**
     * 分账退回列表
     */
    private List<SharingBook> sharingBookRefundList;
    /**
     * 是否使用额度包
     */
    private boolean useQuota = false;

    /**
     * 收银台id
     */
    private String cashDeskId;

    /**
     * 收银台名称
     */
    private String cashDeskName;

    /**
     * 收银台名称
     */
    private String cashierName;

    /**
     * 微信sub_appid，当payway=3时才有值
     */
    private String subAppid;


    /**
     * 间连收单机构商户号
     */
    private String providerMchId;

    /**
     * 订单创建时间
     */
    private Long orderCtime;
    /**
     * 订单原始金额
     */
    private Long orderOriginalAmount;
    /**
     * 订单向支付通道请求的支付总金额
     */
    private Long orderEffectiveAmount;

    //自定义记账方式名称
    private String chargeSource;


    private String tradeApp;

    public String getTradeApp() {
        return tradeApp;
    }

    public void setTradeApp(String tradeApp) {
        this.tradeApp = tradeApp;
    }

    public String getChargeSource() {
        return chargeSource;
    }

    public void setChargeSource(String chargeSource) {
        this.chargeSource = chargeSource;
    }

    public String getSubAppid() {
        return subAppid;
    }

    public void setSubAppid(String subAppid) {
        this.subAppid = subAppid;
    }

    /**
     * 分账详情（分账退回详情）
     */
    @Data
    public static class SharingBook {
        private Integer type;
        private String alias;
        private Long amount;
        private Long ctime;
        private Boolean showRatioHelp;
    }

    private String refundFlag;

    public String getRefundFlag() {
        return refundFlag;
    }

    public void setRefundFlag(String refundFlag) {
        this.refundFlag = refundFlag;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public List<SharingBook> getSharingBookList() {
        return sharingBookList;
    }

    public void setSharingBookList(List<SharingBook> sharingBookList) {
        this.sharingBookList = sharingBookList;
    }

    public List<SharingBook> getSharingBookRefundList() {
        return sharingBookRefundList;
    }

    public void setSharingBookRefundList(List<SharingBook> sharingBookRefundList) {
        this.sharingBookRefundList = sharingBookRefundList;
    }

    public String getProductFlag() {
        return productFlag;
    }

    public void setProductFlag(String productFlag) {
        this.productFlag = productFlag;
    }

    public String getProductIcon() {
        return productIcon;
    }

    public void setProductIcon(String productIcon) {
        this.productIcon = productIcon;
    }

    public Boolean getInstalment() {
        return isInstalment;
    }

    public void setInstalment(Boolean instalment) {
        isInstalment = instalment;
    }

    public Long getInstalmentMerchantCharge() {
        return instalmentMerchantCharge;
    }

    public void setInstalmentMerchantCharge(Long instalmentMerchantCharge) {
        this.instalmentMerchantCharge = instalmentMerchantCharge;
    }

    public Integer getPayWay() {
        return payWay;
    }

    public void setPayWay(Integer payWay) {
        this.payWay = payWay;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getFailText() {
        return failText;
    }

    public void setFailText(String failText) {
        this.failText = failText;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }


    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType;
    }

    public Long getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(Long totalFee) {
        this.totalFee = totalFee;
    }

    public Long getHongBaoWosaiMchTotal() {
        return hongBaoWosaiMchTotal;
    }

    public void setHongBaoWosaiMchTotal(Long hongBaoWosaiMchTotal) {
        this.hongBaoWosaiMchTotal = hongBaoWosaiMchTotal;
    }

    public Long getDiscountWosaiMchTotal() {
        return discountWosaiMchTotal;
    }

    public void setDiscountWosaiMchTotal(Long discountWosaiMchTotal) {
        this.discountWosaiMchTotal = discountWosaiMchTotal;
    }

    public Long getWosaiFavorableAmount() {
        return wosaiFavorableAmount;
    }

    public void setWosaiFavorableAmount(Long wosaiFavorableAmount) {
        this.wosaiFavorableAmount = wosaiFavorableAmount;
    }

    public Long getChannelAgentFavorableAmount() {
        return channelAgentFavorableAmount;
    }

    public void setChannelAgentFavorableAmount(Long channelAgentFavorableAmount) {
        this.channelAgentFavorableAmount = channelAgentFavorableAmount;
    }

    public Long getActualReceiveAmount() {
        return actualReceiveAmount;
    }

    public void setActualReceiveAmount(Long actualReceiveAmount) {
        this.actualReceiveAmount = actualReceiveAmount;
    }


    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getBankTradeNo() {
        return bankTradeNo;
    }

    public void setBankTradeNo(String bankTradeNo) {
        this.bankTradeNo = bankTradeNo;
    }

    public String getProviderTradeNo() {
        return providerTradeNo;
    }

    public void setProviderTradeNo(String providerTradeNo) {
        this.providerTradeNo = providerTradeNo;
    }

    public String getPayWayTradeNo() {
        return payWayTradeNo;
    }

    public void setPayWayTradeNo(String payWayTradeNo) {
        this.payWayTradeNo = payWayTradeNo;
    }

    public String getTransactionSn() {
        return transactionSn;
    }

    public void setTransactionSn(String transactionSn) {
        this.transactionSn = transactionSn;
    }

    public Long getClearingAmount() {
        return clearingAmount;
    }

    public void setClearingAmount(Long clearingAmount) {
        this.clearingAmount = clearingAmount;
    }

    public Double getTradeFeeRate() {
        return tradeFeeRate;
    }

    public void setTradeFeeRate(Double tradeFeeRate) {
        this.tradeFeeRate = tradeFeeRate;
    }

    public Integer getBlendingStatus() {
        return blendingStatus;
    }

    public void setBlendingStatus(Integer blendingStatus) {
        this.blendingStatus = blendingStatus;
    }

    public String getTradeMemo() {
        return tradeMemo;
    }

    public void setTradeMemo(String tradeMemo) {
        this.tradeMemo = tradeMemo;
    }

    public Long getNetRefundAmount() {
        return netRefundAmount;
    }

    public void setNetRefundAmount(Long netRefundAmount) {
        this.netRefundAmount = netRefundAmount;
    }

    public Long getNetReceivedRefundAmount() {
        return netReceivedRefundAmount;
    }

    public void setNetReceivedRefundAmount(Long netReceivedRefundAmount) {
        this.netReceivedRefundAmount = netReceivedRefundAmount;
    }

    public List<Map> getRefundList() {
        return refundList;
    }

    public void setRefundList(List<Map> refundList) {
        this.refundList = refundList;
    }

    public String getOriginalTransactionId() {
        return originalTransactionId;
    }

    public void setOriginalTransactionId(String originalTransactionId) {
        this.originalTransactionId = originalTransactionId;
    }

    public String getOriginalTransactionSn() {
        return originalTransactionSn;
    }

    public void setOriginalTransactionSn(String originalTransactionSn) {
        this.originalTransactionSn = originalTransactionSn;
    }

    public Long getOriginalTransactionCtime() {
        return originalTransactionCtime;
    }

    public void setOriginalTransactionCtime(Long originalTransactionCtime) {
        this.originalTransactionCtime = originalTransactionCtime;
    }

    public Long getTradeFee() {
        return tradeFee;
    }

    public void setTradeFee(Long tradeFee) {
        this.tradeFee = tradeFee;
    }


    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Long getChannelMchFavorableAmount() {
        return channelMchFavorableAmount;
    }

    public void setChannelMchFavorableAmount(Long channelMchFavorableAmount) {
        this.channelMchFavorableAmount = channelMchFavorableAmount;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getBuyerAccount() {
        return buyerAccount;
    }

    public void setBuyerAccount(String buyerAccount) {
        this.buyerAccount = buyerAccount;
    }

    public void setBuyerUid(String buyerUid) {
        this.buyerUid = buyerUid;
    }

    public String getBuyerUid() {
        return buyerUid;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerIcon(String buyerIcon) {
        this.buyerIcon = buyerIcon;
    }

    public String getBuyerIcon() {
        return buyerIcon;
    }

    public Boolean getDirect() {
        return direct;
    }

    public void setDirect(Boolean direct) {
        this.direct = direct;
    }

    public String getAppAuthShopId() {
        return appAuthShopId;
    }

    public void setAppAuthShopId(String appAuthShopId) {
        this.appAuthShopId = appAuthShopId;
    }

    public String getStoreSn() {
        return storeSn;
    }

    public void setStoreSn(String storeSn) {
        this.storeSn = storeSn;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public Long getTradeFeeOriginal() {
        return tradeFeeOriginal;
    }

    public void setTradeFeeOriginal(Long tradeFeeOriginal) {
        this.tradeFeeOriginal = tradeFeeOriginal;
    }

    public Double getFeeRateOriginal() {
        return feeRateOriginal;
    }

    public void setFeeRateOriginal(Double feeRateOriginal) {
        this.feeRateOriginal = feeRateOriginal;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public Long getChannelMchTopUpFavorableAmount() {
        return channelMchTopUpFavorableAmount;
    }

    public void setChannelMchTopUpFavorableAmount(Long channelMchTopUpFavorableAmount) {
        this.channelMchTopUpFavorableAmount = channelMchTopUpFavorableAmount;
    }

    public String getTradeNoOriginalDeposit() {
        return tradeNoOriginalDeposit;
    }

    public void setTradeNoOriginalDeposit(String tradeNoOriginalDeposit) {
        this.tradeNoOriginalDeposit = tradeNoOriginalDeposit;
    }

    public String getDiscountWoSaiMchType() {
        return discountWoSaiMchType;
    }

    public void setDiscountWoSaiMchType(String discountWoSaiMchType) {
        this.discountWoSaiMchType = discountWoSaiMchType;
    }

    public Long getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Long paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Long getEffectiveAmount() {
        return effectiveAmount;
    }

    public void setEffectiveAmount(Long effectiveAmount) {
        this.effectiveAmount = effectiveAmount;
    }

    public String getTerminalSn() {
        return terminalSn;
    }

    public void setTerminalSn(String terminalSn) {
        this.terminalSn = terminalSn;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getAuthNo() {
        return authNo;
    }

    public void setAuthNo(String authNo) {
        this.authNo = authNo;
    }

    public String getSysTraceNo() {
        return sysTraceNo;
    }

    public void setSysTraceNo(String sysTraceNo) {
        this.sysTraceNo = sysTraceNo;
    }

    public String getBatchBillNo() {
        return batchBillNo;
    }

    public void setBatchBillNo(String batchBillNo) {
        this.batchBillNo = batchBillNo;
    }


    public Long getChannelFinishTime() {
        return channelFinishTime;
    }

    public void setChannelFinishTime(Long channelFinishTime) {
        this.channelFinishTime = channelFinishTime;
    }

    public boolean getAllRefund() {
        return allRefund;
    }

    public void setAllRefund(boolean allRefund) {
        this.allRefund = allRefund;
    }


    public Integer getSharingFlag() {
        return sharingFlag;
    }

    public void setSharingFlag(Integer sharingFlag) {
        this.sharingFlag = sharingFlag;
    }

    public Long getSharingAmount() {
        return sharingAmount;
    }

    public void setSharingAmount(Long sharingAmount) {
        this.sharingAmount = sharingAmount;
    }

    public String getDiscountIcon() {
        return discountIcon;
    }

    public void setDiscountIcon(String discountIcon) {
        this.discountIcon = discountIcon;
    }

    public String getMchDiscountOriginType() {
        return mchDiscountOriginType;
    }

    public void setMchDiscountOriginType(String mchDiscountOriginType) {
        this.mchDiscountOriginType = mchDiscountOriginType;
    }

    public Long getOuterDiscount() {
        return outerDiscount;
    }

    public void setOuterDiscount(Long outerDiscount) {
        this.outerDiscount = outerDiscount;
    }

    public long getWoSaiMchFavorableAmount() {
        return woSaiMchFavorableAmount;
    }

    public void setWoSaiMchFavorableAmount(long woSaiMchFavorableAmount) {
        this.woSaiMchFavorableAmount = woSaiMchFavorableAmount;
    }

    public void setUseQuota(boolean useQuota) {
        this.useQuota = useQuota;
    }

    public boolean getUseQuota() {
        return useQuota;
    }

    public List<Map<String, Object>> getWoSaiMchFavorableAmountList() {
        return woSaiMchFavorableAmountList;
    }

    public void setWoSaiMchFavorableAmountList(List<Map<String, Object>> woSaiMchFavorableAmountList) {
        this.woSaiMchFavorableAmountList = woSaiMchFavorableAmountList;
    }

    public void setCashDeskId(String cashDeskId) {
        this.cashDeskId = cashDeskId;
    }

    public String getCashDeskId() {
        return cashDeskId;
    }

    public void setCashDeskName(String cashDeskName) {
        this.cashDeskName = cashDeskName;
    }

    public String getCashDeskName() {
        return cashDeskName;
    }

    public String getCashierName() {
        return cashierName;
    }

    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public String getRefernumber() {
        return refernumber;
    }

    public void setRefernumber(String refernumber) {
        this.refernumber = refernumber;
    }

    public Boolean getIsDepositConsumeRefund() {
        return isDepositConsumeRefund;
    }

    public void setIsDepositConsumeRefund(Boolean isDepositConsumeRefund) {
        this.isDepositConsumeRefund = isDepositConsumeRefund;
    }

    public String getProviderMchId() {
        return providerMchId;
    }

    public void setProviderMchId(String providerMchId) {
        this.providerMchId = providerMchId;
    }

    public Long getOrderCtime() {
        return orderCtime;
    }

    public void setOrderCtime(Long orderCtime) {
        this.orderCtime = orderCtime;
    }

    public Long getOrderOriginalAmount() {
        return orderOriginalAmount;
    }

    public void setOrderOriginalAmount(Long orderOriginalAmount) {
        this.orderOriginalAmount = orderOriginalAmount;
    }

    public Long getOrderEffectiveAmount() {
        return orderEffectiveAmount;
    }

    public void setOrderEffectiveAmount(Long orderEffectiveAmount) {
        this.orderEffectiveAmount = orderEffectiveAmount;
    }

    public String getOperatorOrigin() {
        return operatorOrigin;
    }

    public void setOperatorOrigin(String operatorOrigin) {
        this.operatorOrigin = operatorOrigin;
    }
}
