package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum TransactionStatus implements BaseEnum<Integer, String> {

    /**
     * 成功
     */
    SUCCESS(2000, "成功"),

    /**
     * 处理中
     */
    IN_PROG(1001, "处理中"),

    /**
     * 初始化
     */
    INIT(0, "初始化");

    private int code;

    private String desc;

    private TransactionStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
