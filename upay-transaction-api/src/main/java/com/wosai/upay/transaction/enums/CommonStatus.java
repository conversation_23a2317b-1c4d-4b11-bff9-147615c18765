package com.wosai.upay.transaction.enums;

/**
 * <AUTHOR>
 */
public enum CommonStatus implements BaseEnum<Integer, String> {

    /**
     * SUCCESS
     */
    SUCCESS(1, "成功"),

    /**
     * ERROR
     */
    ERROR(2, "错误");

    private int code;

    private String desc;

    private CommonStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
