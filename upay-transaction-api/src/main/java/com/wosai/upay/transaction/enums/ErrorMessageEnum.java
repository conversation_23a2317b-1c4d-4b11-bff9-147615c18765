package com.wosai.upay.transaction.enums;

import com.wosai.upay.transaction.exception.BizException;

/**
 * <AUTHOR>
 */
public enum ErrorMessageEnum {

    /**
     *missing param
     */
    MISSING_PARAM(1, "missing param"),

    /**
     * token is required
     */
    MISSING_TOKEN(2, "token is required"),

    /**
     * merchant_id is required
     */
    MISSING_MERCHANT_ID(3, "merchant_id is required"),

    /**
     * The time span is greater than 3 months
     */
    ILLEGAL_TIME_SPAN(4, "The time span is greater than 3 months"),

    /**
     * The min amount should less than max amount
     */
    ILLEGAL_AMOUNT_SPAN(4, "The min amount should less than max amount"),

    /**
     * merchant_ids，merchant_id不能同时为空
     */
    MISSING_MERCHANT_IDS_OR_MERCHANT_ID(4, "merchant_ids，merchant_id不能同时为空!"),

    /**
     * merchant_ids，merchant_sn，merchant_id 不能同时为空
     */
    MISSING_MERCHANT_IDS_OR_MERCHANT_ID_OR_MERCHANT_SN(5, "merchant_ids，merchant_sn，merchant_id 不能同时为空!"),

    /**
     * 获取终端信息处出错
     */
    GET_TERMINAL_ERROR(6, "获取终端信息处出错!"),

    /**
     * 获取门店信息处出错
     */
    GET_STORE_ERROR(7, "获取门店信息处出错!"),

    /**
     * 获取商户信息处出错
     */
    GET_MERCHANT_ERROR(8, "获取商户信息处出错!"),

    /**
     * 构建订单信息出错
     */
    BULID_ORDER_ERROR(9, "构建订单信息出错!"),

    /**
     * 获取商户下用户的基本信息出错
     */
    GET_OPERATOR_ERROR(10, "获取商户下用户的基本信息出错!"),
    /**
     * 系统繁忙,请稍后再试
     */
    SYSTEM_BUSY(11, "系统繁忙,请稍后再试"),

    /**
     * account_id 不能为空
     */
    MISSING_ACCOUNT_ID(12, "account_id 不能为空"),

    /**
     * 账本暂不支持该排序类型
     */
    UNSUPPORTED_ACCOUNT_BOOK_SORT_TYPE(13, "账本暂不支持该排序类型"),

    /**
     * 起始和截止时间不能为空
     */
    START_AND_END_TIME_CANNOT_BE_NULL(14, "起始和截止时间不能为空"),

    /**
     * 账本排序时，时间跨度不能超过24小时
     */
    @Deprecated
    TIME_SPAN_CANNOT_EXCEED_24_HOURS(15, "账本排序时，时间跨度不能超过24小时"),

    /**
     * 获取数据出错
     */
    ERROR_GETTING_DATA(16, "获取数据出错"),

    PAGE_SIZE_TOO_LARGE(17, "分页查询，页大小不能超过5000"),

    /**
     * 请求过于频繁,请稍后重试
     */
    REQUEST_TOO_FREQUENTLY(18, "请求过于频繁,请稍后重试"),

    /**
     * 起始时间必须早于截止时间
     */
    START_TIME_MUST_BE_EARLIER_THAN_END_TIME(19, "开始时间必须早于结束时间"),

    PAGE_SIZE_TOO_LARGE_200(20, "分页查询，页大小不能超过200"),

    START_TIME_GREATER_THAN_ONE_YEAR(21, "不支持导出1年前的余额对账单"),

    /**
     * 获取终端信息处出错
     */
    GET_CASH_DESK_ERROR(22, "获取收银台信息出错!"),

    CASH_DESK_TRADE_QUERY_ERROR(23, "筛选终端或收银员与收银台不匹配"),

    ACCOUNT_BOOK_QUERY_INVALID_SOURCE_ERROR(24, "账本不支持该业务方查询"),

    /**
     * 账本排序时，时间跨度不能超过3个月
     */
    TIME_SPAN_CANNOT_EXCEED_3_MONTHS(25, "账本排序时，时间跨度不能超过3个月");


    private final int code;

    private final String msg;


    private ErrorMessageEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public BizException getBizException() {
        return new BizException(this.code, this.msg);
    }

}
