package com.wosai.upay.transaction.model;


/**
 * <AUTHOR>
 */
public class Order {
    public static final String TN = "order";

    public static final int STATUS_CREATED = 0;

    public static final int STATUS_PAID = 1200;
    public static final int STATUS_PAY_CANCELED = 1300;
    public static final int STATUS_PAY_ERROR = 1501;

    public static final int STATUS_REFUND_INPROGRESS = 2100;

    public static final int STATUS_REFUNDED = 2201;
    public static final int STATUS_PARTIAL_REFUNDED = 2210;
    public static final int STATUS_REFUND_ERROR = 2501;

    public static final int STATUS_CANCEL_INPROGRESS = 3100;
    public static final int STATUS_CANCELED = 3201;
    public static final int STATUS_CANCEL_ERROR = 3501;


    /**
     * 预授权-进行中
     */
    public static final int STATUS_DEPOSIT_FREEZE_INPROGRESS = 1;

    /**
     * 预授权-成功
     */
    public static final int STATUS_DEPOSIT_FREEZED = 4200;

    /**
     * 预授权-失败后撤销（结果为失败）
     */
    public static final int STATUS_DEPOSIT_FREEZE_CANCELED = 4300;

    /**
     * 预授权-失败（结果未知，可能成功，也有可能失败）
     */
    public static final int STATUS_DEPOSIT_FREEZE_ERROR = 4501;

    /**
     * 预授权撤销-进行中
     */
    public static final int STATUS_DEPOSIT_CANCEL_INPROGRESS = 5100;

    /**
     * 预授权撤销-失败
     */
    public static final int STATUS_DEPOSIT_CANCELED = 5201;

    /**
     * 预授权撤销-失败
     */
    public static final int STATUS_DEPOSIT_CANCEL_ERROR = 5501;


    /**
     * 预授权完成-进行中
     */
    public static final int STATUS_DEPOSIT_CONSUME_INPROGRESS = 6100;

    /**
     * 预授权完成-成功
     */
    public static final int STATUS_DEPOSIT_CONSUMED = 6201;

    /**
     * 预授权完成-失败
     */
    public static final int STATUS_DEPOSIT_CONSUME_ERROR = 6501;


    /**
     * 预授权完成撤销-进行中
     */
    public static final int STATUS_DEPOSIT_CONSUME_CANCLE_INPROGRESS = 7100;

    /**
     * 预授权完成撤销-成功
     */
    public static final int STATUS_DEPOSIT_CONSUMED_CANCELED = 7201;

    /**
     * 预授权完成撤销-失败
     */
    public static final int STATUS_DEPOSIT_CONSUME_CANCLE_ERROR = 7501;






    public static final int CONTEXT_TYPE_STORE = 1;
    public static final int CONTEXT_TYPE_TERMINAL = 2;

    public static final int PROVIDER_CIBBANK = 1001;
    public static final int PROVIDER_LAKALA = 1002;
    public static final int PROVIDER_CITICBANK = 1003;

    public static final int PAYWAY_ALIPAY = 1;
    public static final int PAYWAY_ALIPAY2 = 2;
    public static final int PAYWAY_WEIXIN = 3;
    public static final int PAYWAY_BAIFUBAO = 4;
    public static final int PAYWAY_JDWALLET = 5;
    public static final int PAYWAY_QQWALLET = 6;
    public static final int PAYWAY_APPLEPAY = 7;
    public static final int PAYWAY_NFC = 7;
    /**
     * 拉卡拉钱包
     */
    public static final int PAYWAY_LKLWALLET = 8;
    /**
     *  和支付
     */
    public static final int PAYWAY_CMCC = 9;
    /**
     * 拉卡拉银联二维码支付
     */
    public static final int PAYWAY_LKL_UNIONPAY = 17;
    /**
     * 翼支付
     */
    public static final int PAYWAY_BESTPAY = 18;
    /**
     *  微信香港本地支付
     */
    public static final int PAYWAY_WEIXIN_HK = 19;
    /**
     * 支付宝国际版支付
     */
    public static final int PAYWAY_ALIPAY_INTL = 20;
    /**
     * 银行卡
     */
    public static final int PAYWAY_BANKCARD = 21;
    /**
     * 索迪斯
     */
    public static final int PAYWAY_SODEXO = 22;


    /**
     * 数字人民币
     */
    public static final int PAYWAY_DCEP = 23;

    /**
     * 银行转账
     */
    public static final int PAYWAY_BANKACCOUNT = 27;

    /**
     * 储值卡支付方式
     * <p/>
     * 储值卡（原名称："礼品卡"，原储值卡 100 弃用并合并至 101）
     */
    public static final int PAYWAY_GIFT_CARD = 101;

    /**
     * 现金
     */
    public static final int PAYWAY_CASH = 102;
    /**
     * 饿了么
     */
    public static final int PAYWAY_ELE = 103;
    /**
     * 美团
     */
    public static final int PAYWAY_MEI_TUAN = 104;
    /**
     * 储值
     */
    public static final int PAYWAY_STORE = 105;
    /**
     * 储值充值，退款（在支付网关侧 是支付宝或微信交易）
     */
    public static final int PAYWAY_STORE_IN = 106;
    /**
     * 大众点评
     */
    public static final int PAYWAY_DIAN_PING = 107;

    /**
     * 建设银行
     */
    public static final int PAYWAY_CCB_APP = 109;

    /**
     * 招商银行
     */
    public static final int PAYWAY_CMB_APP = 110;

    /**
     * 福利卡
     */
    public static final int PAYWAY_WELFARE_CARD = 111;

    /**
     * 支付宝记账
     */
    public static final int PAYWAY_ALI_CHG = 302;

    /**
     * 微信记账
     */
    public static final int PAYWAY_WX_CHG = 303;

    /**
     * 抖音券记账
     */
    public static final int PAYWAY_DY_QUAN = 112;

    /**
     * 其他记账方式
     */
    public static final int PAYWAY_OTH_CHG = 108;
    public static final int SUB_PAYWAY_BARCODE = 1;
    public static final int SUB_PAYWAY_QRCODE = 2;
    public static final int SUB_PAYWAY_WAP = 3;
    public static final int SUB_PAYWAY_MINI = 4;


    /**
     * 订单号 VARCHAR(20)
     */
    public static final String SN = "sn";

    /**
     * 商户订单号/同一个商户下唯一，否则视为重复订单 VARCHAR(32)
     */
    public static final String CLIENT_SN = "client_sn";

    /**
     * 标题 VARCHAR(45)
     */
    public static final String SUBJECT = "subject";

    /**
     * 详情 VARCHAR(255)
     */
    public static final String BODY = "body";

    /**
     * 明细 BLOB
     */
    public static final String ITEMS = "items";

    /**
     * 扣除退款商品后的明细 BLOB
     */
    public static final String NET_ITEMS = "net_items";

    /**
     * 状态 INT
     */
    public static final String STATUS = "status";

    /**
     * 订单是否触发trade-coprocessor处理逻辑 TINYINT
     */
    public static final String TCP_MODIFIED = "tcp_modified";

    /**
     * 原始总金额 BIGINT
     */
    public static final String ORIGINAL_TOTAL = "original_total";

    /**
     * 扣除退款后的原始金额的净值 BIGINT
     */
    public static final String NET_ORIGINAL = "net_original";

    /**
     * 向支付通道请求的支付总金额 BIGINT (trade-coprocessor可能会返回不等于original_total的值）
     */
    public static final String EFFECTIVE_TOTAL = "effective_total";

    /**
     * 扣除退款后的向支付通道请求的支付金额的净值 BIGINT
     */
    public static final String NET_EFFECTIVE = "net_effective";
    /**
     * 向支付通道请求的支付总金额中的折扣部分，包括商户补贴和服务商补贴
     */
    public static final String TOTAL_DISCOUNT = "total_discount";

    /**
     * 如果没有退款操作，net_discount 等于total_discount。否则为退款之后剩余的折扣。有些服务商规定折扣部分优先退。
     */
    public static final String NET_DISCOUNT = "net_discount";

    /**
     * 付款人在支付通道的用户ID  VARCHAR(45)
     */
    public static final String BUYER_UID = "buyer_uid";

    /**
     * 付款人在支付通道的登录帐号 VARCHAR(45)
     */
    public static final String BUYER_LOGIN = "buyer_login";

    /**
     * 服务商UUID
     */
    public static final String TENANT_ID = "tenant_id";

    /**
     * 销售或者代理的UUID
     */
    public static final String SELLER_ID = "seller_id";

    /**
     * 销售或者代理的路径
     */
    public static final String SELLER_PATH = "seller_path";
    /**
     * 商户ID VARCHAR(37)  商户记录的UUID
     */
    public static final String MERCHANT_ID = "merchant_id";

    /**
     * 门店ID VARCHAR(37)  门店记录的UUID
     */
    public static final String STORE_ID = "store_id";

    /**
     * 终端ID VARCHAR(37)  终端记录的UUID
     */
    public static final String TERMINAL_ID = "terminal_id";
    /**
     * 原始支付交易的操作员 VARCHAR(45) 姓名或其它ID
     */
    public static final String OPERATOR = "operator";

    /**
     * 支付通道 INT 直接对接的收款通道参考payway（0-99）, 对接第3方（1000以上） 1001: 兴业银行 1002: 拉卡拉'
     */
    public static final String PROVIDER = "provider";

    /**
     *  支付通道 INT
     */
    public static final String PAYWAY = "payway";

    /**
     *  支付方式 INT
     */
    public static final String SUB_PAYWAY = "sub_payway";

    /**
     * 支付通道返回的交易凭证号 VARCHAR(128)
     */
    public static final String TRADE_NO = "trade_no";

    /**
     * 商户上传的附加字段，保存在订单中。终端查询的时候原样返回。
     */
    public static final String REFLECT = "reflect";

    /**
     * nfc交易的时候的银行卡号
     */
    public static final String NFC_CARD = "nfc_card";
    /**
     * ITEMS 消费者在喔噻平台的付款方式明细 List {type, amount_total, net_amount}
     */
    public static final String PAYMENTS = "payments";

    /**
     * ITEMS 消费者在支付通道平台的付款方式明细 List {type, amount_total, net_amount}
     */
    public static final String CHANNEL_PAYMENTS = "channel_payments";

    /**
     * 存放在order.items.payments里折扣立减信息，把这些字段拆开为 喔噻红包total
     */
    public static final String HONGBAO_WOSAI_TOTAL = "hongbao_wosai_total";

    /**
     * 喔噻商户红包total
     */
    public static final String HONGBAO_WOSAI_MCH_TOTAL = "hongbao_wosai_mch_total";

    /**
     * 喔噻折扣total
     */
    public static final String DISCOUNT_WOSAI_TOTAL = "discount_wosai_total";
    /**
     * 喔噻商户折扣total
     */
    public static final String DISCOUNT_WOSAI_MCH_TOTAL = "discount_wosai_mch_total";

    /**
     * 喔噻红包
     */
    public static final String NET_HONGBAO_WOSAI = "net_hongbao_wosai";

    /**
     * 喔噻商户红包
     */
    public static final String NET_HONGBAO_WOSAI_MCH = "net_hongbao_wosai_mch";

    /**
     * 喔噻折扣
     */
    public static final String NET_DISCOUNT_WOSAI = "net_discount_wosai";

    /**
     * 喔噻商户折扣
     */
    public static final String NET_DISCOUNT_WOSAI_MCH = "net_discount_wosai_mch";

    /**
     * 收款通道折扣净值
     */
    public static final String NET_DISCOUNT_CHANNEL = "net_discount_channel";
    /**
     * 收款通道商户免充值折扣净值
     */
    public static final String NET_DISCOUNT_CHANNEL_MCH = "net_discount_channel_mch";
    /**
     * 收款通道商户充值折扣净值
     */
    public static final String NET_DISCOUNT_CHANNEL_MCH_TOP_UP = "net_discount_channel_mch_top_up";
    /**
     * 收款通道红包净值
     */
    public static final String NET_HONGBAO_CHANNEL = "net_hongbao_channel";
    /**
     *  收款通道商户免充值红包净值
     */
    public static final String NET_HONGBAO_CHANNEL_MCH = "net_hongbao_channel_mch";
    /**
     * 收款通道商户充值红包净值
     */
    public static final String NET_HONGBAO_CHANNEL_MCH_TOP_UP = "net_hongbao_channel_mch_top_up";

    /**
     * 收款通道折扣原始金额
     */
    public static final String DISCOUNT_CHANNEL_TOTAL = "discount_channel_total";
    /**
     * 收款通道商户免充值折扣原始金额
     */
    public static final String DISCOUNT_CHANNEL_MCH_TOTAL = "discount_channel_mch_total";
    /**
     * 收款通道商户充值折扣原始金额
     */
    public static final String DISCOUNT_CHANNEL_MCH_TOP_UP_TOTAL = "discount_channel_mch_top_up_total";
    /**
     * 收款通道红包原始金额
     */
    public static final String HONGBAO_CHANNEL_TOTAL = "hongbao_channel_total";
    /**
     * 收款通道商户免充值红包原始金额
     */
    public static final String HONGBAO_CHANNEL_MCH_TOTAL = "hongbao_channel_mch_total";
    /**
     * 收款通道商户充值红包原始金额
     */
    public static final String HONGBAO_CHANNEL_MCH_TOP_UP_TOTAL = "hongbao_channel_mch_top_up_total";

    /**
     * 商户优惠
     */
    public static final String MCH_FAVORABLE_AMOUNT = "mch_favorable_amount";
    /**
     * 收钱吧优惠
     */
    public static final String WOSAI_FAVORABLE_AMOUNT = "wosai_favorable_amount";
    /**
     * 收款通道机构优惠
     */
    public static final String CHANNEL_AGENT_FAVORABLE_AMOUNT = "channel_agent_favorable_amount";
    /**
     * 收款通道商户免充值优惠
     */
    public static final String CHANNEL_MCH_FAVORABLE_AMOUNT = "channel_mch_favorable_amount";
    /**
     * 收款通道商户充值优惠
     */
    public static final String CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT = "channel_mch_top_up_favorable_amount";
    /**
     * 收款通道优惠
     */
    public static final String CHANNEL_FAVORABLE_AMOUNT = "channel_favorable_amount";
    /**
     * 实收金额
     */
    public static final String ACTUAL_RECEIVE_AMOUNT = "actual_receive_amount";
    /**
     * 费率
     */
    public static final String FEE_RATE = "fee_rate";
    /**
     * 手续费
     */
    public static final String FEE = "fee";
    /**
     * 结算金额
     */
    public static final String CLEARING_AMOUNT = "clearing_amount";

    /**
     * 分账金额
     */
    public static final String SHARING_AMOUNT = "sharing_amount";

    /**
     * 技术服务费
     */
    public static final String TRADE_SERVICE_SHARING_AMOUNT = "trade_service_sharing_amount";

    /**
     * 储值标志
     */
    public static final String STORE_FLAG = "is_store";


    public static final String TRADE_APP_NAME  = "trade_app_name";

    public static final String TRADE_APP  = "trade_app";

    public static final String SQB_BIZ_MODEL_NAME  = "sqb_biz_model_name";

    public static final String SQB_BIZ_MODEL  = "sqb_biz_model";


    public static final String SQB_PAY_SOURCE_NAME = "sqb_pay_source_name";

    public static final String SQB_PAY_SOURCE = "sqb_pay_source";

    public static final String SQB_PAY_PATH_NAME = "sqb_pay_path_name";

    public static final String SQB_PAY_PATH = "sqb_pay_path";


    public static enum Status {
        CREATED(STATUS_CREATED),
        PAID(STATUS_PAID),
        PAY_CANCELED(STATUS_PAY_CANCELED),
        PAY_ERROR(STATUS_PAY_ERROR),
        REFUND_INPROGRESS(STATUS_REFUND_INPROGRESS),
        REFUNDED(STATUS_REFUNDED),
        PARTIAL_REFUNDED(STATUS_PARTIAL_REFUNDED),
        REFUND_ERROR(STATUS_REFUND_ERROR),
        CANCEL_INPROGRESS(STATUS_CANCEL_INPROGRESS),
        CANCELED(STATUS_CANCELED),
        CANCEL_ERROR(STATUS_CANCEL_ERROR),
        INVALID_STATUS_CODE(99999);

        private int code;

        Status(int code) {
            this.code = code;
        }

        public static Status fromCode(int code) {
            for (Status status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return INVALID_STATUS_CODE;
        }


    }
}
