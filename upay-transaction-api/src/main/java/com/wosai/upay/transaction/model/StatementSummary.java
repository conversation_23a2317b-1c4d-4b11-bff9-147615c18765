package com.wosai.upay.transaction.model;

/**
 * <AUTHOR>
 * 对账单汇总统计
 */
public class StatementSummary {
    /**
     * 保存商户基本信息
     */
    public static final String MERCHANT_BASIC_INFO_MAP = "merchant_basic_info_map";
    /**
     * 保存门店基本信息
     */
    public static final String STORE_BASIC_INFO_MAP = "store_basic_info_map";
    /**
     * 保存收银台基本信息
     */
    public static final String CASH_DESK_BASIC_INFO_MAP = "cash_desk_basic_info_map";
    /**
     * 保存终端基本信息
     */
    public static final String TERMINAL_BASIC_INFO_MAP = "terminal_basic_info_map";
    /**
     * 保存收银员基本信息
     */
    public static final String OPERATOR_BASIC_INFO_MAP = "operator_basic_info_map";
    /**
     * 保存班次收银员基本信息
     */
    public static final String CASHIER_BASIC_INFO_MAP = "cashier_basic_info_map";
    /**
     * 保存班次收银员基本信息
     */
    public static final String CASH_DESK_CASHIER_BASIC_INFO_MAP = "cash_desk_cashier_basic_info_map";

    /**
     * 保存[商户: 门店]关联关系
     */
    public static final String MERCHANT_SN_STORES_MAP = "merchant_sn_stores_map";
    /**
     * 保存[门店: 收银台]关联关系
     */
    public static final String STORE_SN_CASHDESKS_MAP = "store_sn_cashdesks_map";
    /**
     * 保存[门店: 终端]关联关系
     */
    public static final String STORE_SN_TERMINALS_MAP = "store_sn_terminals_map";
    /**
     * 保存[终端: 收银员]关联关系
     */
    public static final String TERMINAL_SN_OPERATORS_MAP = "terminal_sn_operators_map";
    /**
     * 保存班次[门店: 收银员]关联关系
     */
    public static final String STORE_SN_CASHIERS_MAP = "store_sn_cashiers_map";
    /**
     * 保存班次[门店: 收银员]关联关系
     */
    public static final String CASH_DESK_STORE_SN_CASHIERS_MAP = "cash_desk_store_sn_cashiers_map";

    /**
     * 保存payway汇总数据
     */
    public static final String PAYWAY_SUMMARY_MAP = "payway_summary_map";
    /**
     * 保存商户汇总数据
     */
    public static final String MERCHANT_SUMMARY_MAP = "merchant_summary_map";
    /**
     * 保存门店汇总数据
     */
    public static final String STORE_SUMMARY_MAP = "store_summary_map";
    /**
     * 保存收银台汇总数据
     */
    public static final String CASH_DESK_SUMMARY_MAP = "cash_desk_summary_map";
    /**
     * 保存终端汇总数据
     */
    public static final String TERMINAL_SUMMARY_MAP = "terminal_summary_map";
    /**
     * 保存收银员汇总数据
     */
    public static final String OPERATOR_SUMMARY_MAP = "operator_summary_map";
    /** 
     * 保存收银台汇总数据
    */
    public static final String CASHIER_SUMMARY_MAP = "cashier_summary_map";
    /**
     * 保存班次收银员汇总数据
     */
    public static final String CASH_DESK_CASHIER_SUMMARY_MAP = "cash_desk_cashier_summary_map";

    /**
     * 商户门店支付源汇总
     */
    public static final String UNION_SUMMARY_MAP = "union_summary_map";


    public static final String PREFIX = "store_";

    public static final String TRADE_COUNT = "tradeCount";
    public static final String TRADE_AMOUNT = "tradeAmount";
    public static final String REFUND_COUNT = "refundCount";
    public static final String REFUND_AMOUNT = "refundAmount";
    public static final String MERCHANT_DISCOUNT = "merchantDiscount";
    public static final String SHOUQIANBA_DISCOUNT = "shouqianbaDiscount";
    public static final String CHANNEL_DISCOUNT = "channelDiscount";
    public static final String CHANNEL_MCH_TOP_UP_DISCOUNT = "channelMchTopUpDiscount";
    public static final String CHANNEL_MCH_DISCOUNT = "channelMchDiscount";
    public static final String RECEIVE_AMOUNT = "receiveAmount";
    public static final String TRADE_FEE = "tradeFee";
    public static final String SHARING_AMOUNT = "sharingAmount";
    public static final String TRADE_SERVICE_SHARING_AMOUNT = "tradeServiceSharingAmount";
    public static final String INCOME_AMOUNT = "incomeAmount";
    public static final String CHARGE_INCOME_AMOUNT = "chargeIncomeAmount";
    public static final String STORE_PAY_RECEIVE_AMOUNT = "storePayReceiveAmount";
    public static final String STORE_IN_CASH_INCOME_AMOUNT = "store_cashIncomeAmount";
    public static final String STORE_IN_OTHER_CHARGE_INCOME_AMOUNT = "store_otherChargeIncomeAmount";
    public static final String LIQUIDATION_INCOME_AMOUNT = "liquidationIncomeAmount";
    public static final String FOREIGN_CARD_INCOME_AMOUNT = "foreignCardIncomeAmount";
    public static final String CROSS_MCH_REFUND_AMOUNT = "crossMchRefundAmount";
    public static final String CROSS_MCH_REFUND_COUNT = "crossMchRefundCount";
    public static final String HBFQ_MCH_DISCOUNT_AMOUNT = "hbfqMchDiscountAmount";
    public static final String DEAL_TO_ACCOUNT_AMOUNT = "dealToAccountAmount";
//
//    public static final String STORE_TRADE_COUNT = PREFIX + "tradeCount";
//    public static final String STORE_TRADE_AMOUNT = PREFIX + "tradeAmount";
//    public static final String STORE_REFUND_COUNT = PREFIX + "refundCount";
//    public static final String STORE_REFUND_AMOUNT = PREFIX + "refundAmount";
//    public static final String STORE_MERCHANT_DISCOUNT = PREFIX + "merchantDiscount";
//    public static final String STORE_SHOUQIANBA_DISCOUNT = PREFIX + "shouqianbaDiscount";
//    public static final String STORE_CHANNEL_DISCOUNT = PREFIX + "channelDiscount";
//    public static final String STORE_CHANNEL_MCH_TOP_UP_DISCOUNT = PREFIX + "channelMchTopUpDiscount";
//    public static final String STORE_CHANNEL_MCH_DISCOUNT = PREFIX + "channelMchDiscount";
//    public static final String STORE_RECEIVE_AMOUNT = PREFIX + "receiveAmount";
//    public static final String STORE_TRADE_FEE = PREFIX + "tradeFee";
//    public static final String STORE_SHARING_AMOUNT = PREFIX + "sharingAmount";
//    public static final String STORE_INCOME_AMOUNT = PREFIX + "incomeAmount";
//    public static final String STORE_CROSS_MCH_REFUND_AMOUNT = PREFIX + "crossMchRefundAmount";
//    public static final String STORE_CROSS_MCH_REFUND_COUNT = PREFIX + "crossMchRefundCount";


}
