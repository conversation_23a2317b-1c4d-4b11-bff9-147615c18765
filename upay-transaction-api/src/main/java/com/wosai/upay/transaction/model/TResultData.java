package com.wosai.upay.transaction.model;

import java.util.*;

/**
 * <AUTHOR>
 * @param <T>
 */
public class TResultData<T> {

    /**
     * 最小记录时间戳,下次查询的结束时间
     */
    private Long lastRecordTime;

    private Set<TPayWayIcon> payWayIcons;

    private T data;

    public Long getLastRecordTime() {
        return lastRecordTime;
    }

    public void setLastRecordTime(Long lastRecordTime) {
        this.lastRecordTime = lastRecordTime;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Set<TPayWayIcon> getPayWayIcons() {
        return payWayIcons;
    }

    public void setPayWayIcons(Set<TPayWayIcon> payWayIcons) {
        this.payWayIcons = payWayIcons;
    }


    public TResultData(Long lastRecordTime, Set<TPayWayIcon> payWayIcons, T data) {
        this.lastRecordTime = lastRecordTime;
        this.payWayIcons = payWayIcons;
        this.data = data;
    }


    public TResultData() {
    }

    @Override
    public String toString() {
        return "TResultData{" +
                "lastRecordTime=" + lastRecordTime +
                ", payWayIcons=" + payWayIcons +
                ", data=" + data +
                '}';
    }
}
