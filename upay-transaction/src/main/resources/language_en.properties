period=Period
merchant_no=Merchant No.
merchant_name=Merchant Name
statement_style= Statement Type
merchant_transaction_summary=Merchant Transaction Summary
merchant_store_payway_transaction_summary=Store Payment Type Summary
merchant_transaction_summary_list=Merchant Transaction Summary List
payment_type=Payment Type
transaction_no=Transaction No.
transaction_amount=Transaction Amount
refund_no=Refund No.
refund_amount=Refund Amount
transaction_net_amount=Transaction Net Amount
merchant_discount=Merchant Discount
wosai_discount=WOSAI Discount
payment_type_discount=Payment Type Discount
merchant_discount_prepaid_mode=Merchant Discount - Prepaid Mode
merchant_discount_non_prepaid_mode=Merchant Discount - Non-prepaid Mode
paid_amount=Paid Amount
charge=Charge
settlement_amount=Settlement Amount
detail_settlement_amount=Settlement Amount
total_settlement_to_account_amount=Total Settlement To Account Amount
total_deal_to_account_amount=Total Deal To Account Amount
total_settlement_amount=Total Settlement Amount
settlement_to_account_amount=Settlement To Account Amount
deal_to_account_amount=Deal To Account Amount
deal_no_foreign_card_to_account_amount=Deal No Foreign Card To Account Amount
foreign_card_trade_to_account_amount=Deal Foreign Card To Account Amount
store_in_total_settlement_amount=Store In Total Settlement Amount
store_in_settlement_to_account_amount=Store In Settlement To Account Amount
store_deal_to_account_amount=Store In Deal To Account Amount
charge_settlement_amount=Charge Settlement Amount
store_pay_receive_amount=Store Pay Receive Amount
store_in_cash_settlement_amount=Store In Cash Settlement Amount
store_in_other_charge_settlement_amount=Store In Other Charge Settlement Amount
liquidation_settlement_amount=Liquidation Settlement Amount
store_in_liquidation_settlement_amount=Store In Liquidation Settlement Amount
cross_merchant_refund_no=Diff Merchant Refund No
cross_merchant_refund_amount=Diff Merchant Refund Amount
mch_discount_origin_type=Merchant Discount Type

store_transaction_no= Stored Value Transaction No.
store_transaction_amount= Stored Value Transaction Amount
store_refund_no= Stored Value Refund No.
store_refund_amount= Stored Value Refund Amount
store_transaction_net_amount= Stored Value Transaction Net Amount
store_merchant_discount= Stored Value Merchant Discount
store_wosai_discount= Stored Value SQB Discount
store_payment_type_discount= Stored Value Payment Type Discount
store_merchant_discount_prepaid_mode= Stored Value Merchant Discount-Prepaid Mode
store_merchant_discount_non_prepaid_mode= Stored Value Merchant Discount-Non-prepaid Mode
store_paid_amount= Stored Value Paid Amount
store_charge= Stored Value Charge
store_settlement_amount= Stored Value Settlement Amount
store_sharing_amount= Stored Value Sharing Amount
store_merchant_sharing_amount= Stored Value Merchant Sharing Amount
store_trade_service_sharing_amount= Stored Value Trade Service Sharing Amount

alipay_new_no=Alipay 2.0 No.
alipay_new_paid_amount=Alipay 2.0 Paid Amount
alipay_new_settlement_amount=Alipay 2.0 Settlement Amount
wechat_no=Wechat No.
wechat_paid_amount=Wechat Paid Amount
wechat_settlement_amount=Wechat Settlement Amount
unionpay_cloud_flash_payment_no=UnionPay No.
unionpay_cloud_flash_payment_paid_amount=UnionPay Paid Amount
unionpay_cloud_flash_payment_settlement_amount=UnionPay Settlement Amount
bankcard_payment_no=Bank Card No.
bankcard_payment_paid_amount=Bank Card Paid Amount
bankcard_payment_settlement_amount=Bank Card Settlement Amount

product_flag=Application
subpayway=SubPayway
cash_desk_name=CashDesk Name
cash_desk_no=CashDesk No
store_cashier_transaction_summary_list=Store Cashier  Transaction Summary List（only the cashiers with shift records are counted）
store_cash_desk_transaction_summary_list=Store Cash Desk Transaction Summary List
store_cash_desk_change_shifts_summary_list=Store Change Shifts Transaction Summary List
cashier_name=Cashier
cashier_phone=Phone Number
change_shifts_batch_no=Batch No
change_shifts_start_date=Start Date
change_shifts_end_date=End Date

op_store_sn=Operate Store No
op_mch_store_sn=Operate Merchant Store No.
op_store_name=Operate Store Name
pay_channel=Pay Channel
pay_fund_channel=Pay Channel
is_cross_mch_refund=Refund Type


currency=Currency
store_terminal_transaction_summary_list=Store Terminal Transaction Summary List
store_no=Store No.
merchant_store_no=Merchant Store No.
store_name=Store Name
terminal_no=Terminal No.
merchant_terminal_no=Merchant Terminal No.
terminal_name=Terminal Name
terminal_type=Terminal Type
device_id=Device ID
operator=Operator
cashier=Cashier
wosai_transaction_details=WOSAI Transaction Details
transaction_details_list=Transaction Details List
transaction_date=Transaction Date
time=Time
merchant_serial_no=Merchant Serial No.
product_name=Product Name
merchant_order_no=Merchant Order No.
wosai_order_no=Wosai Order No.
payment_type_order_no=Payment Type Order No.
transaction_type=Transaction Type
transaction_status=Transaction Status
consumer_account=Consumer Account
the_amount_consumer_paid=The Amount Consumer Paid
charge_rate=Charge Rate
remarks=Remarks
sharing_flag=Sharing Flag
sharing_amount=Sharing Amount(including trade service fee)
merchant_sharing_amount=Sharing Amount
trade_service_sharing_amount=Trade Service Fee

pay_type=Pay Type
batchbillno=Batch Bill No
systraceno=Sys Trace No
bank_type=Bank Type
lakala_merc_id=Lakala Merc Id
lakala_term_id=Lakala Term Id
trade_no=Trade No
channel_finish_time=Card Finish Time

order_status=Order Status
closed=Closed
partially_refunded=Partially Refunded
full_refund=Full Refund
transaction_order_amount=Transaction Amount
trial_not_trial=Trial/Not Trial
statement_no=Statement No.
group_information=Group Information And Statement Type
group_no=Group No.
group_name=Group Name
group_transaction_summary=Group Transaction Summary
transaction_type_payment=Payment
transaction_type_return=Return
transaction_deposit_freeze=Deposit Freeze
transaction_deposit_cancel=Deposit Cancel
transaction_deposit_consume=Deposit Consume
transaction_deposit_consume_cancel=Deposit Consume cancel
transaction_type_store_in_payment=Stored Value Charge
transaction_type_store_in_return=Stored Value Charge Refund
transaction_type_store_pay_payment=Stored Value Pay
transaction_type_store_pay_return=Stored Value Pay Refund
transaction_status_success=Success
transaction_status_failure=Failure
transaction_status_in_process=In Process
transaction_summary=Transaction Summary
transaction_details=Transaction Details
settlement=Settlement
refund_withdrawal=Refund Withdrawal
withdrawal=Withdrawal
merchant_basic_information=Merchant Basic Information And Statement Type
settlement_summary_wosai=SettlementSummary_Wosai
settlement_details_wosai=SettlementDetails_Wosai
settlement_details_cross_mch_refund_wosai=SettlementDiffMerchant_Refund
Android=Android
iOS=IOS
pc_software=PC Software
payment_device=Payment Device
store_qr_code=Store QR Code
service=Service
admin=Admin
department=Department
store=Store
summary=Summary
store_transaction_summary=Store Transaction Summary
withdraw_funds_summary=Withdraw Funds Summary
to_account_funds_summary=TO ACCOUNT Funds Summary
to_account_funds_summary_with_foreign_card=TO ACCOUNT Funds Summary(If your receiving account includes transactions from foreign cards, the portion of the receipts from these foreign card transactions is expected to be transferred to your personal settlement bank card within T+1 business day. The actual time of arrival still depends on the clearing cycle of the bank's system.)

fq_transaction_details_list=Installment Transaction Details
fq_num=Number of Installments
fq_type=Installment type
fq_mch_discount_amount=Installment Service Fee
fq_pay_type=Installment Interest Payment Type
fq_type_seller=Merchant
fq_type_buyer=Consumer
fq_store_transaction_summary=Store Transaction Summary of Installment
fq_transaction_details=Number of Installments
hb_fq_type=HuaBei Installment
credit_fq_type=Credit card installment
fq_amount=Amount of instalment
trade_tsn=Transaction serial number
sqb_biz_model=Transaction scenario

smart_order=Smart Order Service Fee
smart_flowservice=Smart Flow Service Fee
smart_campusdistribution=Smart Campus Distribution Service Fee
smart_thirddistribution=Smart Third Distribution Service Fee
saas_couponsale=Saas Coupon Sale Service Fee
saas_equitycardsale=Saas Equity Card Sale Service Fee
saas_cardrecharge=Saas Card Recharge Service Fee
hbfq_indirect_tiexi=Hbfq Indirect Tiexi Service Fee
pay_after_user=Pay After User Service Fee
smart_flow_service_commission=Smart Flow Service Commission
smart_flow_service_base_amount=Smart Flow Service Base Amount