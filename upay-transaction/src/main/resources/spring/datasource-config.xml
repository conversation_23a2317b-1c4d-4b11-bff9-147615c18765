<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans.xsd



          http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <import resource="classpath:vault-datasource-manager.xml"/>

    <bean id="databaseVault" class="com.wosai.middleware.vault.Vault" factory-method="autoload"/>

    <bean id="dataSourceTranslateImpls"
          class="com.wosai.database.instrumentation.spring.translate.impl.DBCP2DataSourceTranslate">
        <constructor-arg ref="dataSourceManager"/>
    </bean>

    <!-- ========================= DATASOURCE DEFINITION ========================= -->

    <!-- DataSource configuration for Apache Commons DBCP. -->
    <bean id="statementDatasource" class="org.apache.commons.dbcp2.BasicDataSource" destroy-method="close"
          p:minIdle="${db.minIdle}" p:maxTotal="${db.maxActive}"
          p:driverClassName="${jdbc_transaction.driverClassName}" p:url="${jdbc_transaction.url}"
          p:username="${jdbc_transaction.username:}" p:password="${jdbc_transaction.password:}"
          p:timeBetweenEvictionRunsMillis="${jdbc_transaction.connection.eviction.interval}"/>
          

    <util:list id="initSqls">
        <value>set names utf8mb4</value>
    </util:list>

</beans>
