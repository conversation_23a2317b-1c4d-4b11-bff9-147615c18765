#upay_transaction
jdbc_transaction.driverClassName=com.mysql.cj.jdbc.Driver
jdbc_transaction.url=tk-upay-transaction-upay_transaction-9678
jdbc_transaction.connection.eviction.interval=60000

#db config
db.maxActive=15
db.minIdle=8

# my
upay-picture.server=http://shouqianba-picture-service.beta.iwosai.com/
upay-prepaid-card.server=http://upay-prepaid-card.beta.iwosai.com/


core-business.server=http://core-business.beta.iwosai.com/
app-backend.server=http://app-backend-service.beta.iwosai.com/
upay-task-center.server=http://upay-task-center.iwosai.com/

merchant-user-service.server=http://merchant-user-service.beta.iwosai.com/

#ads coeus service
rpc.service.url.ads.coeus.service=http://coeus-nexus.beta.iwosai.com/

#profit sharing
profit-sharing.server=http://profit-sharing.beta.iwosai.com/

#shouqianba-withdra-service.server
shouqianba-withdraw-service.server=http://shouqianba-withdraw-service.beta.iwosai.com/


#clearance-service.server
clearance-service.server=http://clearance-service.beta.iwosai.com/

#credit-pay-backend
credit-pay-backend.server=http://credit-pay-backend.beta.iwosai.com/

upay-grayscale.server=http://upay-grayscale.beta.iwosai.com/

#upay-trade
upay-trade.server=http://upay-trade.beta.iwosai.com/

#upay-cashdesk
upay-cashdesk.server=http://upay-cashdesk.beta.iwosai.com/

#marketing-saas-merchant-facade
marketing-saas-merchant-facade.server=http://marketing-saas-merchant-facade.beta.iwosai.com/

#transaction-report
transaction-report.server=http://transaction-report.beta.iwosai.com/

#merchant-center
merchant-center.server=http://merchant-center.beta.iwosai.com/
merchant-center.connection_timeout=1500
merchant-center.read_timeout=5000

#trade_manage
trade_manage.server=http://trade-manage-service.beta.iwosai.com/
trade_manage.server.connection_timeout=1500
trade_manage.server.read_timeout=5000

#merchant-contract-job
merchant-contract-job.server=http://merchant-contract-job.beta.iwosai.com
merchant-contract-job.server.connection_timeout=1500
merchant-contract-job.server.read_timeout=5000

#crm-customer-relation
crm-customer-relation.server=http://crm-customer-relation.beta.iwosai.com/
crm-customer-relation.connection_timeout=1500
crm-customer-relation.read_timeout=5000

#brand-business
jsonrpc.brand-business.server = http://brand-business.beta.iwosai.com/
brand-business.connection_timeout=1500
brand-business.read_timeout=5000

#redis
redis.url=r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
redis.port=6379
redis.database=14
redis.password=roFXzHwXPY3RnI%5







spring.application.name = upay-transaction

upay_swipe.prefix=7892

#solr
solr.zkHost=hb-8vbbm6rvs2k1olp39-master1-001.hbase.zhangbei.rds.aliyuncs.com:2181,hb-8vbbm6rvs2k1olp39-master2-001.hbase.zhangbei.rds.aliyuncs.com:2181,hb-8vbbm6rvs2k1olp39-master3-001.hbase.zhangbei.rds.aliyuncs.com:2181/solr
solr.timeout=10000
solr.swipe.tx.collection=upay_swipe_tx
solr.swipe.o.collection=upay_swipe_order

#hbase
hbase.zkHost=hb-8vbbm6rvs2k1olp39-master1-001.hbase.zhangbei.rds.aliyuncs.com:2181,hb-8vbbm6rvs2k1olp39-master2-001.hbase.zhangbei.rds.aliyuncs.com:2181,hb-8vbbm6rvs2k1olp39-master3-001.hbase.zhangbei.rds.aliyuncs.com:2181
# 扫码交易是按照月份分表，值不要设定
hbase.tx.tableName=
hbase.o.tableName=
# 刷卡交易hbase名称，需指定表名称，值不能删除
hbase.swipe.tx.tableName=upay:swipe_transaction
hbase.swipe.o.tableName=upay:swipe_order

lindorm.zkHost=ld-bp1ndbr1snq29xfr7-proxy-lindorm.lindorm.rds.aliyuncs.com:30020
lindorm.solr.zkHost=ld-bp1ndbr1snq29xfr7-proxy-zk.lindorm.rds.aliyuncs.com:2181/solr

#es
es.instance=es-cn-oew23jmo60057yene
es.hostname=es-cn-oew23jmo60057yene.elasticsearch.aliyuncs.com
es.port=9200
es.index_name=binlog_tx
es.connect-timeout=3000
es.socket-timeout=3000
es.connection-request-timeout=3000
es.max-connect-total=100
es.max-connect-per-route=100

#es
tradeMemo.es.instance=es-cn-oew23jmo60057yene
tradeMemo.es.hostname=es-cn-oew23jmo60057yene.elasticsearch.aliyuncs.com
tradeMemo.es.port=9200
tradeMemo.es.index_name=memo_tx
tradeMemo.es.connect-timeout=3000
tradeMemo.es.socket-timeout=3000
tradeMemo.es.connection-request-timeout=3000
tradeMemo.es.max-connect-total=100
tradeMemo.es.max-connect-per-route=100

tradeMemo.kafka.bootstrap-servers=aliyun-beta-kafka-01.iwosai.com:9092,aliyun-beta-kafka-02.iwosai.com:9092,aliyun-beta-kafka-03.iwosai.com:9092
tradeMemo.kafka.consumer.registry-url=http://aliyun-beta-schema-01.iwosai.com:8081,http://aliyun-beta-schema-02.iwosai.com:8081,http://aliyun-beta-schema-03.iwosai.com:8081
tradeMemo.kafka.consumer.group-id=upay-transaction
tradeMemo.kafka.consumer.topic=events_PAY_tradeMemo-sync
tradeMemo.kafka.consumer.max-poll-records=20
tradeMemo.kafka.consumer.auto.commit.interval.ms=1000

trade.kafka.bootstrap-servers=**************:9092,**************:9092,**************:9092
trade.kafka.consumer.registry-url=http://**************:8081,http://**************:8081,http://**************:8081
trade.kafka.consumer.group-id=upay-transaction
trade.kafka.consumer.topic=trade
trade.kafka.consumer.max-poll-records=20
trade.kafka.consumer.auto.commit.interval.ms=1000

mq.consumer.enabled=true

logback.rootAppender=FT_FILE

mail.host=smtpdm.aliyun.com
mail.port=465
mail.protocol=smtp
mail.properties.mail.smtp.auth=true
mail.properties.mail.smtp.starttls.enable=true
mail.properties.mail.smtp.quitwait=false
mail.properties.mail.smtp.ssl.enable=true
mail.properties.mail.imap.ssl.socketFactory.fallback=false
mail.properties.mail.smtp.ssl.socketFactory.class=com.fintech.modules.base.util.mail.MailSSLSocketFactory
mail.properties.mail.smtp.connectiontimeout=3000
mail.properties.mail.smtp.timeout=15000
mail.properties.mail.smtp.writetimeout=15000
mail.properties.mail.debug=false
mail.properties.default-encoding=UTF-8

config.deposit.reflect.rsa_key.id=f017ec9f-8776-4300-833b-626f4af681a6

odps.endPoint=http://service.odps.aliyun.com/api
odps.projectName=wosai_main_dev