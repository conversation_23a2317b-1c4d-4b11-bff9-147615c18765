<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd

        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!-- ========================= RESOURCE DEFINITIONS ========================= -->

    <!-- import the dataSource definition -->
    <import resource="datasource-config.xml"/>

    <bean id="statementJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <constructor-arg ref="statementDatasource"/>
    </bean>


    <bean id="statementConfigDao" class="com.wosai.data.dao.common.TimedDaoBase">
        <constructor-arg>
            <bean class="com.wosai.upay.common.dao.JsonBlobAwareDao">
                <constructor-arg index="0">
                    <bean class="com.wosai.data.dao.jdbc.JdbcDaoBase">
                        <constructor-arg index="0" value="statement_config"/>
                        <constructor-arg index="1" ref="statementJdbcTemplate"/>
                    </bean>
                </constructor-arg>
                <constructor-arg index="1">
                    <set>
                        <value>extra_info</value>
                    </set>
                </constructor-arg>
            </bean>
        </constructor-arg>
    </bean>



</beans>