<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
			   http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- We use the below config to resolve a view returned by a controller method.
         Note JSON response body returned by a rest service method DOES NOT need to go
         through this mess.
    -->

    <!--
        - The ContentNegotiatingViewResolver delegates to the InternalResourceViewResolver and BeanNameViewResolver,
        - and uses the requested media type (determined by the path extension) to pick a matching view.
        - When the media type is 'text/html', it will delegate to the InternalResourceViewResolver's JstlView,
        - otherwise to the BeanNameViewResolver.
    -->
    <bean class="org.springframework.web.accept.ContentNegotiationManagerFactoryBean">
        <property name="favorPathExtension" value="true"/>
        <property name="ignoreAcceptHeader" value="false"/>

        <property name="defaultContentType" value="application/json"/>

        <property name="mediaTypes">
            <map>
                <entry key="html" value="text/html"/>
                <entry key="xml" value="application/xml"/>
                <entry key="atom" value="application/atom+xml"/>
                <entry key="xls" value="application/vnd.ms-excel"/>
            </map>
        </property>

    </bean>

</beans>
