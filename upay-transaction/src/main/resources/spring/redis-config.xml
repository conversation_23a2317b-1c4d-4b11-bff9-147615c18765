<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:redisson="http://redisson.org/schema/redisson"
       xsi:schemaLocation=
               "http://www.springframework.org/schema/beans
                http://www.springframework.org/schema/beans/spring-beans.xsd
                http://redisson.org/schema/redisson
                http://redisson.org/schema/redisson/redisson.xsd">

    <!-- redis template definition -->
    <!-- 配置Jedis的 缓冲池 -->
    <bean id="JedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig"
          p:maxTotal="64"
          p:maxIdle="6"
          p:maxWaitMillis="1000"
          p:minEvictableIdleTimeMillis="300000"
          p:numTestsPerEvictionRun="3"
          p:timeBetweenEvictionRunsMillis="60000">
    </bean>
    <bean id="jedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory"
          p:poolConfig-ref="JedisPoolConfig"
          p:hostName="${redis.url}"
          p:port="${redis.port}"
          p:password="${redis.password}"
          p:database="${redis.database}"
          p:timeout="500"
          p:usePool="true">
    </bean>
    <!-- 配置 redisTemplate -->

    <bean id="redisTemplate" class="org.springframework.data.redis.core.StringRedisTemplate">
        <property name="connectionFactory" ref="jedisConnectionFactory"/>
    </bean>

    <bean id="objectRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="jedisConnectionFactory"/>
        <property name="keySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"></bean>
        </property>
        <property name="valueSerializer">
            <bean class="com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer"></bean>
        </property>
        <property name="hashKeySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"></bean>
        </property>
        <property name="hashValueSerializer">
            <bean class="com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer"></bean>
        </property>
    </bean>

    <bean id="redissonKryoCodec" class="org.redisson.codec.KryoCodec"/>

    <redisson:client id="redissonClient" codec-ref="redissonKryoCodec">
        <redisson:single-server
                address="redis://${redis.url}:${redis.port}"
                password="${redis.password}"
                database="${redis.database}"
                timeout="500"
                connection-pool-size="32"
                ping-timeout="30000"
        />
    </redisson:client>
</beans>