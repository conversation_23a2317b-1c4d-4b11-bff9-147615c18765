#upay_transaction
jdbc_transaction.driverClassName=com.mysql.cj.jdbc.Driver
jdbc_transaction.url=pk-upay-transaction-lindorm-upay_transaction-9543
jdbc_transaction.connection.eviction.interval=60000

#db config
db.maxActive=200
db.minIdle=8

# my
upay-picture.server=http://shouqianba-picture-service/

upay-prepaid-card.server=http://upay-prepaid-card/

#trade_manage
trade_manage.server=http://trade-manage-service/
trade_manage.server.connection_timeout=500
trade_manage.server.read_timeout=5000


core-business.server=http://app-core-business/

#app-backend server
app-backend.server=http://app-backend-service/


merchant-user-service.server=http://merchant-user-service/
#ads coeus service
rpc.service.url.ads.coeus.service=http://coeus-nexus/
#profit sharing
profit-sharing.server=http://profit-sharing/
#shouqianba-withdra-service
shouqianba-withdraw-service.server=http://withdraw-service.internal.shouqianba.com/

#clearance-service.server
clearance-service.server=http://clearance-service.internal.shouqianba.com/

#upay-trade
upay-trade.server=http://upay-trade/

upay-grayscale.server=http://upay-grayscale/

upay-task-center.server=http://upay-task-center/

#upay-cashdesk
upay-cashdesk.server=http://upay-cashdesk/

#marketing-saas-merchant-facade
marketing-saas-merchant-facade.server=http://marketing-saas-merchant-facade/

#transaction-report
transaction-report.server=http://transaction-report/

#merchant-center
merchant-center.server=http://merchant-center/
merchant-center.connection_timeout=500
merchant-center.read_timeout=5000

#merchant-contract-job
merchant-contract-job.server=http://merchant-contract-job
merchant-contract-job.server.connection_timeout=500
merchant-contract-job.server.read_timeout=5000

#crm-customer-relation
crm-customer-relation.server=http://crm-customer-relation/
crm-customer-relation.connection_timeout=500
crm-customer-relation.read_timeout=5000

#brand-business
jsonrpc.brand-business.server = http://brand-business/
brand-business.connection_timeout=500
brand-business.read_timeout=5000

#redis
redis.url=r-bp192915766f85a4.redis.rds.aliyuncs.com
redis.port=6379
redis.database=13
redis.password=Ud9YAFb1jRoAYOG3OVS5

# tracing
spring.application.name = upay-transaction


upay_swipe.prefix=7896

#solr
solr.zkHost=ld-bp153xexe6i47mo8c-proxy-zk.lindorm.rds.aliyuncs.com:2181/solr
solr.timeout=3000
solr.swipe.tx.collection=upay_swipe_tx
solr.swipe.o.collection=upay_swipe_order

#hbase
hbase.zkHost=ld-bp153xexe6i47mo8c-proxy-lindorm.lindorm.rds.aliyuncs.com:30020
# 扫码交易是按照月份分表，值不要设定
hbase.tx.tableName=
hbase.o.tableName=
# 刷卡交易hbase名称，需指定表名称，值不能删除
hbase.swipe.tx.tableName=upay:swipe_transaction
hbase.swipe.o.tableName=upay:swipe_order

lindorm.zkHost=hb-bp1f0wlcn95s024ex-master3-001.hbase.rds.aliyuncs.com:2181,hb-bp1f0wlcn95s024ex-master2-001.hbase.rds.aliyuncs.com:2181,hb-bp1f0wlcn95s024ex-master1-001.hbase.rds.aliyuncs.com:2181
lindorm.solr.zkHost=hb-bp1f0wlcn95s024ex-master3-001.hbase.rds.aliyuncs.com:2181,hb-bp1f0wlcn95s024ex-master2-001.hbase.rds.aliyuncs.com:2181,hb-bp1f0wlcn95s024ex-master1-001.hbase.rds.aliyuncs.com:2181/solr

#es
es.instance=es-cn-tl32s69zz001gji72
es.hostname=es-cn-tl32s69zz001gji72.elasticsearch.aliyuncs.com
es.port=9200
es.index_name=binlog_tx
es.connect-timeout=1500
es.socket-timeout=1500
es.connection-request-timeout=1500
es.max-connect-total=100
es.max-connect-per-route=100

#es
tradeMemo.es.instance=es-cn-wuf3w7lj7000245ad
tradeMemo.es.hostname=es-cn-wuf3w7lj7000245ad.elasticsearch.aliyuncs.com
tradeMemo.es.port=9200
tradeMemo.es.index_name=memo_tx
tradeMemo.es.connect-timeout=3000
tradeMemo.es.socket-timeout=3000
tradeMemo.es.connection-request-timeout=3000
tradeMemo.es.max-connect-total=100
tradeMemo.es.max-connect-per-route=100

tradeMemo.kafka.bootstrap-servers=aliyun-kafka-01.shouqianba.com:9092,aliyun-kafka-02.shouqianba.com:9092,aliyun-kafka-03.shouqianba.com:9092
tradeMemo.kafka.consumer.registry-url=http://aliyun-schema-01.shouqianba.com:8081,http://aliyun-schema-02.shouqianba.com:8081,http://aliyun-schema-03.shouqianba.com:8081
tradeMemo.kafka.consumer.group-id=upay-transaction
tradeMemo.kafka.consumer.topic=events_PAY_tradeMemo-sync
tradeMemo.kafka.consumer.max-poll-records=20
tradeMemo.kafka.consumer.auto.commit.interval.ms=1000

trade.kafka.bootstrap-servers=conflunt-kafka-01.shouqianba.com:9092,conflunt-kafka-02.shouqianba.com:9092,conflunt-kafka-03.shouqianba.com:9092,conflunt-kafka-04.shouqianba.com:9092,conflunt-kafka-05.shouqianba.com:9092
trade.kafka.consumer.registry-url=http://conflunt-schema-01.shouqianba.com:8081,http://conflunt-schema-02.shouqianba.com:8081,http://conflunt-schema-03.shouqianba.com:8081,http://conflunt-schema-04.shouqianba.com:8081,http://conflunt-schema-05.shouqianba.com:8081
trade.kafka.consumer.group-id=upay-transaction
trade.kafka.consumer.topic=events.upay.trade
trade.kafka.consumer.max-poll-records=20
trade.kafka.consumer.auto.commit.interval.ms=1000

mq.consumer.enabled=true

logback.rootAppender=FT_CONSOLE_JSON

mail.host=smtpdm.aliyun.com
mail.port=465
mail.protocol=smtp
mail.properties.mail.smtp.auth=true
mail.properties.mail.smtp.starttls.enable=true
mail.properties.mail.smtp.quitwait=false
mail.properties.mail.smtp.ssl.enable=true
mail.properties.mail.imap.ssl.socketFactory.fallback=false
mail.properties.mail.smtp.ssl.socketFactory.class=com.fintech.modules.base.util.mail.MailSSLSocketFactory
mail.properties.mail.smtp.connectiontimeout=3000
mail.properties.mail.smtp.timeout=15000
mail.properties.mail.smtp.writetimeout=15000
mail.properties.mail.debug=false
mail.properties.default-encoding=UTF-8

config.deposit.reflect.rsa_key.id=f017ec9f-8776-4300-833b-626f4af681a6

odps.endPoint=http://service.cn.maxcompute.aliyun-inc.com/api
odps.projectName=wosai_main