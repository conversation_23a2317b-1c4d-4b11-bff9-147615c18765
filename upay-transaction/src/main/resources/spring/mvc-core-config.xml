<?xml version="1.0" encoding="UTF-8"?>
<!--
MVC controllers, http converters, ...
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/mvc
        http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd">

    <import resource="mvc-view-config.xml"/>
    <context:annotation-config/>
    <context:component-scan
            base-package="com.hemayun.upay.transaction"/>


    <!-- Disable XML namespace configuration. -->
    <!-- 
    <mvc:annotation-driven conversion-service="conversionService">
        <mvc:message-converters register-defaults="true">
            <bean class="org.springframework.http.converter.ByteArrayHttpMessageConverter" />
            <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter" />
        </mvc:message-converters>
    </mvc:annotation-driven>
    -->
    <!-- Use Java class based configuration. -->
    <bean class="com.wosai.upay.common.config.WebConfig"/>

    <mvc:view-controller path="/" view-name="documentation"/>

    <bean id="conversionService" class="org.springframework.format.support.FormattingConversionServiceFactoryBean">
        <!--
        <property name="formatters">
          <set>
          </set>
        </property>
        <property name="converters">
          <set>
      </set>
        </property>
        -->
    </bean>

    <!--
        - This bean resolves specific types of exceptions to corresponding logical 
        - view names for error views.
    -->
    <!--
    <bean class="org.springframework.web.servlet.handler.SimpleMappingExceptionResolver">
        <property name="defaultErrorView" value="exception"/>
        <property name="warnLogCategory" value="warn"/>
    </bean>
    -->

    <!-- This bean provides custom exception resolution that renders the exception into JSON. -->
    <!--
    <bean class="com.wosai.springmvc.service.excepton.ExceptionResolver">
      <property name="order" value="0" />
    </bean>
    -->

    <!--
    <bean name="rpcErrorResolver" class="com.googlecode.jsonrpc4j.MultipleErrorResolver">
      <constructor-arg>
	<list>
	  <bean class="com.wosai.springmvc.rpc.ExceptionBaseErrorResolver" />
	  <bean class="com.googlecode.jsonrpc4j.DefaultErrorResolver" />
	</list>
      </constructor-arg>
    </bean>
	
    <bean name="/rpc/store" class="com.wosai.springmvc.rpc.SecuredJsonServiceExporter">
        <property name="service" ref="remoteStoreService"/>
        <property name="serviceInterface" value="com.wosai.springmvc.service.remote.RemoteStoreService"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
    </bean>
    -->

</beans>
