<?xml version="1.0" encoding="UTF-8"?>
<!--
Data and Service layers
-->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd

http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.1.xsd

			   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <bean class="com.wosai.upay.transaction.util.SpringUtil" />

    <bean class="com.wosai.upay.common.helper.UpayMethodValidationPostProcessor">
        <property name="validatedAnnotationType"
                  value="com.wosai.upay.transaction.annotation.CommonTransactionValidated"/>
    </bean>

    <bean id="serviceMethodInterceptor" class="com.wosai.upay.transaction.helper.UpayServiceMethodInterceptor"/>
    <bean class="com.wosai.upay.common.helper.CommonServicePostProcessor">
        <property name="advice" ref="serviceMethodInterceptor"/>
        <property name="annotationTypeClass" value="com.wosai.upay.transaction.annotation.CommonTransactionService"/>
    </bean>
    <bean id = "dBSelectServiceMethodInterceptor" class="com.wosai.upay.transaction.helper.DBSelectServiceMethodInterceptor"></bean>

    <bean class="com.wosai.upay.transaction.helper.DBSelectPostProcessor">
        <property name="advice" ref="dBSelectServiceMethodInterceptor"/>
    </bean>
    <bean class="org.springframework.web.servlet.handler.BeanNameUrlHandlerMapping"/>
    <bean class="com.wosai.upay.transaction.controller.ApolloConfigValidationController"/>

    <context:component-scan base-package="com.wosai.upay.transaction.service, com.wosai.upay.transaction.config"/>
    <mvc:annotation-driven />
    <mvc:default-servlet-handler />
    <mvc:interceptors>
        <bean class="com.wosai.upay.transaction.helper.UpayServiceHandlerInterceptor"/>
        <mvc:interceptor>
            <mvc:mapping path="/**"/>
            <bean class="com.wosai.upay.transaction.helper.UpayServiceHandlerInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>

    <bean id="myObjectMapper" class="com.wosai.upay.common.helper.MyObjectMapper"/>

    <bean id="healthController" class="com.wosai.upay.transaction.controller.HealthyController"/>
    <bean id="metricsController" class="com.wosai.upay.transaction.controller.MetricsController"/>

    <bean id="rpcErrorResolver" class="com.googlecode.jsonrpc4j.MultipleErrorResolver">
      <constructor-arg>
          <list value-type="com.googlecode.jsonrpc4j.ErrorResolver">
            <value type="com.wosai.upay.transaction.helper.ExceptionBaseErrorResolver">INSTANCE</value>
            <value type="com.googlecode.jsonrpc4j.DefaultErrorResolver">INSTANCE</value>
          </list>
      </constructor-arg>
  </bean>
    
    <!--<context:property-placeholder location="classpath:spring/flavor-${shouqianba.flavor:dev}.properties"/>-->

    <!-- spring的属性加载器，加载properties文件中的属性 -->
    <bean id="propertyConfigurer" class="com.wosai.upay.transaction.util.PropertyUtil">
        <property name="locations">
            <list>
                <value>classpath:spring/flavor-${shouqianba.flavor:default}.properties</value>
                <value>classpath:spring/avatar.properties</value>
            </list>
        </property>
    </bean>

    <import resource="jdbc-dao-config.xml"/>

    <import resource="redis-config.xml"/>

    <bean id="springContextHolder" class="com.wosai.upay.common.util.SpringContextHolder" lazy-init="false"/>

    <bean id="actionExecutor" class="java.util.concurrent.Executors" factory-method="newScheduledThreadPool">
        <constructor-arg value="20"/>
    </bean>

    <bean id="restTemplate" class="org.springframework.web.client.RestTemplate"></bean>
    <bean id="metaCacheUtil" class="com.wosai.upay.transaction.util.MetaCacheUtil"></bean>
    <bean class="com.wosai.upay.transaction.repository.DataRepository">
        <property name="statementConfigDao" ref="statementConfigDao"></property>
        <property name="statementJdbcTemplate" ref="statementJdbcTemplate"></property>
    </bean>
    <bean id="transactionService" class="com.wosai.upay.transaction.service.TransactionServiceImpl"></bean>
    <bean id="transactionServiceV2" class="com.wosai.upay.transaction.service.TransactionServiceV2Impl"></bean>
    <bean id="orderService" class="com.wosai.upay.transaction.service.OrderServiceImpl"/>
    <bean id="statementObjectConfigService" class="com.wosai.upay.transaction.service.StatementObjectConfigServiceImpl"></bean>
    <bean id="exportService" class="com.wosai.upay.transaction.service.ExportServiceImpl">
    </bean>
    <bean id="walletChangeLogHBaseService" class="com.wosai.upay.transaction.service.WalletChangeLogHBaseServiceImpl"></bean>
    <bean id="StatementConfigInterface" class="com.wosai.upay.transaction.service.StatementConfigServiceImpl"></bean>
    <bean id="transactionHBaseServic" class="com.wosai.upay.transaction.service.TransactionHBaseServiceImpl"></bean>
    <bean id="statementConfigService" class="com.wosai.upay.transaction.service.StatementConfigServiceImpl"></bean>
    <bean id="accountBookService" class="com.wosai.upay.transaction.service.api.AccountBookServiceImpl"></bean>
    <bean id="bizExportService" class="com.wosai.upay.transaction.service.api.BizExportServiceImpl"></bean>
    <bean id="accountBookServiceOpen" class="com.wosai.upay.transaction.service.api.AccountBookServiceOpenImpl"></bean>
    <bean id="orderServiceV2" class="com.wosai.upay.transaction.service.api.OrderServiceV2Impl"></bean>
    <bean id="taskLogService" class="com.wosai.upay.transaction.service.TaskLogServiceImpl"></bean>
    <bean id="businessService" class="com.wosai.upay.transaction.service.BusinessService"></bean>
    <bean id="ossFileUploaderService" class="com.wosai.upay.transaction.util.OssFileUploader"></bean>
    <bean id="cacheService" class="com.wosai.upay.transaction.service.CacheServiceImpl"></bean>
    <bean id="upayOrderService" class="com.wosai.upay.transaction.service.UpayOrderServiceImpl"/>
    <bean id="gatewaySupportService" class="com.wosai.upay.transaction.service.GatewaySupportServiceImpl"/>
    <bean id="crmService" class="com.wosai.upay.transaction.service.CrmServiceImpl"/>
    <bean id="checkService" class="com.wosai.upay.transaction.service.CheckServiceImpl"/>
    <bean id="crossMerchantService" class="com.wosai.upay.transaction.service.CrossMerchantServiceImpl"/>
    <bean id="aopRpc" class="com.wosai.upay.transaction.rpc.impl.AopRpcImpl" />
    <bean id="customerTransactionRpc" class="com.wosai.upay.transaction.rpc.impl.CustomerTransactionRpcImpl" />
    <bean id="backendUpayTerminalService" class="com.wosai.upay.transaction.service.BackendUpayTerminalServiceImpl"></bean>
    <bean name="/rpc/terminal" class="com.googlecode.jsonrpc4j.spring.JsonServiceExporter">
        <property name="service" ref="backendUpayTerminalService"/>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.BackendUpayTerminalService"/>
        <property name="errorResolver" ref="rpcErrorResolver"></property>
    </bean>

<!--    my-->
    <bean id="qrcodeImagedown" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-picture.server}rpc/image"></property>
        <property name="serviceInterface" value="com.wosai.upay.picture.service.ImageBuildService"></property>
        <property name="serverName" value="upay-picture-service"/>
        <property name="connectionTimeoutMillis" value="${upay-picture.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-picture.read_timeout:5000}"/>
    </bean>

    <bean id="prepaidStatementService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-prepaid-card.server}rpc/statement"></property>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidStatementService"></property>
        <property name="serverName" value="upay-prepaid-card.server"/>
        <property name="connectionTimeoutMillis" value="${upay-picture.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-picture.read_timeout:5000}"/>
    </bean>

    <bean id="prepaidIssuerService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-prepaid-card.server}rpc/issuer"></property>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidIssuerService"></property>
        <property name="serverName" value="upay-prepaid-card.server"/>
        <property name="connectionTimeoutMillis" value="${upay-picture.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-picture.read_timeout:5000}"/>
    </bean>


    <bean id="prepaidMerchantService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-prepaid-card.server}rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.upay.prepaid.api.PrepaidMerchantService"></property>
        <property name="serverName" value="upay-prepaid-card.server"/>
        <property name="connectionTimeoutMillis" value="${upay-picture.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-picture.read_timeout:5000}"/>
    </bean>

    <bean id="merchantUserService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}/rpc/merchantuser"></property>
        <property name="serviceInterface" value="com.wosai.app.service.MerchantUserService"></property>
        <property name="serverName" value="merchant-user-service"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>


    <bean id="externalMerchantService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/merchant"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MerchantService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="externalTradeConfigService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/tradeConfig"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TradeConfigService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="externalBusinssCommonService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/common"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.BusinssCommonService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>


    <bean id="externalStoreService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.StoreService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="merchantCenterStoreService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-center.server}rpc/store"></property>
        <property name="serviceInterface" value="com.wosai.mc.service.StoreService"></property>
        <property name="serverName" value="merchant-center"/>
        <property name="connectionTimeoutMillis" value="${merchant-center.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-center.read_timeout:5000}"/>
    </bean>

    <bean id="externalTerminalService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/terminal"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.TerminalService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="externalMetaService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/meta"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MetaService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="externalChangeShiftsService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/changeShifts"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.ChangeShiftsService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="supportService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/support"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.SupportService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id="merchantUserGroupService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}rpc/group"></property>
        <property name="serviceInterface" value="com.wosai.app.service.GroupService"></property>
        <property name="serverName" value="merchant-user-service"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>

    <bean id="merchantUserDepartmentService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}rpc/department"></property>
        <property name="serviceInterface" value="com.wosai.app.service.DepartmentService"></property>
        <property name="serverName" value="merchant-user-service"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>

<!--    upay-task-center-api -->

    <bean id="taskExportService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-task-center.server}rpc/export"></property>
        <property name="serviceInterface" value="com.wosai.upay.task.center.service.ExportService"></property>
        <property name="serverName" value="upay-task-center"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>

    <bean id="taskTaskLogService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-task-center.server}rpc/task"></property>
        <property name="serviceInterface" value="com.wosai.upay.task.center.service.TaskLogService"></property>
        <property name="serverName" value="upay-task-center"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>


    <bean id="taskStatementObjectConfigService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-task-center.server}rpc/statementObjectConfig"></property>
        <property name="serviceInterface" value="com.wosai.upay.task.center.service.StatementObjectConfigService"></property>
        <property name="serverName" value="upay-task-center"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>



    <bean id = "formService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-trade.server}rpc/form"></property>
        <property name="serviceInterface" value="com.wosai.upay.trade.api.FormService"></property>
        <property name="serverName" value="upay-trade"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>

    <bean id = "formpayService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-trade.server}rpc/formpay"></property>
        <property name="serviceInterface" value="com.wosai.upay.trade.api.FormpayService"></property>
        <property name="serverName" value="upay-trade"/>
        <property name="connectionTimeoutMillis" value="${upay-trade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-trade.read_timeout:10000}"/>
    </bean>

    <bean id = "cashDeskService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/cash_desk"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.CashDeskService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id = "cashDeskTradeService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-cashdesk.server}rpc/cashdesk"></property>
        <property name="serviceInterface" value="com.wosai.upay.cashdesk.api.service.CashDeskTradeService"></property>
        <property name="serverName" value="upay-cashdesk"/>
        <property name="connectionTimeoutMillis" value="500"/>
        <property name="readTimeoutMillis" value="5000"/>
    </bean>

    <bean id = "appMerchantServie" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${app-backend.server}rpc/merchantconfig"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IMerchantService"></property>
        <property name="serverName" value="app-backend-process"/>
        <property name="connectionTimeoutMillis" value="${app-backend.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${app-backend.read_timeout:5000}"/>
    </bean>

    <bean id="appStaffService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${app-backend.server}rpc/staff"></property>
        <property name="serviceInterface" value="com.wosai.app.backend.api.service.IStaffService"></property>
        <property name="serverName" value="app-backend-service"/>
        <property name="connectionTimeoutMillis" value="${app-backend.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${app-backend.read_timeout:5000}"/>
    </bean>

    <bean id="merchantUserServiceV2" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}/rpc/merchantuserV2"></property>
        <property name="serviceInterface" value="com.wosai.app.service.v2.MerchantUserServiceV2"></property>
        <property name="serverName" value="merchant-user-service"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>

    <bean id = "merchantUserDataPermissionsService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-user-service.server}rpc/merchantUser/dataPermissions"></property>
        <property name="serviceInterface" value="com.wosai.app.service.MerchantUserDataPermissionsService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${merchant-user-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-user-service.read_timeout:5000}"/>
    </bean>

    <bean id="nexusAccountService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${rpc.service.url.ads.coeus.service}rpc/nexus/account"></property>
        <property name="serviceInterface" value="com.wosai.ads.coeus.api.service.NexusAccountService"></property>
        <property name="serverName" value="ads-coeus"/>
        <property name="connectionTimeoutMillis" value="${rpc.service.url.ads.coeus.service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${rpc.service.url.ads.coeus.service.read_timeout:5000}"/>
    </bean>


    <bean id="receiverService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${profit-sharing.server}rpc/receiver"></property>
        <property name="serviceInterface" value="com.wosai.upay.transaction.service.remote.RemoteReceiverService"></property>
        <property name="serverName" value="profit-sharing"/>
        <property name="connectionTimeoutMillis" value="${profit-sharing.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${profit-sharing.read_timeout:60000}"/>
    </bean>

    <bean id="withdrawService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${shouqianba-withdraw-service.server}/rpc/withdraw"></property>
        <property name="serviceInterface" value="com.wosai.shouqianba.withdrawservice.service.WithdrawService"></property>
        <property name="serverName" value="shouqianba-withdraw-service"/>
        <property name="connectionTimeoutMillis" value="${shouqianba-withdraw-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${shouqianba-withdraw-service.read_timeout:60000}"/>
    </bean>
    <bean id="clearanceService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${clearance-service.server}/rpc/clearance"/>
        <property name="serviceInterface" value="com.wosai.upay.clearance.service.ClearanceService"/>
        <property name="serverName" value="clearance-service"/>
        <property name="connectionTimeoutMillis" value="${clearance-service.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${clearance-service.read_timeout:60000}"/>
    </bean>
    <bean class="com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter">
        <property name="objectMapper" ref="myObjectMapper"/>
        <property name="errorResolver" ref="rpcErrorResolver"/>
        <property name="interceptorList" ref="jsonRpcSentinelInterceptor"/>
    </bean>

    <bean id = "merchantGrayService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${upay-grayscale.server}rpc/merchantGray"></property>
        <property name="serviceInterface" value="com.wosai.service.IMerchantGrayService"></property>
        <property name="serverName" value="upay-grayscale"/>
        <property name="connectionTimeoutMillis" value="${upay-grayscale.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${upay-grayscale.read_timeout:3000}"/>
    </bean>

    <bean id = "customerService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${marketing-saas-merchant-facade.server}rpc/customer"></property>
        <property name="serviceInterface" value="com.wosai.market.saas.merchant.api.service.CustomerService"></property>
        <property name="serverName" value="marketing-saas-merchant-facade"/>
        <property name="connectionTimeoutMillis" value="${marketing-saas-merchant-facade.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${marketing-saas-merchant-facade.read_timeout:500}"/>
    </bean>

    <bean id = "accountReportService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${transaction-report.server}rpc/account_report_proxy_v2"></property>
        <property name="serviceInterface" value="com.wosai.service.IAccountReportServiceProxyV2"></property>
        <property name="serverName" value="transaction-report"/>
        <property name="connectionTimeoutMillis" value="${transaction-report.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${transaction-report.read_timeout:6000}"/>
    </bean>


    <bean id = "tradeComboService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${trade_manage.server}rpc/trade_combo_detail"></property>
        <property name="serviceInterface" value="com.wosai.trade.service.TradeComboDetailService"></property>
        <property name="serverName" value="trade_manage"/>
        <property name="connectionTimeoutMillis" value="${trade_manage.server.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${trade_manage.server.read_timeout:6000}"/>
    </bean>

    <bean id = "tradeAppService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${trade_manage.server}rpc/trade_app"></property>
        <property name="serviceInterface" value="com.wosai.trade.service.TradeAppService"></property>
        <property name="serverName" value="trade_manage"/>
        <property name="connectionTimeoutMillis" value="${trade_manage.server.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${trade_manage.server.read_timeout:6000}"/>
    </bean>


    <bean id = "metaService" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${core-business.server}rpc/meta"></property>
        <property name="serviceInterface" value="com.wosai.upay.core.service.MetaService"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${core-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${core-business.read_timeout:5000}"/>
    </bean>

    <bean id = "t9Service" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${merchant-contract-job.server}/rpc/t9"></property>
        <property name="serviceInterface" value="com.wosai.upay.job.service.T9Service"></property>
        <property name="serverName" value="core-business"/>
        <property name="connectionTimeoutMillis" value="${merchant-contract-job.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${merchant-contract-job.read_timeout:5000}"/>
    </bean>
    <bean id = "iCustomerRelationFacade" class="com.wosai.sentinel.sdk.jsonrpc.SentinelJsonProxyFactoryBean">
        <property name="serviceUrl" value="${crm-customer-relation.server}rpc/relation"></property>
        <property name="serviceInterface" value="facade.ICustomerRelationFacade"></property>
        <property name="serverName" value="crm-customer-relation"/>
        <property name="connectionTimeoutMillis" value="${crm-customer-relation.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${crm-customer-relation.read_timeout:5000}"/>
    </bean>


    <bean class="com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean">
        <property name="serviceUrl" value="${jsonrpc.brand-business.server}/rpc/brand"></property>
        <property name="serviceInterface" value="com.wosai.cua.brand.business.api.facade.BrandFacade"></property>
        <property name="serverName" value="brand-business"/>
        <property name="connectionTimeoutMillis" value="${brand-business.connection_timeout:500}"/>
        <property name="readTimeoutMillis" value="${brand-business.read_timeout:5000}"/>
    </bean>

    <bean id="jsonRpcSentinelInterceptor" class="com.wosai.upay.transaction.service.config.UpayTransactionJsonRpcInterceptor"/>
<!--    <bean id="elasticsearchProperties" class="com.wosai.upay.transaction.service.config.ElasticsearchProperties">-->
<!--        <property name="instance" value="${es.instance}"/>-->
<!--        <property name="hostname" value="${es.hostname}"/>-->
<!--        <property name="port" value="${es.port}"/>-->
<!--        <property name="connectTimeout" value="${es.connect-timeout}"/>-->
<!--        <property name="socketTimeout" value="${es.socket-timeout}"/>-->
<!--        <property name="connectionRequestTimeout" value="${es.connection-request-timeout}"/>-->
<!--        <property name="maxConnectTotal" value="${es.max-connect-total}"/>-->
<!--        <property name="maxConnectPerRoute" value="${es.max-connect-per-route}"/>-->
<!--    </bean>-->
<!--    <bean id="elasticsearchConfig" class="com.wosai.upay.transaction.service.config.ElasticsearchConfig"/>-->

<!--    <bean id="beanConfig" class="com.wosai.upay.transaction.service.config.BeanConfig"/>-->

    <bean class="com.wosai.upay.transaction.util.OrderUtil"></bean>
    <bean id="statementTransactionDetailUtil" class="com.wosai.upay.transaction.util.StatementTransactionUtil"/>
    <bean id="statementWalletUtils" class="com.wosai.upay.transaction.util.StatementWalletUtils"/>
    <bean id="withdrawExportUtils" class="com.wosai.upay.transaction.util.WithdrawExportUtils"/>


    <bean id="exportHandler" class="com.wosai.upay.transaction.export.base.ExportHandler"/>
    <bean class="com.wosai.upay.transaction.util.ApplicationContextUtil"/>
    <bean class="com.wosai.upay.transaction.util.OdpsUtil"/>

    <bean class="com.wosai.upay.transaction.service.remote.ElasticsearchService"/>

    <bean class="com.wosai.upay.transaction.util.SmtpMailSender">
        <property name="javaMailSender" ref="javaMailSender"></property>
    </bean>



    <!-- 等价于 @EnableAsync， executor指定线程池 -->
    <task:annotation-driven executor="asyncExecutor"/>
    <!-- id指定线程池产生线程名称的前缀 -->
    <task:executor
            id="asyncExecutor"
            pool-size="50-100"
            queue-capacity="100"
            keep-alive="120"
            rejection-policy="CALLER_RUNS"/>

</beans>
