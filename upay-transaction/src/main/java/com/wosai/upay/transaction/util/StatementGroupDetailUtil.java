package com.wosai.upay.transaction.util;

import com.google.common.collect.Lists;
import com.wosai.common.utils.transaction.TransactionEnhanceFields;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.export.base.RunContext;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.Transaction;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import static com.wosai.common.utils.transaction.Transaction.MCH_DISCOUNT_ORIGIN_TYPE;
import static com.wosai.upay.transaction.util.LanguageUtil.*;

/**
 * <AUTHOR>
 */
public class StatementGroupDetailUtil {


    public static SheetUtil initSheetAppendHeader(Map context, String sheetName) {
        SheetUtil sheetUtil = initSheet(sheetName);
        appendHeader(context, sheetUtil.getSheet());
        return sheetUtil;
    }

    public static SheetUtil initSheet(String sheetName) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(CommonConstant.ROW_ACCESS_WINDOW_SIZE);
        SXSSFSheet sheet = (SXSSFSheet) workbook.createSheet(sheetName);
        SheetUtil sheetUtil = new SheetUtil(sheet, workbook);
        return sheetUtil;

    }

    private static void appendHeader(Map context, SXSSFSheet details) {
        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");
        //是否是KA对账单
        boolean isKAStatement = RunContext.currentContext().getIsKAStatement();
        String groupSn = BeanUtil.getPropString(context, "group_sn");
        String groupName = BeanUtil.getPropString(context, "group_name");
        StatementCommonUtil.appendLine(details, getValues(WOSAI_TRANSACTION_DETAILS));
        StatementCommonUtil.appendLine(details, Arrays.asList(getValue(GROUP_NO) + ":" + groupSn));
        StatementCommonUtil.appendLine(details, Arrays.asList(getValue(GROUP_NAME) + ":" + groupName));
        StatementCommonUtil.appendLine(details, Arrays.asList(String.format(
                getValue(PERIOD) + ":[%s]—:[%s]",
                CommonConstant.DETAIL_SDF.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DETAIL_SDF.get().format(end) + CommonConstant.TIME_ZONE_TL.get()
        )));
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            StatementCommonUtil.appendLine(details, Arrays.asList("注意事项：每日15点更新昨日交易的手续费和结算金额，如需查看昨日交易的手续费和结算金额请于当日15点后下载对账单查询。"));
        }
        StatementCommonUtil.appendLine(details, Arrays.asList("#-----------------------------------------" + getValue(TRANSACTION_DETAILS_LIST) + "----------------------------------------#"));
        List<String> valueList = Lists.newArrayList(TRANSACTION_DATE, TIME, MERCHANT_SERIAL_NO, PAYMENT_TYPE,
                PRODUCT_NAME, MERCHANT_ORDER_NO, WOSAI_ORDER_NO, PAYMENT_TYPE_ORDER_NO, TRANSACTION_TYPE,
                TRANSACTION_STATUS, CONSUMER_ACCOUNT, CURRENCY, TRANSACTION_AMOUNT);
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY) {
            valueList.addAll(CommonConstant.NOT_BANK_CARD_HEAD_NAME);
            valueList.add(LanguageUtil.getValue(CHARGE_RATE) + "%");
        }

        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            //手续费”放在“实收金额”后面
            valueList.addAll(com.google.common.collect.Lists.newArrayList(PAID_AMOUNT, CHARGE));
        } else {
            valueList.addAll(com.google.common.collect.Lists.newArrayList(CHARGE, PAID_AMOUNT));
        }

        if (!isKAStatement) {
            valueList.add(TRADE_SERVICE_SHARING_AMOUNT); //技术服务费
        }
        valueList.addAll(Lists.newArrayList(DETAIL_SETTLEMENT_AMOUNT, MERCHANT_NO, MERCHANT_NAME, STORE_NO, MERCHANT_STORE_NO, STORE_NAME, TERMINAL_NO, MERCHANT_TERMINAL_NO, TERMINAL_NAME, TERMINAL_TYPE, DEVICE_ID, OPERATOR, CASHIER, REMARKS));

        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            valueList.addAll(CommonConstant.BANK_CARD_HEAD_NAME);
            //“刷卡完成时间”放在“交易日期”、“时间”后面
            valueList.add(2, LanguageUtil.CHANNEL_FINISH_TIME);
            //“操作员”、“收银员”删掉不显示
            valueList.remove(OPERATOR);
            valueList.remove(CASHIER);
            //“备注”放最后
            valueList.remove(REMARKS);
            valueList.add(REMARKS);
        }

        valueList.add(SHARING_FLAG);
        if (!isKAStatement) {
            valueList.add(MERCHANT_SHARING_AMOUNT); //分账金额（不含技术服务费）
        } else {
            valueList.add(SHARING_AMOUNT); //分账金额（含技术服务费）
        }
        valueList.add(MCH_DISCOUNT_ORIGIN_TYPE);
        valueList.add(SUBPAYWAY);
        if (!isKAStatement) {
            valueList.add(SMART_ORDER);
            valueList.add(SMART_FLOW_SERVICE);
            valueList.add(SMART_CAMPUS_DISTRIBUTION);
            valueList.add(SAAS_THIRD_DISTRIBUTION);
            valueList.add(SAAS_COUPON_SALE);
            valueList.add(SAAS_EQUITY_CARD_SALE);
            valueList.add(SAAS_CARD_RECHARGE);
            valueList.add(HBFQ_INDIRECT_TIEXI);
            valueList.add(PAY_AFTER_USE);
            valueList.add(SMART_FLOW_SERVICE_COMMISSION);
            valueList.add(SMART_FLOW_SERVICE_BASE_AMOUNT);
        }
        StatementCommonUtil.appendLine(details, getValues(valueList.toArray(new String[valueList.size()])));
    }


    /**
     * 生成对账单明细
     *
     * @param transactions
     * @param details
     * @return
     */
    public static void appendRows(List<Map<String, Object>> transactions, SXSSFSheet details) {
        boolean isKAStatement = RunContext.currentContext().getIsKAStatement();

        for (Map transaction : transactions) {
            int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);
            //不是对账单需要导出流水类型跳过
            if (!TransactionTypeRelatedUtil.STATEMENT_TYPES.contains(transactionType)) {
                continue;
            }
            long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
            long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
            long discountAmount = BeanUtil.getPropLong(transaction, Transaction.WOSAI_FAVORABLE_AMOUNT);//收钱吧优惠
            String currency = BeanUtil.getPropString(transaction, TransactionParam.CURRENCY, "CNY");
            String language = LanguageUtil.getLanguage();
            long channelDiscount = BeanUtil.getPropLong(transaction, Transaction.CHANNEL_AGENT_FAVORABLE_AMOUNT);
            long channelMchDiscount = BeanUtil.getPropLong(transaction, Transaction.CHANNEL_MCH_FAVORABLE_AMOUNT);
            long channelMchTopUpDiscount = BeanUtil.getPropLong(transaction, Transaction.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT);

            Long paidAmount = (Long) transaction.get(Transaction.PAID_AMOUNT);
            if (paidAmount == null) {
                paidAmount = effectiveAmount - (channelDiscount + channelMchDiscount + channelMchTopUpDiscount);
            }
            long actualReceiveAmount = BeanUtil.getPropLong(transaction, Transaction.ACTUAL_RECEIVE_AMOUNT);
            long clearingAmount = BeanUtil.getPropLong(transaction, Transaction.CLEARING_AMOUNT);
            long merchantDiscount = BeanUtil.getPropLong(transaction, Transaction.MCH_FAVORABLE_AMOUNT);
            long tradeFee = BeanUtil.getPropLong(transaction, Transaction.FEE);
            long sharingAmount = BeanUtil.getPropLong(transaction, Transaction.SHARING_AMOUNT, 0);
            long tradeServiceSharingAmount = BeanUtil.getPropLong(transaction, Transaction.TRADE_SERVICE_SHARING_AMOUNT, 0);
            Map sharingProfitDetail = StatementTransactionUtil.buildSharingProfitDetail(transaction);
            int sharingFlag = BeanUtil.getPropInt(transaction, TransactionEnhanceFields.SHARING_FLAG.getField());
            String subPayway = BeanUtil.getPropString(transaction, Transaction.SUB_PAYWAY);

            if (TransactionTypeRelatedUtil.isNegativeType(transactionType)) {
                originalAmount = originalAmount * -1;
                paidAmount = paidAmount * -1;
                discountAmount *= -1;
                actualReceiveAmount *= -1;
                clearingAmount *= -1;
                merchantDiscount *= -1;
                channelDiscount *= -1;
                channelMchDiscount *= -1;
                channelMchTopUpDiscount *= -1;
                tradeFee *= -1;
                sharingAmount *=-1;
                tradeServiceSharingAmount *=-1;
            }

            double feeRate = 0.0;
            String feeRateString = BeanUtil.getPropString(transaction, TransactionParam.FEE_RATE, "0.0");
            if (!feeRateString.trim().equals("")) {
                feeRate = CommonUtil.formatMoney(feeRateString, 3);
            }
            /*"交易日期", "时间", "商户流水号", "支付方式", "商品名",
                    "商户内部订单号", "商户订单号", "收款通道订单号", "交易类型", "交易状态", "付款账户", "货币类型",
                    "交易金额", "商户优惠", "收钱吧优惠", "收款通道机构优惠", "收款通道商户预充值优惠", "收款通道商户免充值优惠",
                    "消费者实付金额", "扣率%", "手续费", "实收金额", "结算金额", "门店号", "商户门店号", "门店名称", "终端号",
                    "商户终端号", "终端名称", "终端类型", "设备号", "操作员", "收银员"，"备注"，"交易模式" */
            List rowValue = new ArrayList();
            rowValue.add(CommonConstant.DAY_SDF.get().format(transaction.get(DaoConstants.CTIME)));
            rowValue.add(CommonConstant.TIME_SDF.get().format(transaction.get(DaoConstants.CTIME)));
            String clientTsn = null;
            String refundRequestNo = null;
            if (TransactionTypeRelatedUtil.isRefundType(transactionType)) {
                String client_tsn = BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN);
                if(StringUtils.hasText(client_tsn) && client_tsn.split(CommonConstant.PLACE_HOLDER).length == 2){
                    int index = client_tsn.lastIndexOf(CommonConstant.PLACE_HOLDER);
                    clientTsn = client_tsn.substring(0, index);
                    refundRequestNo = client_tsn.substring(index + 1);
                }
            }

            if (TransactionTypeRelatedUtil.isRefundType(transactionType) && refundRequestNo != null) {
                rowValue.add(refundRequestNo);
            } else {
                rowValue.add(BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN));
            }
            rowValue.add(OrderUtil.getPaywayDesc(transaction.get(Transaction.PAYWAY).toString(), currency, language));
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.SUBJECT));
            if (TransactionTypeRelatedUtil.isRefundType(transactionType) && clientTsn != null) {
                rowValue.add(clientTsn);
            } else {
                rowValue.add(BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN));
            }
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.TRADE_NO));
            rowValue.add(getValue(TransactionTypeRelatedUtil.getTransactionTypeLanguageKeyIgnoreDeposit(transactionType)));
            rowValue.add(getValue(OrderUtil.getTransactionStatusLanguageKey(BeanUtil.getPropString(transaction, Transaction.STATUS))));

            int payWay = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
            String buyerUid = BeanUtil.getPropString(transaction, Transaction.BUYER_UID);
            String buyerLogin = BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN);
            if(payWay == 3){
                rowValue.add(StringUtils.hasText(buyerUid)? buyerUid : buyerLogin);
            }else {
                rowValue.add(buyerLogin);
            }

            rowValue.add(currency);
            rowValue.add(originalAmount / 100.0);

            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY) {
                rowValue.add(merchantDiscount / 100.0);
                rowValue.add(discountAmount / 100.0);//收钱吧优惠
                rowValue.add(channelDiscount / 100.0);//收款通道机构优惠
                rowValue.add(channelMchTopUpDiscount / 100.0);
                rowValue.add(channelMchDiscount / 100.0);
                rowValue.add(paidAmount / 100.0);//消费者实付金额
                rowValue.add(feeRate);
            }

            //银行卡excel “手续费”放在“实收金额”后面
            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
                //实收金额
                rowValue.add(actualReceiveAmount / 100.0);
                rowValue.add(tradeFee / 100.0);
            } else {
                rowValue.add(tradeFee / 100.0);
                //实收金额
                rowValue.add(actualReceiveAmount / 100.0);
            }
            if (!isKAStatement) {
                rowValue.add(tradeServiceSharingAmount / 100.0);
            }
            // 结算金额
            rowValue.add(clearingAmount / 100.0);
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_MERCHANT_SN));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_MERCHANT_NAME));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_STORE_SN));
            rowValue.add(BeanUtil.getPropString(transaction, "store_client_sn"));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_STORE_NAME));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_TERMINAL_SN));
            rowValue.add(BeanUtil.getPropString(transaction, "terminal_client_sn"));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_TERMINAL_NAME));
            rowValue.add(getValue(OrderUtil.getTerminalTypeLanguageKey(BeanUtil.getPropString(transaction, "terminal_type"))));
            rowValue.add(BeanUtil.getPropString(transaction, "terminal_device_fingerprint"));
            String reflect = "";
            if (Transaction.TYPE_DEPOSIT_CONSUME == transactionType) {
                reflect = TransactionType.DEPOSIT_CONSUME.getDesc();
            }

            if (DBSelectContext.getContext().getSelectDb() != UpayQueryType.UPAY_SWIPE) {
                rowValue.add(BeanUtil.getPropString(transaction, Transaction.OPERATOR));
                rowValue.add(BeanUtil.getPropString(transaction, "operator_name"));
                rowValue.add(reflect + BeanUtil.getPropString(transaction, Transaction.REFLECT, ""));
            }

            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.PAY_TYPE, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.BATCH_BILL_NO, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.SYS_TRACE_NO, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.BANK_TYPE, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.LAKALA_MERC_ID, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.LAKALA_TERM_ID, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.TRADE_NO, ""));
                String channelFinishTime = BeanUtil.getPropString(transaction, Transaction.CHANNEL_FINISH_TIME);
                if (StringUtils.hasText(channelFinishTime)) {
                    rowValue.add(2, CommonConstant.DETAIL_SDF.get().format(Long.parseLong(channelFinishTime)));
                } else {
                    rowValue.add(2, "");
                }            }

            rowValue.add(sharingFlag == 1 ? "是" : "否");
            if (!isKAStatement) {
                // 分账金额
                rowValue.add(sharingAmount / 100.0);
            } else {
                rowValue.add((sharingAmount + tradeServiceSharingAmount) / 100.0);
            }
            rowValue.add(BeanUtil.getPropString(transaction,MCH_DISCOUNT_ORIGIN_TYPE, ""));
            rowValue.add(SubPaywayUtils.getSubPaywayDesc(subPayway));
            if (!isKAStatement) {
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_ORDER) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_ORDER) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_FLOW_SERVICE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_FLOW_SERVICE) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_CAMPUS_DISTRIBUTION) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_CAMPUS_DISTRIBUTION) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_THIRD_DISTRIBUTION) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_THIRD_DISTRIBUTION) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_COUPON_SALE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_COUPON_SALE) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_EQUITY_CARD_SALE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_EQUITY_CARD_SALE) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_CARD_RECHARGE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_CARD_RECHARGE) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.HBFQ_INDIRECT_TIEXI) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.HBFQ_INDIRECT_TIEXI) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.PAY_AFTER_USE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.PAY_AFTER_USE) / 100.0);

                ImmutablePair<Long, Long> receiverBusinessInfo = StatementTransactionUtil.getReceiverBusinessInfo(transaction);
                //固定佣金、 起步价
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? receiverBusinessInfo.getLeft() * -1 / 100.0 : receiverBusinessInfo.getLeft() / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? receiverBusinessInfo.getRight() * -1 / 100.0 : receiverBusinessInfo.getRight() / 100.0);
            }
            StatementCommonUtil.appendLine(details, rowValue);

        }
        // StatementCommonUtil.appendLine(details, Arrays.asList("#-----------------------------------------交易明细列表结束----------------------------------------#"));
    }

}

