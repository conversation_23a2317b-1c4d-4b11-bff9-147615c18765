package com.wosai.upay.transaction.service.api;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.service.BizExportService;
import com.wosai.upay.transaction.service.ExportService;
import com.wosai.upay.transaction.service.TaskLogService;
import com.wosai.upay.transaction.util.ApolloUtil;
import com.wosai.upay.transaction.util.DateTimeUtil;
import com.wosai.upay.transaction.util.EmailUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2022/11/8 5:30 下午
 */
@AutoJsonRpcServiceImpl
public class BizExportServiceImpl implements BizExportService {
    @Autowired
    private ExportService exportService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private TradeConfigService tradeConfigService;




    @Override
    public void survey(Map<String, Object> param) {
        long start = BeanUtil.getPropLong(param, ConstantUtil.KEY_DATE_START);
        long end = BeanUtil.getPropLong(param, ConstantUtil.KEY_DATE_END);
        String email = BeanUtil.getPropString(param, "email");

        //时间 17天
        if (start > end) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "开始时间需早于结束时间");
        }
        if (end - start > 17 * DateTimeUtil.ONE_DAY_MILLIS) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "时间范围不支持");
        }
        if (System.currentTimeMillis() - end > 17 * DateTimeUtil.ONE_DAY_MILLIS) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "时间范围不支持");
        }

        //邮箱格式
        boolean check = EmailUtil.check(email);
        if(!check){
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "邮箱格式不正确");
        }

        String merchantId = BeanUtil.getPropString(param, ConstantUtil.KEY_MERCHANT_ID);
        List<Map> merchantTradeConfig = tradeConfigService.getMerchantConfigsByMerchantId(merchantId);
        Set<Integer> filterProvider = ApolloUtil.getFilterProvider();
        if(merchantTradeConfig.stream().filter(o -> filterProvider.contains(MapUtils.getIntValue(o, MerchantConfig.PROVIDER, -1))).count() != 0){
            //如果商户有payway在需要屏蔽的通道下，就不允许导出
            throw new BizException(BizException.CODE_BIZ_EXCEPTION, "暂不支持导出，请引导流调人员联系收钱吧客服400-886-9999查询相关数据");
        }
        //进行中的任务
        List<Integer> statusIng = Arrays.asList(0, 1);
        Map<String, Object> queryFilter = CollectionUtil.hashMap(
                StatementTaskLog.APPLY_STATUSES, statusIng,
                StatementTaskLog.USER_ID,merchantId,
                StatementTaskLog.TYPE, StatementTaskLog.TYPE_SURVEY);
        ListResult result = taskLogService.findTaskApplyLogs(null, queryFilter);
        if (result.getTotal() > 0) {
            throw new BizException(BizException.CODE_INVALID_PARAMETER, "您的上一份流调数据还在生成中，请稍后再试");
        }

        String startString = DateTimeUtil.getSimpleTimeString(start);
        String endString = DateTimeUtil.getSimpleTimeString(end);
        Map merchant = merchantService.getMerchant(merchantId);
        String merchantName = BeanUtil.getPropString(merchant, ConstantUtil.KEY_NAME);
        String title = merchantName + "_流调数据导出(" + startString + "_" + endString + ")";

        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put(StatementTaskLog.EXTRA_TO, Collections.singletonList(email));
        extraMap.put(StatementTaskLog.EXTRA_STATUS, StatementTaskLog.EXTRA_STATUS_INIT);

        Map<String, Object> taskInfo = CollectionUtil.hashMap(
                StatementTaskLog.TYPE, StatementTaskLog.TYPE_SURVEY,
                StatementTaskLog.APPLY_SYSTEM, StatementTaskLog.APPLY_SYSTEM_UNKNOWN,
                StatementTaskLog.TITLE, title,
                StatementTaskLog.USER_ID, merchantId,
                StatementTaskLog.FINISH_OPERATION, StatementTaskLog.FINISH_OPERATION_EMAIL,
                StatementTaskLog.OPERATION_EXTRA, extraMap
        );

        exportService.createExportStatementTask(taskInfo, param);
    }

}
