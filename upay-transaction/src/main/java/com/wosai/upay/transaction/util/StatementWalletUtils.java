package com.wosai.upay.transaction.util;

import com.wosai.common.enums.WalletType;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.constant.CommonConstant;

import com.wosai.upay.transaction.constant.WalletConstant;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import java.util.*;

/**
 * @Date: 2019-08-12 15:36
 * @Description:
 * <AUTHOR>
 */
public class StatementWalletUtils {

    public void appendStatementTransactionHeader(Map context, SXSSFSheet sheet) {
        addLine(sheet, "收钱吧交易明细");

        String merchantSn = BeanUtil.getPropString(context, "merchant_sn");
        String merchantName = BeanUtil.getPropString(context, "merchant_name");
        addLine(sheet, "商户号:" + merchantSn);
        addLine(sheet, "商户名称:" + merchantName);

        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");
        addLine(sheet, String.format(
                "起始日期" + ":[%s]—:[%s]",
                CommonConstant.DETAIL_SDF.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DETAIL_SDF.get().format(end) + CommonConstant.TIME_ZONE_TL.get()
        ));

        addLine(sheet, "#-----------------------------------------余额明细列表----------------------------------------#");
        addLine(sheet, "日期", "时间", "余额流水号", "外部业务单号", "初始余额", "变动金额", "变动类型", "变动后余额", "标识");
    }

    public void appendStatementTransactionDetail(Map context, List<Map<String, Object>> balanceList, SXSSFSheet sheet) {
        for (Map<String, Object> map : balanceList) {

            List rowValue = new ArrayList();

            //日期
            rowValue.add(CommonConstant.DAY_SDF.get().format(map.get(DaoConstants.CTIME)));
            //时间
            rowValue.add(CommonConstant.TIME_SDF.get().format(map.get(DaoConstants.CTIME)));
            //余额流水号
            rowValue.add(BeanUtil.getPropString(map, "id"));
            //变动类型
            final int type = BeanUtil.getPropInt(map, "type");
            String bizSn = MapUtil.getString(MapUtil.getMap(map, "detail"), "biz_sn");
            //外部业务单号(交易流水号) 变动类型为收款/退款/撤时显示
            if (bizSn == null && (type == WalletType.INCOME.getValue() || type == WalletType.REFUND.getValue())){
                bizSn = MapUtil.getString(map, "action_id", "").replace("t", "");
            }
            rowValue.add(bizSn);

            int sign = BeanUtil.getPropInt(map, "sign");
            long amount = BeanUtil.getPropLong(map, "amount");
            long signAmout = 0;
            if (sign == 1) {
                signAmout = amount;
            } else if (sign == -1) {
                signAmout = amount * -1;
            }
            final long balance = BeanUtil.getPropLong(map, "balance");

            long oldBanlance = balance - signAmout;
            //初始余额
            rowValue.add(oldBanlance / 100.0);
            //变动金额
            rowValue.add(signAmout / 100.0);
            //变动类型
            String name = MapUtil.getString(map, "name");
            if(!StringUtil.empty(name)) {
                rowValue.add(name);
            }else {
                rowValue.add(getWalletTypeName(type));
            }
            //变动后余额
            rowValue.add(BeanUtil.getPropLong(map, "balance") / 100.0);

            //标识
            //账户类型 默认为本地卡
            int accountType = BeanUtil.getPropInt(map, WalletConstant.KEY_DETAIL_ACCOUNT_TYPE, WalletConstant.ACCOUNT_TYPE_LOCAL);
            rowValue.add(accountType == WalletConstant.ACCOUNT_TYPE_CROSS ? "外卡" : StringUtils.EMPTY);

            SheetHelper.appendLine(sheet, rowValue);
        }


    }

    public void updateStatementSummaryInfo(List<Map<String, Object>> balanceList, Map summary) {

        //收入
        long income = BeanUtil.getPropInt(summary, "income");
        //支出
        long outcome = BeanUtil.getPropInt(summary, "outcome");
        //本地收入
        long localIncome = BeanUtil.getPropLong(summary, "localIncome");
        //本地支出
        long localOutcome = BeanUtil.getPropLong(summary, "localOutcome");
        //跨境收入
        long crossIncome = BeanUtil.getPropLong(summary, "crossIncome");
        //跨境支出
        long crossOutcome = BeanUtil.getPropLong(summary, "crossOutcome");

        for (Map<String, Object> map : balanceList) {

            final long amount = BeanUtil.getPropLong(map, "amount");

            int sign = BeanUtil.getPropInt(map, "sign");
            //账户类型
            int accountType = BeanUtil.getPropInt(map, WalletConstant.KEY_DETAIL_ACCOUNT_TYPE, WalletConstant.ACCOUNT_TYPE_LOCAL); //默认为本地卡

            long signAmount = amount * sign;
            if (sign == 1) {
                income += signAmount;
                if (accountType == WalletConstant.ACCOUNT_TYPE_LOCAL) {
                    localIncome += signAmount;
                } else if (accountType == WalletConstant.ACCOUNT_TYPE_CROSS) {
                    crossIncome += signAmount;
                }
            } else if (sign == -1) {
                outcome += signAmount;
                if (accountType == WalletConstant.ACCOUNT_TYPE_LOCAL) {
                    localOutcome += signAmount;
                } else if (accountType == WalletConstant.ACCOUNT_TYPE_CROSS) {
                    crossOutcome += signAmount;
                }
            }


            //变动类型
            final int type = BeanUtil.getPropInt(map, "type");
            final String walletTypeName = getWalletTypeName(type);
            //取出summary中已经存在的金额
            long typeAmount = BeanUtil.getPropLong(summary, walletTypeName);
            //加上这次的
            typeAmount = typeAmount + signAmount;
            //更新summary
            summary.put(walletTypeName, typeAmount);
            //带账户类型summary
            String accountTypeKey = generatorAccountTypeAndWalletTypeNameKey(walletTypeName, accountType);
            long accountTypeAmount = BeanUtil.getPropLong(summary, accountTypeKey);
            accountTypeAmount += signAmount;
            summary.put(accountTypeKey, accountTypeAmount);
        }

        summary.put("income", income);
        summary.put("outcome", outcome);
        summary.put("localIncome", localIncome);
        summary.put("localOutcome", localOutcome);
        summary.put("crossIncome", crossIncome);
        summary.put("crossOutcome", crossOutcome);
    }


    public void buildUpayStatementSummary(Map context, SXSSFSheet summarySheet, Map summaryData) {

        String merchantSn = BeanUtil.getPropString(context, "merchant_sn");
        String merchantName = BeanUtil.getPropString(context, "merchant_name");
        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");

        //起始日期
        addLine(summarySheet, String.format(
                "起始日期" + ":[%s]—:[%s]",
                CommonConstant.DETAIL_SDF.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DETAIL_SDF.get().format(end) + CommonConstant.TIME_ZONE_TL.get()
        ));
        //对账单编号
        String sid = String.format("SQB%s-%s-%s",
                merchantSn,
                CommonConstant.DAY_SDF_YYYYMMDD.get().format(start),
                CommonConstant.DAY_SDF_YYYYMMDD.get().format(end)
        );
        addLine(summarySheet, "对账单编号", sid);

        addLine(summarySheet, "[商户基本信息]");
        addLine(summarySheet, "商户号", "商户名称");
        addLine(summarySheet, merchantSn, merchantName);
        addLine(summarySheet, "");
        addLine(summarySheet, "如果您是支付宝、微信签约的正式商户，手续费和结算金额仅供参考，如有不一致请以支付通道官方后台数据为准");
        addLine(summarySheet, "外卡交易所产生的余额部分，预计需T+1个工作日到账，实际到账时间取决于银行系统清算周期");
        addLine(summarySheet, "");
        addLine(summarySheet, "商户余额汇总");
        addLine(summarySheet, "");

        Map<String,String> incomeMap = new HashMap<>();
        Map<String,String> outcomeMap = new HashMap<>();
        Map<String,String> localIncomeMap = new HashMap<>();
        Map<String,String> localOutcomeMap = new HashMap<>();
        Map<String,String> crossIncomeMap = new HashMap<>();
        Map<String,String> crossOutcomeMap = new HashMap<>();

        for (WalletType walletType : WalletType.values()) {
            if (walletType.getSign() == 1) {
                long value = BeanUtil.getPropLong(summaryData, walletType.getName());
                if (value > 0) {
                    incomeMap.put(walletType.getName(),value + "");
                }
                addWalletTypeNameAmount(localIncomeMap, summaryData, walletType.getName(), WalletConstant.ACCOUNT_TYPE_LOCAL);
                addWalletTypeNameAmount(crossIncomeMap, summaryData, walletType.getName(), WalletConstant.ACCOUNT_TYPE_CROSS);
            } else if (walletType.getSign() == -1) {
                long value = BeanUtil.getPropLong(summaryData, walletType.getName());
                if (value < 0) {
                    outcomeMap.put(walletType.getName(),value + "");
                }
                addWalletTypeNameAmount(localOutcomeMap, summaryData, walletType.getName(), WalletConstant.ACCOUNT_TYPE_LOCAL);
                addWalletTypeNameAmount(crossOutcomeMap, summaryData, walletType.getName(), WalletConstant.ACCOUNT_TYPE_CROSS);
            }
        }
        StatementSummaryFeeHandle summaryFeeHandle = new StatementSummaryFeeHandle(incomeMap, outcomeMap);
        long feeBackAmount = summaryFeeHandle.feeBackAmount;
        long feeAmount = summaryFeeHandle.feeAmount;
        addLine(summarySheet, "收入总金额", ((BeanUtil.getPropLong(summaryData, "income") - feeBackAmount + feeAmount) / 100.0) + "");

        SheetHelper.appendLine(summarySheet, new ArrayList(incomeMap.keySet()));
        SheetHelper.appendLine(summarySheet, new ArrayList(incomeMap.values()));

        addLine(summarySheet, "支出总金额", ((BeanUtil.getPropLong(summaryData, "outcome") + feeBackAmount - feeAmount) / 100.0) + "");

        SheetHelper.appendLine(summarySheet, new ArrayList(outcomeMap.keySet()));
        SheetHelper.appendLine(summarySheet, new ArrayList(outcomeMap.values()));

        StatementSummaryFeeHandle localSummaryFeeHandle = new StatementSummaryFeeHandle(localIncomeMap, localOutcomeMap);
        addLine(summarySheet, "收入总金额（不含外卡）", ((BeanUtil.getPropLong(summaryData, "localIncome") - localSummaryFeeHandle.getFeeBackAmount() + localSummaryFeeHandle.getFeeAmount()) / 100.0) + "");
        SheetHelper.appendLine(summarySheet, new ArrayList(localIncomeMap.keySet()));
        SheetHelper.appendLine(summarySheet, new ArrayList(localIncomeMap.values()));

        addLine(summarySheet, "支出总金额（不含外卡）", ((BeanUtil.getPropLong(summaryData, "localOutcome") + localSummaryFeeHandle.getFeeBackAmount() - localSummaryFeeHandle.getFeeAmount()) / 100.0) + "");
        SheetHelper.appendLine(summarySheet, new ArrayList(localOutcomeMap.keySet()));
        SheetHelper.appendLine(summarySheet, new ArrayList(localOutcomeMap.values()));

        StatementSummaryFeeHandle crossSummaryFeeHandle = new StatementSummaryFeeHandle(crossIncomeMap, crossOutcomeMap);
        addLine(summarySheet, "收入总金额（外卡）", ((BeanUtil.getPropLong(summaryData, "crossIncome") - crossSummaryFeeHandle.getFeeBackAmount() + crossSummaryFeeHandle.getFeeAmount()) / 100.0) + "");
        SheetHelper.appendLine(summarySheet, new ArrayList(crossIncomeMap.keySet()));
        SheetHelper.appendLine(summarySheet, new ArrayList(crossIncomeMap.values()));

        addLine(summarySheet, "支出总金额（外卡）", ((BeanUtil.getPropLong(summaryData, "crossOutcome") + crossSummaryFeeHandle.getFeeBackAmount() - crossSummaryFeeHandle.getFeeAmount()) / 100.0) + "");
        SheetHelper.appendLine(summarySheet, new ArrayList(crossOutcomeMap.keySet()));
        SheetHelper.appendLine(summarySheet, new ArrayList(crossOutcomeMap.values()));
    }


    private String getWalletTypeName(int type) {
        for (WalletType walletType : WalletType.values()) {
            if (walletType.getValue() == type) {
                return walletType.getName();
            }
        }
        return "";
    }


    private void addLine(SXSSFSheet sxssfSheet, String... s) {
        SheetHelper.appendLine(sxssfSheet, Arrays.asList(s));
    }

    private String generatorAccountTypeAndWalletTypeNameKey(String walletTypeName, Integer accountType) {
        return String.format("walletType_%s_%d", walletTypeName, accountType);
    }

    private void addWalletTypeNameAmount(Map<String, String> result, Map<String, Object> summary, String walletTypeName, int accountType) {
        String key = generatorAccountTypeAndWalletTypeNameKey(walletTypeName, accountType);
        long value = BeanUtil.getPropLong(summary, key);
        if (Math.abs(value) > 0) {
            result.put(walletTypeName, value + "");
        }
    }


    @Data
    public static class StatementSummaryFeeHandle {
        /**
         * 手续费退回 正数
         */
        private long feeBackAmount;
        /**
         * 手续费扣减　负数
         */
        private long feeAmount;

        public StatementSummaryFeeHandle(Map<String, String> incomeMap, Map<String, String> outcomeMap) {
            process(incomeMap, outcomeMap);
        }

        private void process(Map<String, String> incomeMap, Map<String, String> outcomeMap) {
            feeBackAmount = Long.parseLong(incomeMap.getOrDefault(WalletType.FEE_BACK.getName(), "0"));
            feeAmount = Long.parseLong(outcomeMap.getOrDefault(WalletType.FEE_MINUS.getName(), "0"));
            mergeMap(incomeMap, outcomeMap);
            for (Map.Entry<String, String> incomeEntry : incomeMap.entrySet()) {
                long amount = Long.parseLong(incomeEntry.getValue());
                incomeEntry.setValue(String.valueOf(amount / 100.0));
            }
            for (Map.Entry<String, String> outcome : outcomeMap.entrySet()) {
                long amount = Long.parseLong(outcome.getValue());
                outcome.setValue(String.valueOf(amount / 100.0));
            }
        }

        /**
         * 合并手续费
         * @param incomeMap
         * @param outcomeMap
         */
        private void mergeMap(Map<String, String> incomeMap, Map<String, String> outcomeMap) {
            String fee = outcomeMap.getOrDefault(WalletType.FEE_MINUS.getName(), "0");
            long feeAmount = Long.parseLong(fee);
            outcomeMap.remove(WalletType.FEE_MINUS.getName());

            String feeBack = incomeMap.getOrDefault(WalletType.FEE_BACK.getName(), "0");
            long feeBackAmount = Long.parseLong(feeBack);
            incomeMap.remove(WalletType.FEE_BACK.getName());

            String drawFee = outcomeMap.getOrDefault(WalletType.DRAW_FEE_MINUS.getName(), "0");
            long drawFeeAmount = Long.parseLong(drawFee);
            //结算金额 = 结算金额（不包含手续费) + 结算手续费
            String orgDraw = outcomeMap.getOrDefault(WalletType.DRAW.getName(), "0");
            long orgDrawAmount = Long.parseLong(orgDraw);
            long drawAmount = orgDrawAmount + drawFeeAmount;
            if(drawAmount != 0) {
                outcomeMap.put(WalletType.DRAW.getName(), String.valueOf(drawAmount));
            }
            outcomeMap.remove(WalletType.DRAW_FEE_MINUS.getName());

            String drawFeeBack = incomeMap.getOrDefault(WalletType.DRAW_FEE_BACK.getName(), "0");
            long drawFeeBackAmount = Long.parseLong(drawFeeBack);
            //结算退回金额 = 结算退回金额（不包含手续费) + 结算退回手续费
            String orgDrawBack = outcomeMap.getOrDefault(WalletType.DRAW_BACK.getName(), "0");
            long orgDrawBackAmount = Long.parseLong(orgDrawBack);
            long drawBackAmount = orgDrawBackAmount + drawFeeBackAmount;
            if (drawBackAmount != 0) {
                incomeMap.put(WalletType.DRAW_BACK.getName(), String.valueOf(drawBackAmount));
            }
            incomeMap.remove(WalletType.DRAW_FEE_BACK.getName());

            //收款的金额 = 收款(不包含手续费) - 手续费;   例如收款100 手续费0.38元 为 100 + (-0.38)
            String income = incomeMap.getOrDefault(WalletType.INCOME.getName(), "0");
            long orgIncomeAmount = Long.parseLong(income);
            long incomeAmount = orgIncomeAmount + feeAmount;
            if (incomeAmount > 0) {
                incomeMap.put(WalletType.INCOME.getName(), String.valueOf(incomeAmount));
            }
            //退款金额 = 退款(不包含手续) - 手续费回退;   例如退款100 手续费0.38元 为 -100 + 0.38
            String refund = outcomeMap.getOrDefault(WalletType.REFUND.getName(), "0");
            long orgRefundAmount = Long.parseLong(refund);
            long refundAmount = orgRefundAmount + feeBackAmount;
            if(refundAmount < 0){
                outcomeMap.put(WalletType.REFUND.getName(), String.valueOf(refundAmount));
            }
        }
    }
}
