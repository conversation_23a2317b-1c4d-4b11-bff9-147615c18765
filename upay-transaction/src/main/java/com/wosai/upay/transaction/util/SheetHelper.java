package com.wosai.upay.transaction.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.export.base.RunContext;
import com.wosai.upay.transaction.helper.DBSelectContext;

import com.wosai.upay.transaction.model.StatementTaskLog;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import avro.shaded.com.google.common.collect.Lists;

import static com.wosai.common.utils.transaction.Transaction.MCH_DISCOUNT_ORIGIN_TYPE;
import static com.wosai.upay.transaction.util.LanguageUtil.*;

/**
 * <AUTHOR>
 */
public class SheetHelper {


    /**
     * create book with
     */
    public static SXSSFWorkbook newBook() {
        return new SXSSFWorkbook(CommonConstant.ROW_ACCESS_WINDOW_SIZE);
    }


    /**
     * create book with sheet
     */
    public static SXSSFWorkbook newBookWithSheet(String sheetName) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(CommonConstant.ROW_ACCESS_WINDOW_SIZE);
        workbook.createSheet(sheetName);
        return workbook;
    }

    /**
     * create statement detail book with sheet
     */
    public static SXSSFWorkbook newStatementDetailSheet(Map context, String sheetName) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(CommonConstant.ROW_ACCESS_WINDOW_SIZE);
        SXSSFSheet detailSheet = (SXSSFSheet) workbook.createSheet(sheetName);
        appendHeaderForStatementDetailSheet(context, detailSheet);
        return workbook;
    }

    /**
     * add title content for statement summary
     */
    public static void addStatementSummaryTitle(Map context, SXSSFSheet summarySheet) {
        //是否是KA对账单
        boolean isKAStatement = RunContext.currentContext().getIsKAStatement();
        String merchantSn = BeanUtil.getPropString(context, "merchant_sn");
        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");
        String merchantName = BeanUtil.getPropString(context, "merchant_name");
        String sid = String.format("SQB%s-%s-%s",
                merchantSn,
                CommonConstant.DAY_SDF_YYYYMMDD.get().format(start),
                CommonConstant.DAY_SDF_YYYYMMDD.get().format(end)
        );

        appendLine(summarySheet, Arrays.asList(String.format(
                getValue(PERIOD) + ":[%s]:[%s]",
                CommonConstant.DETAIL_SDF.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DETAIL_SDF.get().format(end) + CommonConstant.TIME_ZONE_TL.get()
                ))
        );
        appendLine(summarySheet, Arrays.asList(getValue(STATEMENT_NO), sid));
        appendLine(summarySheet, Arrays.asList(getValues(MERCHANT_BASIC_INFORMATION)));
        appendLine(summarySheet, getValues(MERCHANT_NO, MERCHANT_NAME, STATEMENT_STYLE));
        appendLine(summarySheet, Arrays.asList(merchantSn, merchantName, getStatementTypeByIncludes()));
        appendLine(summarySheet, Arrays.asList());
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            appendLine(summarySheet, Arrays.asList("注意事项：每日15点更新昨日交易的手续费和结算金额，如需查看昨日交易的手续费和结算金额请于当日15点后下载对账单查询。"));
        }
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY) {
            appendLine(summarySheet, Arrays.asList("如果您是微信、支付宝直连商户或银行直清商户，手续费和结算金额仅供参考，如有不一致请以支付通道官方后台数据为准"));
            if (!isKAStatement) {
                appendLine(summarySheet, Arrays.asList("收款净额 = 收款金额 - 退款金额"));
            }
            appendLine(summarySheet, Arrays.asList("实收金额 = 收款金额 - 退款金额 - 收钱吧商户优惠 - 收款通道商户免充值优惠；收钱吧补贴优惠和收款通道补贴优惠不需要商户承担，收款通道商户预充值优惠已预存金额，不做扣减。"));
            appendLine(summarySheet, Arrays.asList("结算金额 = 实收金额 - 支付手续费 - 分账金额（含技术服务费），其中支付手续费为每笔交易手续费相加之和。分账金额包含商户自主业务分账和收钱吧技术服务费，其中商户自主业务分账金额 = （实收金额-交易手续费-技术服务费）*分账比例，收钱吧技术服务费按每笔交易收取，如对分账金额有疑问，可在对账管理下载分账对账单查看更详细的分账数据。"));
        }
        if (RunContext.currentContext().getExportIncludes().contains(StatementTaskLog.INCLUDE_STORE_IN + "")) {
            appendLine(summarySheet, Arrays.asList("到账金额 = 收款净额到账金额 + 储值充值到账金额；到账金额由收款净额和储值充值金额两部分组成，收款净额和储值充值金额分别结算后累加至余额账户，为最终到账银行卡的金额；"));
            appendLine(summarySheet, Arrays.asList("收款净额到账金额 = 结算金额 - 商家记账结算金额 - 储值核销实收金额 - 直清交易结算金额，收款净额到账金额为收款净额结算后累加至余额账户的金额，其中直清交易由微信、支付宝、银行机构等直接结算，直清交易和商家记账都不计入余额账户；储值核销金额在充值时已入账，核销时也不再计入余额账户；"));
            appendLine(summarySheet, Arrays.asList("储值充值到账金额 = 储值充值结算金额 - 现金支付充值金额 - 其他方式记账充值金额 - 直清充值结算金额，储值充值到账金额为储值充值金额结算后累加至余额账户的金额，其中现金充值、其他方式记账充值和直清储值充值都不计入余额账户"));
        } else {
            appendLine(summarySheet, Arrays.asList("收款净额到账金额 = 结算金额 - 商家记账结算金额 - 储值核销实收金额 - 直清交易结算金额，收款净额到账金额为收款净额结算后累加至余额账户的金额，其中直清交易由微信、支付宝、银行机构等直接结算，直清交易和商家记账都不计入余额账户；储值核销金额在充值时已入账，核销时也不再计入余额账户"));
        }
        appendLine(summarySheet, Arrays.asList());
    }


    public static String getStatementTypeByIncludes() {
        RunContext runContext = RunContext.currentContext();
        String includes = runContext.getExportIncludes();
        StringJoiner joiner = new StringJoiner("+");
        if (includes.contains(StatementTaskLog.INCLUDE_PAY + "")) {
            joiner.add("移动收款");
        }
        if (includes.contains(StatementTaskLog.INCLUDE_CHARGE + "")) {
            joiner.add("记账及第三方外卖");
        }
        if (includes.contains(StatementTaskLog.INCLUDE_STORE_IN + "")) {
            joiner.add("储值卡充值");
        }
        return joiner.toString();
    }


    /**
     * add header for statement detail sheet
     */
    private static void appendHeaderForStatementDetailSheet(Map context, SXSSFSheet detailSheet) {
        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");
        //是否是KA对账单
        RunContext runContext = RunContext.currentContext();
        boolean isKAStatement = runContext.getIsKAStatement();
        String merchantSn = BeanUtil.getPropString(context, "merchant_sn");
        String merchantName = BeanUtil.getPropString(context, "merchant_name");
        appendLine(detailSheet, Arrays.asList(getValue(WOSAI_TRANSACTION_DETAILS)));
        appendLine(detailSheet, Arrays.asList(getValue(MERCHANT_NO) + ":" + merchantSn));
        appendLine(detailSheet, Arrays.asList(getValue(MERCHANT_NAME) + ":" + merchantName));
        appendLine(detailSheet, Arrays.asList(String.format(
                getValue(PERIOD) + ":[%s]—:[%s]",
                CommonConstant.DETAIL_SDF.get().format(start) + CommonConstant.TIME_ZONE_TL.get(),
                CommonConstant.DETAIL_SDF.get().format(end) + CommonConstant.TIME_ZONE_TL.get()
        )));
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            appendLine(detailSheet, Arrays.asList("注意事项：每日15点更新昨日交易的手续费和结算金额，如需查看昨日交易的手续费和结算金额请于当日15点后下载对账单查询。"));
        }
        appendLine(detailSheet, Arrays.asList("#-----------------------------------------" + getValue(TRANSACTION_DETAILS_LIST) + "----------------------------------------#"));
        List<String> valueList = Lists.newArrayList(TRANSACTION_DATE, TIME, MERCHANT_SERIAL_NO, PAYMENT_TYPE, PRODUCT_NAME,
                MERCHANT_ORDER_NO, WOSAI_ORDER_NO, PAYMENT_TYPE_ORDER_NO, TRANSACTION_TYPE, TRANSACTION_STATUS, CONSUMER_ACCOUNT, CURRENCY,
                TRANSACTION_AMOUNT);

        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY) {
            valueList.addAll(CommonConstant.NOT_BANK_CARD_HEAD_NAME);
            valueList.add(LanguageUtil.getValue(CHARGE_RATE) + "%");
        }

        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            //手续费”放在“实收金额”后面
            valueList.addAll(com.google.common.collect.Lists.newArrayList(PAID_AMOUNT, CHARGE));
        } else {
            valueList.addAll(com.google.common.collect.Lists.newArrayList(CHARGE, PAID_AMOUNT));
        }

        if (!isKAStatement) {
            valueList.add(TRADE_SERVICE_SHARING_AMOUNT);
        }
        valueList.addAll(Lists.newArrayList(DETAIL_SETTLEMENT_AMOUNT, STORE_NO, MERCHANT_STORE_NO, STORE_NAME, TERMINAL_NO, MERCHANT_TERMINAL_NO, TERMINAL_NAME, TERMINAL_TYPE, DEVICE_ID, OPERATOR, CASHIER, REMARKS));

        if (RunContext.currentContext().crossMchRefundEnable()) {
            valueList.addAll(Lists.newArrayList(OP_MCH_SN, OP_STORE_SN, OP_STORE_NAME, IS_CROSS_MCH_REFUND));
        }
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            valueList.addAll(CommonConstant.BANK_CARD_HEAD_NAME);
            //“刷卡完成时间”放在“交易日期”、“时间”后面
            valueList.add(2, LanguageUtil.CHANNEL_FINISH_TIME);
            //“操作员”、“收银员”删掉不显示
            valueList.remove(OPERATOR);
            valueList.remove(CASHIER);
            //“备注”放最后
            valueList.remove(REMARKS);
            valueList.add(REMARKS);
        }

        valueList.add(SHARING_FLAG);
        if (!isKAStatement) {
            valueList.add(MERCHANT_SHARING_AMOUNT);//分账金额（不含技术服务费）
        } else {
            valueList.add(SHARING_AMOUNT);//分账金额（含技术服务费）
        }
        valueList.add(MCH_DISCOUNT_ORIGIN_TYPE);
        valueList.add(PRODUCT_FLAG);
        valueList.add(SUBPAYWAY);
        if (RunContext.currentContext().getExportIncludes().contains(StatementTaskLog.INCLUDE_CASH_DESK + "")) {
            valueList.add(CASH_DESK_NAME);
        }
        valueList.add(TRADE_TSN);
        valueList.add(SQB_BIZ_MODEL);
        valueList.add(PAY_CHANNEL);
        if (!isKAStatement) {
            valueList.add(SMART_ORDER);
            valueList.add(SMART_FLOW_SERVICE);
            valueList.add(SMART_CAMPUS_DISTRIBUTION);
            valueList.add(SAAS_THIRD_DISTRIBUTION);
            valueList.add(SAAS_COUPON_SALE);
            valueList.add(SAAS_EQUITY_CARD_SALE);
            valueList.add(SAAS_CARD_RECHARGE);
            valueList.add(HBFQ_INDIRECT_TIEXI);
            valueList.add(PAY_AFTER_USE);
            valueList.add(SMART_FLOW_SERVICE_COMMISSION);
            valueList.add(SMART_FLOW_SERVICE_BASE_AMOUNT);
        }
        appendLine(detailSheet, getValueList(valueList));


    }


    /**
     * add header for statement detail sheet
     */
    public static void appendHeaderForStatementCrossMchRefundSheet(SXSSFSheet crossMchRefundSheet) {
        List<String> valueList = getValues(TRANSACTION_DATE, TIME, MERCHANT_SERIAL_NO, PAYMENT_TYPE, PRODUCT_NAME,
                MERCHANT_ORDER_NO, WOSAI_ORDER_NO, PAYMENT_TYPE_ORDER_NO, TRANSACTION_TYPE, TRANSACTION_STATUS, CONSUMER_ACCOUNT, CURRENCY,
                TRANSACTION_AMOUNT);
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY) {
            valueList.addAll(CommonConstant.NOT_BANK_CARD_HEAD_NAME);
            valueList.add(LanguageUtil.getValue(CHARGE_RATE) + "%");
        }

        valueList.addAll(Lists.newArrayList(CHARGE, PAID_AMOUNT, SHARING_AMOUNT, DETAIL_SETTLEMENT_AMOUNT, STORE_NO, MERCHANT_STORE_NO, STORE_NAME, TERMINAL_NO, MERCHANT_TERMINAL_NO, TERMINAL_NAME, TERMINAL_TYPE, DEVICE_ID, OPERATOR, CASHIER, REMARKS));

        if (RunContext.currentContext().crossMchRefundEnable()) {
            valueList.addAll(Lists.newArrayList(OP_MCH_SN, OP_STORE_SN, OP_STORE_NAME, IS_CROSS_MCH_REFUND));
        }
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            valueList.addAll(CommonConstant.BANK_CARD_HEAD_NAME);
        }
        valueList.add(PAY_CHANNEL);
        appendLine(crossMchRefundSheet, getValueList(valueList));
    }


    @SuppressWarnings("all")
    public static void appendLine(SXSSFSheet sheet, List values) {
        //1.format values
        for (int i = 0; i < values.size(); i++) {
            Object value = values.get(i);
            if (value == null) {
                values.set(i, "");
            } else if (value instanceof Double || value instanceof Float || value instanceof BigDecimal) {
                values.set(i, CommonUtil.formatMoney(value.toString(), 2));
            }
        }
        //2.write to cell
        int lastRowNum = sheet.getLastRowNum();
        if (sheet.getRow(lastRowNum) != null) {
            lastRowNum++;
        }
        SXSSFRow row = (SXSSFRow) sheet.createRow(lastRowNum);
        int currentCol = 0;
        for (Object value : values) {
            if (value instanceof Number) {
                row.createCell(currentCol++).setCellValue(((Number) value).doubleValue());
            } else if (value instanceof String) {
                row.createCell(currentCol++).setCellValue((String) value);
            } else {
                row.createCell(currentCol++).setCellValue(value == null ? null : value.toString());
            }
        }
    }

    /**
     * 最加行，根据cellIndex 和 color 可以设置单元格字体颜色
     * @param sheet
     * @param values
     * @param color
     */
    public static void appendLine(SXSSFSheet sheet, List values, Short color) {

        if (color == null) {
            appendLine(sheet, values);
        } else {
            //1.format values
            for (int i = 0; i < values.size(); i++) {
                Object value = values.get(i);
                if (value == null) {
                    values.set(i, "");
                } else if (value instanceof Double || value instanceof Float || value instanceof BigDecimal) {
                    values.set(i, CommonUtil.formatMoney(value.toString(), 2));
                }
            }
            //2.write to cell
            int lastRowNum = sheet.getLastRowNum();
            if (sheet.getRow(lastRowNum) != null) {
                lastRowNum++;
            }
            SXSSFRow row = (SXSSFRow) sheet.createRow(lastRowNum);
            int currentCol = 0;
            for (Object value : values) {
                Cell cell = row.createCell(currentCol++);
                Workbook workbook = sheet.getWorkbook();
                CellStyle cellStyle = workbook.createCellStyle();
                // 创建新字体
                Font font = sheet.getWorkbook().createFont();
                font.setColor(color);
                // 设置字体
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
                if (value instanceof Number) {
                    cell.setCellValue(((Number) value).doubleValue());
                } else if (value instanceof String) {
                    cell.setCellValue((String) value);
                } else {
                    cell.setCellValue(value == null ? null : value.toString());
                }
            }
        }
    }

    /**
     * 最加行，根据cellIndex 和 color 可以设置单元格字体颜色
     * @param sheet
     * @param values
     * @param cellIndex
     * @param color
     */
    public static void appendLine(SXSSFSheet sheet, List values, List<Integer> cellIndex, Short color) {
        //1.format values
        for (int i = 0; i < values.size(); i++) {
            Object value = values.get(i);
            if (value == null) {
                values.set(i, "");
            } else if (value instanceof Double || value instanceof Float || value instanceof BigDecimal) {
                values.set(i, CommonUtil.formatMoney(value.toString(), 2));
            }
        }
        //2.write to cell
        int lastRowNum = sheet.getLastRowNum();
        if (sheet.getRow(lastRowNum) != null) {
            lastRowNum++;
        }
        SXSSFRow row = (SXSSFRow) sheet.createRow(lastRowNum);
        int currentCol = 0;
        for (int i = 0; i < values.size(); i++) {
            Object value = values.get(i);
            Cell cell = row.createCell(currentCol++);
            if (cellIndex.contains(i)) {
                Workbook workbook = sheet.getWorkbook();
                CellStyle cellStyle = workbook.createCellStyle();
                // 创建新字体
                Font font = sheet.getWorkbook().createFont();
                font.setColor(color);
                // 设置字体
                cellStyle.setFont(font);
                cell.setCellStyle(cellStyle);
            }
            if (value instanceof Number) {
                cell.setCellValue(((Number) value).doubleValue());
            } else if (value instanceof String) {
                cell.setCellValue((String) value);
            } else {
                cell.setCellValue(value == null ? null : value.toString());
            }
        }
    }

    public static void write2File(SXSSFWorkbook workbook, List<File> statementDetailFiles, String filepath) throws IOException {
        File file = FileUtil.excelToFile(workbook, filepath);
        statementDetailFiles.add(file);
    }

    public static File bookToDiskFile(SXSSFWorkbook workbook, String filepath) throws IOException {
        return FileUtil.excelToFile(workbook, filepath);
    }

    public static void sortSheetOrder(SXSSFWorkbook workbook, String... sortSheetName) {
        if (workbook.getXSSFWorkbook().getNumberOfSheets() < sortSheetName.length) {
            return;
        }
        for (int i = 0; i < sortSheetName.length; i++) {
            workbook.getXSSFWorkbook().setSheetOrder(sortSheetName[i], i);
        }

    }

}