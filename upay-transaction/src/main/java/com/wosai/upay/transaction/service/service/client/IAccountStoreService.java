package com.wosai.upay.transaction.service.service.client;

import com.wosai.upay.transaction.service.model.TerminalCode;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface IAccountStoreService {

    /**
     * 商户id查询员工
     * @param merchantId
     * @return
     */
    Map<String,String> findStoreStaffByMerchant(String merchantId);



    /**
     * 商户ids查询员工
     * @param merchantIds
     * @return
     */
    Map<String,String> findStoreStaffByMerchantIds(Set<String> merchantIds);

    /**
     * 终端id查询终端
     * @param terminalId
     * @return
     */
    TerminalCode findTerminalByTerminalId(String terminalId);

    /**
     * 终端sn查询终端
     * @param terminalSn
     * @return
     */
    Map<String,String> findTerminalByTerminalSn(String terminalSn);


    /**
     * 门店id查询门店
     * @param storeId
     * @return
     */
    Map findStore(String storeId);

    /**
     * merchantId:storeId:buyerUid:payway:appid 查询顾客/会员名称，默认返回defaultName
     * @param
     * @return
     */
    String findPayerAccount(String merchantId, String storeId, String buyerUid, int payWay, String appid);
}
