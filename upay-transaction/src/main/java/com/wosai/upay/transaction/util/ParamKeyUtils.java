package com.wosai.upay.transaction.util;

import com.wosai.common.redisLock.RedisLock;
import com.wosai.common.redisLock.exception.RedisLockException;
import com.wosai.common.utils.WosaiDigestUtils;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;
import com.wosai.upay.transaction.model.param.AccountRecordParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class ParamKeyUtils {

    public static final Logger logger = LoggerFactory.getLogger(ParamKeyUtils.class);

    public static final String GET_ACCOUNT_BOOK_RECORDS = "getAccountBookRecords";

    public static final String GET_MORE_ACCOUNT_BOOK_RECORDS = "getMoreAccountBookRecords";

    public static String getRedisKeyForAccountBook(AccountRecordParam param, String methodName) {
        StringBuilder stringBuilder = new StringBuilder(methodName);
        stringBuilder.append(param.toString());
        try {
            return WosaiDigestUtils.md5(stringBuilder.toString().getBytes());
        } catch (Exception ex) {
        }
        return stringBuilder.toString();
    }

    public static void validRedis(RedisLock redisLock){
        try{
            if(!redisLock.acquire()){
                throw ErrorMessageEnum.SYSTEM_BUSY.getBizException();
            }
        }catch (RedisLockException re){
            logger.error("获取redis锁异常", re);
        }
    }


    public static void deleteValueRedis(RedisLock redisLock) {
        try {
            redisLock.release();
        } catch (Exception e) {
            logger.error("释放redis锁异常", e);
        }
    }




}
