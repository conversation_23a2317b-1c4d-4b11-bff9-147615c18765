package com.wosai.upay.transaction.util;

import com.google.common.collect.Lists;
import com.wosai.common.utils.WosaiJsonUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.model.TransactionQuerySummaryInfo;
import com.wosai.upay.transaction.service.service.common.ReflectService;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.wosai.common.utils.transaction.Transaction.MCH_DISCOUNT_ORIGIN_TYPE;
import static com.wosai.upay.transaction.util.LanguageUtil.*;

/**
 * <AUTHOR>
 */
public class TransactionQueryExportUtil {


    private static final String SHEET_NAME = "流水明细";

    public static SheetUtil initSheetAppendHeader() {
        SheetUtil sheetUtil = initSheet(SHEET_NAME);
        List<String> columns = Lists.newArrayList(TRANSACTION_DATE, TIME, MERCHANT_SERIAL_NO, PAYMENT_TYPE, PRODUCT_NAME,
                MERCHANT_ORDER_NO, WOSAI_ORDER_NO, PAYMENT_TYPE_ORDER_NO, TRANSACTION_TYPE, TRANSACTION_STATUS, CONSUMER_ACCOUNT, CURRENCY,
                TRANSACTION_AMOUNT);
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY) {
            columns.addAll(CommonConstant.NOT_BANK_CARD_HEAD_NAME);
            columns.add(LanguageUtil.getValue(CHARGE_RATE) + "%");
        }
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            //手续费”放在“实收金额”后面
            columns.addAll(Lists.newArrayList(PAID_AMOUNT, CHARGE));
        } else {
            columns.addAll(Lists.newArrayList(CHARGE, PAID_AMOUNT));
        }
        columns.addAll(Lists.newArrayList(SHARING_AMOUNT, DETAIL_SETTLEMENT_AMOUNT, STORE_NO, MERCHANT_STORE_NO, STORE_NAME, TERMINAL_NO,
                MERCHANT_TERMINAL_NO, TERMINAL_NAME, TERMINAL_TYPE, DEVICE_ID, OPERATOR, CASHIER, REMARKS, PAY_FUND_CHANNEL));
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            columns.addAll(CommonConstant.BANK_CARD_HEAD_NAME);
            //“刷卡完成时间”放在“交易日期”、“时间”后面
            columns.add(2, LanguageUtil.CHANNEL_FINISH_TIME);
            //“操作员”、“收银员”删掉不显示
            columns.remove(OPERATOR);
            columns.remove(CASHIER);
            //“备注”放最后
            columns.remove(REMARKS);
            columns.add(REMARKS);
        }
        columns.add(MCH_DISCOUNT_ORIGIN_TYPE);
        columns.add(MERCHANT_NAME);
        StatementCommonUtil.appendLine(sheetUtil.getSheet(), getValues(columns.toArray(new String[columns.size()])));
        return sheetUtil;
    }

    public static SheetUtil initSheet(String sheetName) {
        SXSSFWorkbook workbook = new SXSSFWorkbook(CommonConstant.ROW_ACCESS_WINDOW_SIZE);
        SXSSFSheet sheet = (SXSSFSheet) workbook.createSheet(sheetName);
        SheetUtil sheetUtil = new SheetUtil(sheet, workbook);
        return sheetUtil;
    }

    public static void appendRows(TransactionQuerySummaryInfo summaryInfo, List<Map<String, Object>> transactions, SXSSFSheet details) {
        for (Map transaction : transactions) {
            long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
            boolean liquidationNextDay = BeanUtil.getPropBoolean(transaction, TransactionParam.LIQUIDATION_NEXT_DAY);
            String currency = BeanUtil.getPropString(transaction, TransactionParam.CURRENCY, "CNY");

            long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
            //收钱吧优惠
            long discountAmount = BeanUtil.getPropLong(transaction, Transaction.WOSAI_FAVORABLE_AMOUNT);
            long channelDiscount = BeanUtil.getPropLong(transaction, Transaction.CHANNEL_AGENT_FAVORABLE_AMOUNT);
            long channelMchDiscount = BeanUtil.getPropLong(transaction, Transaction.CHANNEL_MCH_FAVORABLE_AMOUNT);
            long channelMchTopUpDiscount = BeanUtil.getPropLong(transaction, Transaction.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT);

            Long paidAmount = (Long) transaction.get(Transaction.PAID_AMOUNT);
            if (paidAmount == null) {
                paidAmount = effectiveAmount - (channelDiscount + channelMchDiscount + channelMchTopUpDiscount);
            }
            long actualReceiveAmount = BeanUtil.getPropLong(transaction, Transaction.ACTUAL_RECEIVE_AMOUNT);
            long clearingAmount = BeanUtil.getPropLong(transaction, Transaction.CLEARING_AMOUNT);
            long sharingAmount = BeanUtil.getPropLong(transaction, Transaction.SHARING_AMOUNT, 0);
            long merchantDiscount = BeanUtil.getPropLong(transaction, Transaction.MCH_FAVORABLE_AMOUNT);
            long tradeFee = BeanUtil.getPropLong(transaction, Transaction.FEE);
            int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);
            int transactionPayway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
            if (BeanUtil.getPropLong(transaction, Transaction.STATUS) == Transaction.STATUS_SUCCESS) {
                double sumAmount = CommonUtil.formatMoney(originalAmount / 100.0, 2);
                double sumMerchantDiscount = CommonUtil.formatMoney((merchantDiscount + channelMchDiscount) / 100.0, 2);
                double sumActualReceiveAmount = CommonUtil.formatMoney(actualReceiveAmount / 100.0, 2);
                double sumFee = CommonUtil.formatMoney(tradeFee / 100.0, 2);
                double sumSharingAmount = CommonUtil.formatMoney(sharingAmount / 100.0, 2);
                double sumClearingAmount = CommonUtil.formatMoney(clearingAmount / 100.0, 2);
                if (TransactionTypeRelatedUtil.isPositiveType(transactionType)) {
                    summaryInfo.recordAmountByType(sumAmount, sumMerchantDiscount, sumActualReceiveAmount, sumFee, sumSharingAmount, sumClearingAmount, liquidationNextDay, transactionType, transactionPayway);
                }
                if (TransactionTypeRelatedUtil.isNegativeType(transactionType)) {
                    summaryInfo.recordAmountByType(-sumAmount, -sumMerchantDiscount, -sumActualReceiveAmount, -sumFee, -sumSharingAmount, -sumClearingAmount, liquidationNextDay, transactionType, transactionPayway);
                }
            }
            summaryInfo.recordAllCount();
            if (TransactionTypeRelatedUtil.isNegativeType(transactionType)) {
                originalAmount = originalAmount * -1;
                paidAmount = paidAmount * -1;
                discountAmount *= -1;
                actualReceiveAmount *= -1;
                clearingAmount *= -1;
                merchantDiscount *= -1;
                channelDiscount *= -1;
                channelMchDiscount *= -1;
                channelMchTopUpDiscount *= -1;
                tradeFee *= -1;
                sharingAmount *= -1;
            }

            double feeRate = 0.0;
            String feeRateString = BeanUtil.getPropString(transaction, TransactionParam.FEE_RATE, "0.0");
            if (!feeRateString.trim().equals("")) {
                feeRate = CommonUtil.formatMoney(feeRateString, 3);
            }
            /*"交易日期", "时间", "商户流水号", "支付方式", "商品名",
                    "商户内部订单号", "商户订单号", "收款通道订单号", "交易类型", "交易状态", "付款账户", "货币类型",
                    "交易金额", "商户优惠", "收钱吧优惠", "收款通道机构优惠", "收款通道商户预充值优惠", "收款通道商户免充值优惠",
                    "消费者实付金额", "扣率%", "手续费", "实收金额", "分账金额", "结算金额", "门店号", "商户门店号", "门店名称", "终端号",
                    "商户终端号", "终端名称", "终端类型", "设备号", "操作员", "收银员"，"备注","支付源资金渠道","商户名"*/
            List rowValue = new ArrayList();
            long cTime = BeanUtil.getPropLong(transaction, DaoConstants.CTIME);
            rowValue.add(CommonConstant.DAY_SDF.get().format(cTime));
            rowValue.add(CommonConstant.TIME_SDF.get().format(cTime));
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN));
            rowValue.add(OrderUtil.getPaywayDesc(transaction.get(Transaction.PAYWAY).toString(), currency, getLanguage()));
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.SUBJECT));
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN));
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.TRADE_NO));
            rowValue.add(getValue(TransactionTypeRelatedUtil.getTransactionTypeLanguageKey(BeanUtil.getPropString(transaction, Transaction.TYPE))));
            Integer status = BeanUtil.getPropInt(transaction, Transaction.STATUS);
            if(!status.equals(Transaction.STATUS_SUCCESS)) {
                tradeFee = 0;
            }
            rowValue.add(getValue(OrderUtil.getTransactionStatusLanguageKey(status))); //交易状态
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.BUYER_LOGIN));

            rowValue.add(currency);
            rowValue.add(originalAmount / 100.0);

            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY) {
                //商户优惠
                rowValue.add(merchantDiscount / 100.0);
                //收钱吧优惠
                rowValue.add(discountAmount / 100.0);
                //收款通道机构优惠
                rowValue.add(channelDiscount / 100.0);
                ;//收款通道商户预充值优惠
                rowValue.add(channelMchTopUpDiscount / 100.0);
                //收款通道商户免充值优惠
                rowValue.add(channelMchDiscount / 100.0);
                //消费者实付金额
                rowValue.add(paidAmount / 100.0);
                //扣率
                rowValue.add(feeRate);
            }

            //银行卡excel “手续费”放在“实收金额”后面
            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
                //实收金额
                rowValue.add(actualReceiveAmount / 100.0);
                rowValue.add(tradeFee / 100.0);
            } else {
                rowValue.add(tradeFee / 100.0);
                //实收金额
                rowValue.add(actualReceiveAmount / 100.0);
            }
            // 分账金额
            rowValue.add(sharingAmount / 100.0);
            //结算金额
            rowValue.add(clearingAmount / 100.0);
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_STORE_SN));
            rowValue.add(BeanUtil.getPropString(transaction, "store_client_sn"));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_STORE_NAME));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_TERMINAL_SN));
            rowValue.add(BeanUtil.getPropString(transaction, "terminal_client_sn"));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_TERMINAL_NAME));
            rowValue.add(getValue(OrderUtil.getTerminalTypeLanguageKey(BeanUtil.getPropString(transaction, "terminal_type"))));
            rowValue.add(BeanUtil.getPropString(transaction, "terminal_device_fingerprint"));

            Object reflect = transaction.get(Transaction.REFLECT);
            if ((reflect instanceof String) && org.apache.commons.lang3.StringUtils.contains((String) reflect, Transaction.REFLECT_DEPOSIT_TYPE)) {
                ReflectService reflectService = SpringUtil.getBean(ReflectService.class);
                reflect = reflectService.getReflectRemark(reflect);
            }

            if (DBSelectContext.getContext().getSelectDb() != UpayQueryType.UPAY_SWIPE) {
                rowValue.add(BeanUtil.getPropString(transaction, Transaction.OPERATOR));
                rowValue.add(BeanUtil.getPropString(transaction, "operator_name"));
                if (reflect instanceof Map) {
                    rowValue.add(CollectionUtils.isEmpty((Map) reflect) ? "" : WosaiJsonUtils.toJSONString(reflect));
                } else {
                    rowValue.add(Objects.isNull(reflect) ? "" : "" + reflect);
                }
            }

            rowValue.add(BeanUtil.getPropString(transaction, "pay_fund_channel", ""));
            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.PAY_TYPE, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.BATCH_BILL_NO, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.SYS_TRACE_NO, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.BANK_TYPE, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.LAKALA_MERC_ID, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.LAKALA_TERM_ID, ""));
                rowValue.add(BeanUtil.getPropString(transaction, LanguageUtil.TRADE_NO, ""));
                String channelFinishTime = BeanUtil.getPropString(transaction, Transaction.CHANNEL_FINISH_TIME);
                if (StringUtils.hasText(channelFinishTime)) {
                    rowValue.add(2, CommonConstant.DETAIL_SDF.get().format(Long.parseLong(channelFinishTime)));
                } else {
                    rowValue.add(2, "");
                }
            }

            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
                if (reflect instanceof Map) {
                    rowValue.add(CollectionUtils.isEmpty((Map) reflect) ? "" : WosaiJsonUtils.toJSONString(reflect));
                } else {
                    rowValue.add(Objects.isNull(reflect) ? "" : "" + reflect);
                }
            }
            rowValue.add(BeanUtil.getPropString(transaction, MCH_DISCOUNT_ORIGIN_TYPE, ""));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_MERCHANT_NAME));
            StatementCommonUtil.appendLine(details, rowValue);

        }
    }

    public static void appendSummaryRow(SheetUtil sheetUtil, TransactionQuerySummaryInfo summary) {
        String s1 = "总交易成功金额为：" + formatMoneyToString(summary.getAmount(), 2) + ", 总交易成功笔数为: " + summary.getCount() +
                ", 商户优惠金额为: " +formatMoneyToString(summary.getMerchantDiscount(),2) + ", 实收金额为: " + formatMoneyToString(summary.getActualReceiveAmount(), 2) +
                ", 手续费为: " + formatMoneyToString(summary.getFee(), 2) + ", 分账金额为: " + formatMoneyToString(summary.getSharingAmount(), 2) +
                ", 结算金额为: " + formatMoneyToString(summary.getClearingAmount(), 2) + ", 储值核销金额为: " + formatMoneyToString(summary.getStorePayAmount(), 2) +
                ", 储值核销结算金额为: " + formatMoneyToString(summary.getStorePayClearingAmount(), 2) +
                ", 收款净额到账金额为: " + formatMoneyToString(summary.getClearingAmount() - summary.getStorePayReceiveAmount() - summary.getChargeClearingAmount() - summary.getLiquidationClearingAmount(), 2);;

        String s2 = "";
        if (!(DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE)) {
            s2 += "，移动支付充值金额为：" + formatMoneyToString(summary.getStoreInAmount() - summary.getStoreInCashAmount() - summary.getStoreInOtherChargeAmount() , 2) + ", 移动支付充值笔数为: " + (summary.getStoreInCount() - summary.getStoreInCashCount() - summary.getStoreInOtherChargeCount()) +
//                ", 商户优惠金额为: " + summary.getMerchantDiscount() + ", 实收金额为: " + formatMoneyToString(summary.getActualReceiveAmount(), 2) +
                    ", 充值手续费为: " + formatMoneyToString(summary.getStoreInFee(), 2) +
                    ", 储值充值服务费为: " + formatMoneyToString(summary.getStoreInSharingAmount(), 2) +
//                + ", 分账金额为: " + formatMoneyToString(summary.getSharingAmount(), 2) +
                    ", 充值结算金额为: " + formatMoneyToString(summary.getStoreInClearingAmount(), 2) +
                    ", 现金充值金额为: " + formatMoneyToString(summary.getStoreInCashAmount(), 2) +
                    ", 现金充值笔数为: " + summary.getStoreInCashCount() +
                    ", 储值充值到账金额为: " + formatMoneyToString(summary.getStoreInClearingAmount() - summary.getStoreInCashClearingAmount() - summary.getStoreInOtherChargeClearingAmount() - summary.getStoreInLiquidationClearingAmount(), 2);

        }
        sheetUtil.appendRow(Arrays.asList(s1 + s2));
        sheetUtil.mergeCell(sheetUtil.getCurrentRow(), 0, sheetUtil.getCurrentRow(), 30);
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            sheetUtil.appendRow(Arrays.asList("说明：1.总实收金额为：付款金额+预授权完成金额-退款金额-预授权完成撤销；2.预授权和预授权撤销的金额不计算在内"));
        } else {
            sheetUtil.appendRow(Arrays.asList("说明：1.总交易成功金额为：收款金额+预授权完成金额-退款金额，且都为成功状态；2.储值卡充值交易金额为：充值金额-充值退款金额；3.预授权和预授权撤销的金额不计算在内；4.结算金额为：实收金额-手续费-分账金额（含服务费）；5.到账金额=收款净额到账金额+储值充值到账金额"));
        }
        sheetUtil.mergeCell(sheetUtil.getCurrentRow(), 0, sheetUtil.getCurrentRow(), 18);
        sheetUtil.appendRow(Arrays.asList("说明：银行卡交易隔日根据拉卡拉对账单进行勾兑，每日15:00前更新前一天交易的手续费和结算金额；直清交易为支付宝、微信、银行机构等直接结算给商家的交易，若存在直清交易，在到账金额中会做扣除"));
    }

    /**
     * 金额转为字符串
     *
     * @param money
     * @param scaleSize
     * @return
     */
    public static String formatMoneyToString(Object money, int scaleSize) {
        if (money == null) {
            return "";
        } else {
            return new BigDecimal(money.toString()).setScale(scaleSize, BigDecimal.ROUND_HALF_UP).toString();
        }
    }
}
