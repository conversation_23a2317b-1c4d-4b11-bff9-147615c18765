package com.wosai.upay.transaction.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang3.tuple.ImmutablePair;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.transaction.enums.TradeType;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.model.param.AccountBookOrderByType;

/**
 * 账本排序工具类
 *
 * <AUTHOR>
 */
public final class AccountBookUtils {
    private static final Cache<String, ImmutablePair<Boolean, List<Integer>>> TRADE_STATUS_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.HOURS)
            .maximumSize(1000)
            .build();

    // 查询全部流水类型（注意：需要按顺序添加）
    private static final List<Integer> ALL_TRADE_STATUS = new ArrayList<Integer>(Arrays.asList(
                TradeType.PAID.getCode(),
                TradeType.REFUNDED.getCode(),
                TradeType.DEPOSIT_CANCEL.getCode(),
                TradeType.DEPOSIT_CONSUME.getCode(),
                TradeType.DEPOSIT_CONSUME_CANCEL.getCode(),
                TradeType.DEPOSIT_FREEZE.getCode()
            ));

    private static final String ALL_TRADE_STATUS_STR = ALL_TRADE_STATUS.stream().map(String::valueOf).collect(Collectors.joining(","));
    private static final ImmutablePair<Boolean, List<Integer>> EMPTY_TRADE_STATUS_RESULT = ImmutablePair.of(true, Collections.emptyList());

    public static OrderBy getOrderBy(int orderBy) {
        // 1 表示按交易金额降序
        if (orderBy == AccountBookOrderByType.ORIGIN_AMOUNT_DESC) {
            return new OrderBy(Transaction.ORIGINAL_AMOUNT, OrderBy.OrderType.DESC);
        }
        // 2 表示按交易金额升序
        if (orderBy == AccountBookOrderByType.ORIGIN_AMOUNT_ASC) {
            return new OrderBy(Transaction.ORIGINAL_AMOUNT, OrderBy.OrderType.ASC);
        }
        // 3 表示按时间降序
        if (orderBy == AccountBookOrderByType.CTIME_DESC) {
            return new OrderBy("ctime", OrderBy.OrderType.DESC);
        }
        // 4 表示按时间升序
        if (orderBy == AccountBookOrderByType.CTIME_ASC) {
            return new OrderBy("ctime", OrderBy.OrderType.ASC);
        }

        return new OrderBy("ctime", OrderBy.OrderType.DESC);
    }

    public static ImmutablePair<Boolean, List<Integer>> getSortedTradeStatus(String tradeStatus){
        if(StringUtil.isEmpty(tradeStatus)) {
            return EMPTY_TRADE_STATUS_RESULT;
        }
        ImmutablePair<Boolean, List<Integer>> result = TRADE_STATUS_CACHE.getIfPresent(tradeStatus);
        if(result == null) {
            List<Integer> queryTradeStatus = Arrays.stream(tradeStatus.split(","))
                    .filter(v -> v.length() > 0)
                    .map(Integer::valueOf)
                    .distinct()
                    .sorted((o1, o2) -> o1 < o2 ? -1 : 1)
                    .collect(Collectors.toList());
            if(queryTradeStatus.isEmpty()) {
                return EMPTY_TRADE_STATUS_RESULT; 
            }
            boolean isFullTypeQuery = ALL_TRADE_STATUS_STR.equals(queryTradeStatus.stream().map(String::valueOf).collect(Collectors.joining(",")));
            result = ImmutablePair.of(isFullTypeQuery, queryTradeStatus);
            TRADE_STATUS_CACHE.put(tradeStatus, result);
        }
        return result;
    }

}
