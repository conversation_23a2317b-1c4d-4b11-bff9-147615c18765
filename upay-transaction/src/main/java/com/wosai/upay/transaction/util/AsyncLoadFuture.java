package com.wosai.upay.transaction.util;


import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;

/**
 * @param <V>
 * <AUTHOR>
 */
public class AsyncLoadFuture<V> extends FutureTask<V> {
    // 记录下future开始执行的时间
    private long executeStartTime = 0;
    // 记录下future执行结束时间
    private long executeEndTime = 0;
    @JsonIgnore
    private Thread runnerThread;

    private AsyncLoadMsg initMsg;

    public AsyncLoadFuture(Callable<V> callable) {
        super(callable);
    }

    public AsyncLoadFuture(AsyncLoadRunnable runnable, V result) {
        super(runnable, result);
        initMsg = runnable.getInitMsg();
    }

    @Override
    protected void done() {
        // 记录一下时间点，Future在cancel调用，正常完成，或者运行出异常都会回调该方法
        executeEndTime = System.currentTimeMillis();
    }

    @Override
    public void run() {
        executeStartTime = System.currentTimeMillis();
        // 记录的下具体pool中的runnerThread
        runnerThread = Thread.currentThread();
        super.run();
    }

    public long getExecuteStartTime() {
        return executeStartTime;
    }

    public long getExecuteEndTime() {
        return executeEndTime;
    }

    public AsyncLoadMsg getConfig() {
        return initMsg;
    }

    public Thread getRunnerThread() {
        return runnerThread;
    }
}
