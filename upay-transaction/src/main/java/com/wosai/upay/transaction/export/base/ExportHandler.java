package com.wosai.upay.transaction.export.base;

import com.google.common.collect.Sets;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.MapUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.side.util.OssDownloadUtil;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.StatementTaskStyle;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.model.SensitiveProperties;
import com.wosai.upay.transaction.model.StatementObjectConfig;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.service.StatementObjectConfigService;
import com.wosai.upay.transaction.service.TaskLogService;
import com.wosai.upay.transaction.util.OssFileUploader;
import com.wosai.upay.transaction.util.SmtpMailSender;
import com.wosai.upay.transaction.util.StatementTransactionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 */
@Component
public class ExportHandler implements ApplicationContextAware {

    public static final Logger logger = LoggerFactory.getLogger(ExportHandler.class);

    @Autowired
    private TaskLogService taskLogService;

    @Autowired
    private StatementObjectConfigService configService;

    private ApplicationContext applicationContext;

    @Autowired
    private StatementTransactionUtil statementTransactionUtil;
    @Autowired
    private SmtpMailSender smtpMailSender;

    @Resource
    private BusinessService businessService;

    @Autowired
    private SensitiveProperties sensitiveProperties;

    @Value("${mail.accountName:收钱吧}")
    private String accountName;

    private Map<Integer,ActionFunction> ACTION_FUNCTION = MapUtils.hashMap(
            StatementTaskLog.FINISH_OPERATION_NULL,(c,s)->{},
            StatementTaskLog.FINISH_OPERATION_EMAIL,new MailAction()
    );

    private static final String OSS_TRANSACTION_DIR_PREFIX = "portal/transaction/";

    private String STATEMENT_DIR_PREFIX;

    private static long EXPIRE = 1000l * 60 * 60 * 24 * 30;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        String logDir = applicationContext.getEnvironment().getProperty("logDir", String.class, ".");
        /**
         * + "statement" + File.separator;
         */
        STATEMENT_DIR_PREFIX = logDir + File.separator;
    }


    private void init(Class clazz, String taskLogId) {
        BaseExportService exportService = (BaseExportService) applicationContext.getBean(clazz);
        RunContext runContext = RunContext.currentContext();

        //初始化运行需要的service 任务id
        runContext.setService(exportService);
        runContext.setTaskLogId(taskLogId);
        //初始化导出临时文件路径
        String localDirPath = STATEMENT_DIR_PREFIX + (new SimpleDateFormat("yyyy-MM-dd").format(new Date()) + File.separator) + taskLogId + File.separator;
        runContext.setLocalDirPath(localDirPath);

        //初始化任务详情,配置信息
        Map taskDetail = taskLogService.getTaskApplyLog(taskLogId);

        String includes = BeanUtil.getPropString(taskDetail, StatementTaskLog.INCLUDES, "1");
        Integer upayQueryType = (Integer) BeanUtil.getNestedProperty(taskDetail, StatementTaskLog.PAYLOAD + "." + CommonConstant.UPAY_QUERY_TYPE);
        if (UpayQueryType.UPAY_SWIPE.getCode().equals(upayQueryType)) {
            //刷卡交易不生效ncludes
            includes = "1";
        }
        //设置导出的类别
        runContext.setExportStyle(StatementTaskStyle.getStyleByInclude(includes));
        runContext.setExportIncludes(includes);

        int taskType = BeanUtil.getPropInt(taskDetail, StatementTaskLog.TYPE);
        if (StatementTaskLog.TYPE_TRANSACTION == taskType || StatementTaskLog.TYPE_TRANSACTION_GROUP == taskType
                || StatementTaskLog.TYPE_HISTORICAL_TRANSACTION == taskType || StatementTaskLog.TYPE_HISTORICAL_GROUP_TRANSACTION == taskType ) {
            boolean isKAStatement = (boolean) BeanUtil.getNestedProperty(taskDetail, StatementTaskLog.PAYLOAD + "." + CommonConstant.IS_KA_STATEMENT);
            //设置是否是ka对账单
            runContext.setIsKAStatement(isKAStatement);
        }

        Map queryFilter = (Map) BeanUtil.getProperty(taskDetail, StatementTaskLog.PAYLOAD);
        String objectId = BeanUtil.getPropString(queryFilter, StatementObjectConfig.OBJECT_ID);


        Map taskConfig = null;
        if ("sp-v2".equals(objectId)) {
            Map sp = configService.getConfigByObjectId("sp");
            taskConfig = (Map) BeanUtil.getProperty(queryFilter, "statementConfig");
            sp.put("language", taskConfig.get("language"));
            sp.put("split_type", taskConfig.get("split_type"));
            ((Map) sp.get("sheet_params")).put("sheet_type", MapUtil.getMap(taskConfig, "sheet_params").get("sheet_type"));
            Map sheetDetailTypeMap = ((Map) sp.get("sheet_detail_params"));
            if (sheetDetailTypeMap != null) {
                sheetDetailTypeMap.put("sheet_detail_type"
                        , MapUtil.getMap(taskConfig, "sheet_detail_params").get("sheet_detail_type"));
            } else {
                sp.put("sheet_detail_params", new HashMap<>()
                        .put("sheet_detail_type", MapUtil.getMap(taskConfig, "sheet_detail_params").get("sheet_detail_type")));
            }
            taskConfig = sp;
        } else {
            taskConfig = configService.getConfigByObjectId(objectId);
        }
        Map configSheetParams = (Map) BeanUtil.getProperty(taskConfig, StatementObjectConfig.SHEET_PARAMS);
        Collection<Integer> payWaysObj = (Collection<Integer>) configSheetParams.get(StatementObjectConfig.PAYWAY);
        if (!CollectionUtils.isEmpty(payWaysObj)) {
            //记账以及外卖类型的对账单，payway +上3个
            HashSet<Integer> payWays = new HashSet<>(payWaysObj);
            //银行卡
            if (UpayQueryType.UPAY_SWIPE.getCode().equals(upayQueryType)) {
                payWays = Sets.newHashSet(CommonConstant.BANK_CARD);
            } else {
                Set<Integer> chargePayWayCodes = businessService.getAllChargePayWay().keySet();
                if (includes.contains(StatementTaskLog.INCLUDE_CHARGE + "")) {
                    payWays.addAll(chargePayWayCodes);
                } else {
                    chargePayWayCodes.forEach(payWays::remove);
                }
            }
            configSheetParams.put(StatementObjectConfig.PAYWAY, new ArrayList<>(payWays));
        }
        runContext.setRunDetail(taskDetail);
        runContext.setRunConfig(taskConfig);
        //初始化导出语言信息
        runContext.initLanguage();

        String className = clazz.getSimpleName();
        logger.info("export task init {} ,use class {} ", taskLogId, className);
    }

    public void handleTask(Class clazz, String taskLogId) {
        try {
            init(clazz, taskLogId);
            boolean needRun = preRunJob();
            if (needRun) {
                runJob();
            }
            postRunJob(null);
            safeFinishRunTaskHandler();
        } catch (Throwable e) {
            postRunJob(e);
        } finally {
            RunContext.currentContext().clear();
        }
    }


    /**
     * 开始处理,预先判断是否需要进行处理逻辑
     *
     * @return 是否需要真正执行任务
     */
    private boolean preRunJob() {
        RunContext runContext = RunContext.currentContext();
        String taskLogId = runContext.getTaskLogId();
        runContext.start();
        //1.判断是否有相同任务决定是否执行
        String urlPath = taskLogService.getSameTaskFileUrl(taskLogId);
        if (urlPath != null) {
            runContext.setExportFileUrl(urlPath);
            return false;
        }
        return true;
    }

    /**
     * 执行导出操作,导出完成的标志是获取了oss的url
     */
    private void runJob() throws Exception {
        RunContext runContext = RunContext.currentContext();
        BaseExportService exportService = runContext.getService();
        String taskLogId = runContext.getTaskLogId();
        //更新导出状态
        taskLogService.updateTaskApplyLogStatus(taskLogId, StatementTaskLog.APPLY_STATUS_IN_SERVICE);
        logger.info("export task  {} change status to {}", taskLogId, StatementTaskLog.APPLY_STATUS_IN_SERVICE);
        //导出获取文件(zip 或者单个文件)
        File file = exportService.export(taskLogId);
        taskLogService.updateTaskApplyLogStatus(taskLogId, StatementTaskLog.APPLY_STATUS_BEFORE_UPLOAD);
        String filename = file.getName();
        String fullName = OSS_TRANSACTION_DIR_PREFIX + taskLogId + "/" + filename;
        String ossFileUrlSummary = statementTransactionUtil.uploadStatementToOSS(file, fullName);
        logger.info("export task  {} upload finish , oss url {}", taskLogId, ossFileUrlSummary);
        RunContext.currentContext().setExportFileUrl(OSS_TRANSACTION_DIR_PREFIX + taskLogId + "/" + URLEncoder.encode(filename));
    }


    private void postRunJob(Throwable e) {
        RunContext runContext = RunContext.currentContext();
        String taskLogId = runContext.getTaskLogId();
        runContext.finish();
        Map<String, Object> exportResult = new HashMap<>(16);
        int applyStatus = StatementTaskLog.APPLY_STATUS_SUCCESS;
        if (e != null) {
            logger.error("export task {} exception ", taskLogId, e);
            applyStatus = StatementTaskLog.APPLY_STATUS_FAIL;
            exportResult.put("err_msg", "系统繁忙，对账单生成失败，请稍后重试");
        } else {
            exportResult.put("statement_oss_url", runContext.getExportFileUrl());
        }
        exportResult.put("task_run_info", runContext.runInfo());
        taskLogService.updateTaskApplyLog(CollectionUtil.hashMap(
                ConstantUtil.KEY_ID, taskLogId,
                StatementTaskLog.APPLY_STATUS, applyStatus,
                StatementTaskLog.APPLY_RESULT, JacksonUtil.toJsonString(exportResult),
                StatementTaskLog.OPERATE_TIME, runContext.operatorTime(),
                StatementTaskLog.STATEMENT_ROW, runContext.totalRow())
        );
        logger.info("export task {} finish, result status {}", taskLogId, applyStatus);
    }

    public void safeFinishRunTaskHandler(String taskLogId, String fileUrl){
        RunContext context = RunContext.currentContext();
        context.setExportFileUrl(fileUrl);
        context.setTaskLogId(taskLogId);
        safeFinishRunTaskHandler();
        context.clear();
    }

    private void safeFinishRunTaskHandler(){
        RunContext context = RunContext.currentContext();
        Map taskApplyLog = null;
        try {
            String taskLogId = context.getTaskLogId();
            taskApplyLog = taskLogService.getTaskApplyLog(taskLogId);
            int operation = BeanUtil.getPropInt(taskApplyLog, StatementTaskLog.FINISH_OPERATION, StatementTaskLog.FINISH_OPERATION_NULL);
            ACTION_FUNCTION.get(operation).doAction(context,taskApplyLog);
            if (taskApplyLog != null) {
                setFinishOperation(taskApplyLog, null, StatementTaskLog.EXTRA_STATUS_SUCCESS);
            }
        }catch (Exception e) {
            if (taskApplyLog != null) {
                String message = e.getMessage();
                setFinishOperation(taskApplyLog, message,StatementTaskLog.EXTRA_STATUS_FAIL);
            }
        }
    }

    private void setFinishOperation(Map taskApplyLog,String message,Integer status){
        Map<String,Object> extraMap;
        Object property = BeanUtil.getProperty(taskApplyLog, StatementTaskLog.OPERATION_EXTRA);
        String id = BeanUtil.getPropString(taskApplyLog, DaoConstants.ID);
        if(property != null && property instanceof Map){
            extraMap = (Map<String, Object>) property;
        }else {
            extraMap = new HashMap<>();
        }
        if(!StringUtils.isEmpty(message)) {
            extraMap.put(StatementTaskLog.EXTRA_ERROR_MSG, message);
        }
        extraMap.put(StatementTaskLog.EXTRA_STATUS,status);
        taskLogService.updateTaskApplyLog(CollectionUtil.hashMap(
                ConstantUtil.KEY_ID, id,
                StatementTaskLog.OPERATION_EXTRA, extraMap
        ));
    }

    public class MailAction implements ActionFunction{

        public static final String TO = "to";
        public static final String HTML_BODY = "尊敬的收钱吧商家，您好！%s 已导出，请点击下方链接下载<br>注意：文件有效期为30天，请及时查收" +
                "<br> <hr>" +
                "<a href= %s >%s</a>";
        @Override
        public void doAction(RunContext context,Map statementTaskLog) throws Exception{
            String title = BeanUtil.getPropString(statementTaskLog, StatementTaskLog.TITLE);
            Map<String,Object> extraMap = (Map<String, Object>) BeanUtil.getProperty(statementTaskLog,StatementTaskLog.OPERATION_EXTRA,Map.class);
            List<String> toAddressesList = (List<String>) BeanUtil.getProperty(extraMap,TO,List.class);
            String toAddresses = String.join(",",toAddressesList);
            String ossUrl = URLDecoder.decode(context.getExportFileUrl());
            String fullOssUrl = OssDownloadUtil.getPayGroupDownloadUrl(OssFileUploader.IMAGE_BUCKET_NAME, ossUrl, System.currentTimeMillis() + EXPIRE);
            SmtpMailSender.Mail mail = new SmtpMailSender.Mail()
                    .setAccountName(sensitiveProperties.getMailUsername())
                    .setAccountAlias(accountName)
                    .setSubject(title)
                    .setHtmlBody(String.format(HTML_BODY,title,fullOssUrl,title))
                    .setToAddresses(toAddresses);
            logger.info("邮件发送 taskId:{} 接收方:{} 主题:{}",context.getTaskLogId(),mail.getSubject(),mail.getToAddresses());
            smtpMailSender.send(mail);
        }
    }

    @FunctionalInterface
    public interface ActionFunction {
        void doAction(RunContext context,Map params) throws Exception;
    }

}