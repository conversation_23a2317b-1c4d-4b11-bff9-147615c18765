package com.wosai.upay.transaction.service.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class TerminalCode implements Serializable {

    private String deviceFingerprint;

    private String name;

    private int type;

    private String typeName;

    private String vendorAppAppid;

    private String vendorAppName;

    private Integer category;
    private String categoryName;


    private String sn;

    public TerminalCode() {
    }

    public TerminalCode(String deviceFingerprint, String name) {
        this.deviceFingerprint = deviceFingerprint;
        this.name = name;
    }

    public String getDeviceFingerprint() {
        return deviceFingerprint;
    }

    public void setDeviceFingerprint(String deviceFingerprint) {
        this.deviceFingerprint = deviceFingerprint;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getVendorAppAppid() {
        return vendorAppAppid;
    }

    public void setVendorAppAppid(String vendorAppAppid) {
        this.vendorAppAppid = vendorAppAppid;
    }

    public String getVendorAppName() {
        return vendorAppName;
    }

    public void setVendorAppName(String vendorAppName) {
        this.vendorAppName = vendorAppName;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
}
