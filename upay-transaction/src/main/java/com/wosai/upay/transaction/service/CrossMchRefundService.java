package com.wosai.upay.transaction.service;

import avro.shaded.com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.transaction.constant.QueryFlagConstant;
import com.wosai.upay.transaction.model.QueryResultVO;
import com.wosai.upay.transaction.repository.DataRepository;
import com.wosai.upay.transaction.util.CommonUtil;
import com.wosai.upay.transaction.util.StatementTransactionUtil;
import com.wosai.upay.transaction.util.TransactionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Component
public class CrossMchRefundService {

    @Autowired
    public StoreService storeService;

    @Autowired
    public DataRepository dataRepository;

    @Autowired
    public BusinessService businessService;

    @Autowired
    StatementTransactionUtil statementTransactionUtil;

    private static final Long LIMIT_COUNT = 5000L;


    public QueryResultVO doQueryResult(String merchantId, String merchantUserId, List<String> storeIds, String terminalId, Long start, Long lastTime, String lastId, Long limit) {
        QueryResult queryResult = doQuery(merchantId, merchantUserId, storeIds, terminalId, start, lastTime, lastId, limit);
        return new QueryResultVO(queryResult.getResult(), queryResult.getLastTime(), queryResult.getLastId());
    }

    private QueryResult doQuery(String merchantId, String merchantUserId, List<String> storeIds, String terminalId, Long start, Long lastTime, String lastId, Long limit) {
        StringBuilder sql = new StringBuilder("select * from cross_mch_refund_tx where 1=1  ");
        sql.append(" and refund_merchant_id =  '").append(merchantId).append("'");
        if (storeIds != null && storeIds.size() > 0) {
            sql.append(" and refund_store_id in ('").append(CommonUtil.join("','", storeIds)).append("') ");
        }
        if (!StringUtil.empty(merchantUserId)) {
            sql.append(" and refund_operator =  ").append("'").append(merchantUserId).append("'");
        }
        if (!StringUtil.empty(terminalId)) {
            sql.append(" and refund_terminal_id =  ").append("'").append(terminalId).append("'");
        }
        sql.append(" and ctime >= ").append(start);
        if (lastId != null) {
            sql.append(" and ( ctime < ").append(lastTime).append(" or ( ctime = ").append(lastTime).append(" and id < '").append(lastId).append("' )) ");
        } else {
            sql.append(" and ctime <= ").append(lastTime);
        }
        sql.append(" order by ctime desc,id desc limit ").append(limit);
        List<Map<String, Object>> result = dataRepository.getStatementJdbcTemplate().queryForList(sql.toString());
        if (result.size() == 0) {
            return new QueryResult();
        }
        formatTransactions(result, Arrays.asList(
                QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO
        ));
        Map lastRecord = result.get(result.size() - 1);
        return new QueryResult(result, BeanUtil.getPropLong(lastRecord, ConstantUtil.KEY_CTIME), BeanUtil.getPropString(lastRecord, ConstantUtil.KEY_ID));
    }

    private void formatTransactions(Object transactions, List queryFlags) {
        List<Map<String, Object>> list = new LinkedList();
        if (transactions instanceof List) {
            list = (List) transactions;
        } else if (transactions instanceof Map) {
            list.add((Map<String, Object>) transactions);
        } else {
            return;
        }
        businessService.addBusinessInfoWithQueryFlag(transactions, queryFlags);
        for (Map<String, Object> transaction : list) {
            TransactionUtil.transformReflect(transaction);
            TransactionUtil.jsonFormatOrder(transaction);
            TransactionUtil.expandTransactionItemsPayments(transaction, false);
            TransactionUtil.expandTransactionItemTradeInfo(transaction);
            TransactionUtil.calculateExtendFields(transaction);
        }
    }


    private static class QueryResult {
        private List<Map<String, Object>> result = Lists.newArrayList();
        private long lastTime;
        private String lastId;

        public QueryResult() {
        }

        public QueryResult(List<Map<String, Object>> result, long lastTime, String lastId) {
            this.result = result;
            this.lastId = lastId;
            this.lastTime = lastTime;
        }

        public List<Map<String, Object>> getResult() {
            return result;
        }

        public void setResult(List<Map<String, Object>> result) {
            this.result = result;
        }

        public long getLastTime() {
            return lastTime;
        }

        public void setLastTime(long lastTime) {
            this.lastTime = lastTime;
        }

        public String getLastId() {
            return lastId;
        }

        public void setLastId(String lastId) {
            this.lastId = lastId;
        }
    }
}
