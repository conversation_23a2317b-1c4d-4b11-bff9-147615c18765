package com.wosai.upay.transaction.service.dao.base;

import java.time.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphO;
import com.alibaba.csp.sentinel.context.ContextUtil;
import com.google.common.collect.Lists;
import com.wosai.upay.cashdesk.api.bean.CashDeskTransaction;
import com.wosai.upay.cashdesk.api.request.CashDeskTradeQueryListRequest;
import com.wosai.upay.cashdesk.api.service.CashDeskTradeService;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.DateTimeConst;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.service.config.HbaseDispatcherTimeConfig;
import com.wosai.upay.transaction.service.config.UpayTransactionJsonRpcInterceptor;
import com.wosai.upay.transaction.service.service.common.ReflectService;
import com.wosai.upay.transaction.util.*;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.filter.RegexStringComparator;
import org.apache.hadoop.hbase.filter.RowFilter;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.params.CommonParams;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.google.common.base.Joiner;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.google.common.collect.Maps;
import com.wosai.common.exception.CommonException;
import com.wosai.common.utils.WosaiJsonUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.middleware.hera.toolkit.trace.SupplierWrapper;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.service.model.query.BaseQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.service.remote.ElasticsearchService;
import com.wosai.upay.transaction.util.SolrHBaseUtils.SolrPartition;
import lombok.SneakyThrows;
import org.springframework.util.Assert;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * @param <P>
 * <AUTHOR>
 */
public abstract class HBaseDao<P extends BaseQuery> {

    public static final Logger logger = LoggerFactory.getLogger(HBaseDao.class);

    public static final byte[] FAMILY = Bytes.toBytes("f1");

    protected static final String COUNT = "count";

    public static final String QUERY_LIST = "queryList";

    protected TableName tableName;

    protected TableName swipeTableName;

    protected List<String> swipeCollections;

    protected static final Interner<String> STRING_POOL = Interners.newWeakInterner();

    protected static final Map<String, TableName> TABLE_NAMES = new ConcurrentHashMap<>();
    private static final String[] ES_QUERY_COLUMN = {DaoConstants.ID, DaoConstants.CTIME};
    private static final String[] ES_MEMO_QUERY_COLUMN = {DaoConstants.ID, Transaction.TSN, DaoConstants.CTIME};

    @Autowired
    protected ElasticsearchService elasticsearchService;
    @Resource
    protected RestHighLevelClient tradeMemoRestHighLevelClient;
    @Autowired
    HbaseDispatcherTimeConfig hbaseDispatcherTimeConfig;

    @Resource
    protected CashDeskTradeService cashDeskTradeService;

    @Resource
    protected BusinessService businessService;
    @Resource
    protected ReflectService reflectService;

    protected void limit(SolrQuery solrQuery, P query) {
        if (!Objects.isNull(query.getLimit())) {
            solrQuery.setStart(query.getOffset());
            solrQuery.setRows(query.getLimit());
        }
    }

    protected void orderBy(SolrQuery solrQuery, P query) {
        List<OrderBy> orderBys = query.getOrderBys();
        if (CollectionUtils.isEmpty(orderBys)) {
            return;
        }
        orderBys.forEach(orderBy -> {
            if (orderBy.getOrder() == OrderBy.OrderType.DESC) {
                solrQuery.addSort(orderBy.getField(), SolrQuery.ORDER.desc);
            } else {
                solrQuery.addSort(orderBy.getField(), SolrQuery.ORDER.asc);
            }
        });
    }


    protected void queryStrategy(SolrQuery solrQuery, List<String> fq, List<String> q, Long startTime) {
        if (CollectionUtils.isNotEmpty(fq)) {
            solrQuery.addFilterQuery(fq.toArray(new String[fq.size()]));
        }

        if (CollectionUtils.isNotEmpty(q)) {
            solrQuery.setQuery(Joiner.on(SolrUtils.SEPARATOR_AND).join(q));
        }

        // 超过4个月之前的数据不需要设置缓存
        solrQuery.set(CommonParams.CACHE, hbaseDispatcherTimeConfig.needCache(startTime));
    }

    protected abstract int getSolrTimeout(Integer solrTimeout);

    protected abstract void where(SolrQuery solrQuery, P query);

    protected abstract void esWhere(BoolQueryBuilder esQuery, P query);

    protected abstract void buildCashDeskRequest(CashDeskTradeQueryListRequest request, P query, BusinessService businessService);

    @SneakyThrows
    public List<String> queryListByMemo(P query) {
        startTimeLimit(query);
        String resource = UpayTransactionJsonRpcInterceptor.INTO_METHOD.get() + "_queryTradeMemo";
        if (!SphO.entry(resource, EntryType.IN, 1)) {
            throw new RuntimeException("请求过于频繁,请稍后重试");
        }
        try {
            return queryListByEsMemo(query);
        } finally {
            if (ContextUtil.getContext() != null) {
                SphO.exit(1);
            }
        }
    }

    public Long count(P query) {
        startTimeLimit(query);
        String resource = UpayTransactionJsonRpcInterceptor.INTO_METHOD.get() + "_queryList_false";
        if (!SphO.entry(resource, EntryType.IN, 1)) {
            throw new RuntimeException("请求过于频繁,请稍后重试");
        }
        try {
            SolrQuery solrQuery = new SolrQuery("*:*");
            where(solrQuery, query);
            solrQuery.setRows(0);
            List<String> collections = getCollections(query);
            if (CollectionUtils.isEmpty(collections)) {
                return 0L;
            }
            List<QueryResponse> responses = SolrHBaseUtils.query(collections, solrQuery, COUNT, getSolrTimeout(query.getSolrTimeout()));

            return responses.parallelStream()
                    .mapToLong(response -> response.getResults().getNumFound())
                    .sum();
        } finally {
            if (ContextUtil.getContext() != null) {
                SphO.exit(1);
            }
        }
    }

    @SneakyThrows
    public List<Map<String, Object>> queryList(P query) {
        startTimeLimit(query);
        boolean isQueryEs = query.isQueryEs();
        String resource = UpayTransactionJsonRpcInterceptor.INTO_METHOD.get() + "_queryList_" + isQueryEs;
        if (!SphO.entry(resource, EntryType.IN, 1)) {
            throw new RuntimeException("请求过于频繁,请稍后重试");
        }
        try {
            List<Pair<String, Long>> idCTimes;
            if (query.isQueryCashDesk()) {
                try {
                    idCTimes = queryListByCashDesk(query);
                } catch (Exception e) {
                    logger.error("error in queryListByCashDesk", e);
                    throw ErrorMessageEnum.GET_CASH_DESK_ERROR.getBizException();
                }
            } else if (isQueryEs) {
                try {
                    idCTimes = queryListByEs(query);
                } catch (Exception e) {
                    logger.error("error in queryListByEs", e);
                    query.setQueryEs(false);
                    idCTimes = queryListBySolr(query);
                }
            } else {
                idCTimes = queryListBySolr(query);
            }
            if (CollectionUtils.isEmpty(idCTimes)) {
                return Collections.emptyList();
            }
            return rowFilterByIds(idCTimes, query.getFilterColumns(), query.gethBaseTimeout());
        } finally {
            if (ContextUtil.getContext() != null) {
                SphO.exit(1);
            }
        }
    }

    private void startTimeLimit(P query) {
        if (query.getStartTime() != null && query.getStartTime() < hbaseDispatcherTimeConfig.getHotIndexStartTime()) {
            throw new BizException(CommonException.CODE_ACCESS_DENIED, String.format("不支持查询%s年前交易", hbaseDispatcherTimeConfig.getHotIndexLimitYear()));
        }
    }

    private List<Pair<String, Long>> queryListBySolr(P query) {
        SolrQuery solrQuery = new SolrQuery("*:*");
        solrQuery.setParam("fl", "id,ctime");
        where(solrQuery, query);
        limit(solrQuery, query);
        orderBy(solrQuery, query);
        List<String> collections = getCollections(query);
        if (CollectionUtils.isEmpty(collections)) {
            return Lists.newArrayList();
        }
        List<QueryResponse> responses = SolrHBaseUtils.query(collections, solrQuery, QUERY_LIST, getSolrTimeout(query.getSolrTimeout()));
        if (responses.isEmpty()) {
            return Lists.newArrayList();
        }

        return responses.stream()
                .flatMap(response -> response.getResults().parallelStream())
                .map(document -> Pair.of(document.getFieldValue(DaoConstants.ID).toString(), (Long) document.getFieldValue(DaoConstants.CTIME)))
                .collect(Collectors.toList());
    }

    private List<String> queryListByEsMemo(P query) throws Exception {
        BoolQueryBuilder mustQuery = QueryBuilders.boolQuery();
        esWhere(mustQuery, query);
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(mustQuery);
        builder.size(query.getLimit());
        builder.from(query.getOffset());
        builder.fetchSource(ES_MEMO_QUERY_COLUMN, Strings.EMPTY_ARRAY);
        logger.info("builder is {}", builder);
        SearchResponse response = elasticsearchService.search(tradeMemoRestHighLevelClient, builder);
        if (null == response
                || null == response.getHits()
                || response.getHits().getTotalHits() == 0) {
            return Collections.emptyList();
        }

        return Arrays.stream(response.getHits().getHits())
                .map(hits -> {
                    Map<String, Object> document = hits.getSourceAsMap();
                    return MapUtil.getString(document, Transaction.TSN);
                })
                .collect(Collectors.toList());
    }

    private List<Pair<String, Long>> queryListByEs(P query) throws Exception {
        BoolQueryBuilder mustQuery = QueryBuilders.boolQuery();
        esWhere(mustQuery, query);
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.query(mustQuery);
        builder.size(query.getLimit());
        builder.from(query.getOffset());
        if (CollectionUtils.isNotEmpty(query.getOrderBys())) {
            query.getOrderBys().forEach(orderBy -> {
                if (orderBy.getOrder() == OrderBy.OrderType.DESC) {
                    builder.sort(orderBy.getField(), SortOrder.DESC);
                } else {
                    builder.sort(orderBy.getField(), SortOrder.ASC);
                }
            });
        }
        builder.fetchSource(ES_QUERY_COLUMN, Strings.EMPTY_ARRAY);
        SearchResponse response = elasticsearchService.search(getTableNamePrefix(), builder, ((TransactionHBaseQuery) query).getMerchantIds());
        if (null == response
                || null == response.getHits()
                || response.getHits().getTotalHits() == 0) {
            return Collections.emptyList();
        }

        return Arrays.stream(response.getHits().getHits())
                .map(hits -> {
                    Map<String, Object> document = hits.getSourceAsMap();
                    return Pair.of(MapUtil.getString(document, DaoConstants.ID), MapUtil.getLong(document, DaoConstants.CTIME));
                })
                .collect(Collectors.toList());
    }

    private List<Pair<String, Long>> queryListByCashDesk(P query) throws Exception {

        CashDeskTradeQueryListRequest request = new CashDeskTradeQueryListRequest();
        buildCashDeskRequest(request, query, businessService);
        request.setPageSize(query.getLimit());
        request.setPage(query.getLimit() * query.getOffset() + 1);

        if (!org.springframework.util.CollectionUtils.isEmpty(query.getOrderBys())) {
            List<com.wosai.upay.cashdesk.api.utils.OrderBy> orderBys = new ArrayList<>();
            query.getOrderBys().forEach(orderBy -> {
                if (orderBy.getOrder() == OrderBy.OrderType.DESC) {
                    orderBys.add(new com.wosai.upay.cashdesk.api.utils.OrderBy(orderBy.getField(), com.wosai.upay.cashdesk.api.utils.OrderBy.OrderType.DESC));
                } else {
                    orderBys.add(new com.wosai.upay.cashdesk.api.utils.OrderBy(orderBy.getField(), com.wosai.upay.cashdesk.api.utils.OrderBy.OrderType.ASC));
                }
            });
            request.setOrderBy(orderBys);
        }
        List<CashDeskTransaction> response = cashDeskTradeService.getCashDeskTradeList(request);
        if (CollectionUtils.isEmpty(response)) {
            return Collections.emptyList();
        }

        return response.stream()
                .map(r -> {
                    String hexString = String.valueOf(Hex.encodeHex(Bytes.add(Bytes.toBytes(r.getMerchantId()), Bytes.toBytes(r.getTradeCtime()), Bytes.toBytes("t" + r.getId()))));
                    return Pair.of(hexString, r.getTradeCtime());
                })
                .collect(Collectors.toList());
    }

    /**
     * solr按照月份划分，近3个月数据使用hot，近6个月使用recent_6m，3个月之前的使用clod，查询策略
     * 1、通过传入的ctime范围,确认当日流水所属的月,查询该月索引(若该月为非近4月,强制查询不进行cache)
     * 2、根据流水号查询流水明细信息，由于当前根据流水号无法确定流水实际所属的日期, 查询hot别名索引,hot别名索引不存在时,查询cold索引
     * 3、APP账本包含近6月数据进行筛选查询，如果筛选的start_time < LocalDate.of(now.getYear(),now.getMonth(),1).plusMonths(-6) 走recent_6m索引进行查询
     */
    @Nullable
    protected List<String> getCollections(P query) {
        // 刷卡交易没有分表
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            return swipeCollections;
        }
        LocalDateTime now = LocalDateTime.now();

        // 指定索引分区，则使用指定的索引分区
        if (query.getSolrPartition() != null) {
            return getCollectionsBySolrPartition(query.getSolrPartition(), now);
        }

        // 默认根据根据时间计算 Collection
        return getCollectionsByTimestamp(query.getStartTime(), query.getEndTime(), now);
    }


    private long toMillis(LocalDateTime date) {
        return date.atOffset(ZoneOffset.of("+8")).toEpochSecond() * 1000;
    }

    /**
     * 根据开始时间和结束时间获取 Collections（区分冷热索引）
     */
    @Nullable
    private List<String> getCollectionsByTimestamp(Long startTime, Long endTime, LocalDateTime now) {
        long currentMillis = toMillis(now);
        if (endTime == null || endTime >= currentMillis) {
            endTime = currentMillis;
        }
        // 保持原有语义
        if (startTime == null) {
            // 3个月前的月初0时
            long threeMonthsStart = toMillis(now.toLocalDate()
                    .withDayOfMonth(1)
                    .plusMonths(-3).atStartOfDay());
            if (endTime > threeMonthsStart) {
                return getCollectionsBySolrPartition(SolrPartition.all, now);
            } else {
                return getCollectionsBySolrPartition(SolrPartition.cold, now);
            }
        }
        if (startTime < DateTimeConst.DATA_START_TIME) {
            startTime = hbaseDispatcherTimeConfig.getSolrLimitStartTime(DateTimeConst.DATA_START_TIME);
        }
        // 起始时间大于等于结束时间时无效
        if (startTime >= endTime) {
            return null;
        }
//        data_start_time | start  end | now
        // 不区分冷热
        return Lists.newArrayList(calculateSolrCollections(startTime, endTime));
    }

    /**
     * 通过指定的 Solr 冷热索引分区来获取对应的 Collection 列表，
     * <br/>
     * 如果是 {@link SolrPartition#all} 或 空值，则返回 hot & cold 两个并列集合，而非一整个
     */
    private List<String> getCollectionsBySolrPartition(SolrPartition solrPartition, LocalDateTime now) {
        // 基准时间：今天
        LocalDate today = now.toLocalDate();
        // Hot 索引库
        if (SolrPartition.hot.equals(solrPartition)) {
            return Lists.newArrayList(getHotCollections(today));
        }
        // 最近 6 个月
        if (SolrPartition.recent_6m.equals(solrPartition)) {
            return Lists.newArrayList(getRecent6monthsCollections(today));
        }

        // Cold 索引库：6个月之前的数据，走 cold索引 （注：6个月之前的数据现只有对账单导出功能，开始时间和结束时间都会在6个月之前，所以此处只查cold就可以了）
        if (SolrPartition.cold.equals(solrPartition)) {
            return Lists.newArrayList(getColdCollections(today));
        }
        // 兜底 all = hot & cold
        return Lists.newArrayList(getHotCollections(today), getColdCollections(today));
    }

    /**
     * 根据今日动态获取 hot 索引集合
     */
    private String getHotCollections(LocalDate today) {
        // 上溯3个月（不用指定月初0时）
        long startTime = today.plusMonths(-3).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        // 今晚最后一刻（非今天0时就可以）
        long endTime = today.plusDays(1).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli() - 1;
        return calculateSolrCollections(startTime, endTime);
    }

    /**
     * 根据今日动态获取 最近6个月 索引集合
     */
    private String getRecent6monthsCollections(LocalDate today) {
        // 上溯6个月（不用指定月初0时）
        long startTime = today.plusMonths(-6).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        // 今晚最后一刻（非今天0时就可以）
        long endTime = today.plusDays(1).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli() - 1;
        return calculateSolrCollections(startTime, endTime);
    }


    /**
     * 根据今日动态获取 cold 索引集合
     */
    private String getColdCollections(LocalDate today) {
        // 上溯4个月
        long endTime = today.plusMonths(-4).atStartOfDay()
                // 上溯4个月的月末（非月初0时就可以）
                .with(TemporalAdjusters.lastDayOfMonth())
                // 月末前一刻（PS：月末一刻是次月月初）
                .toInstant(ZoneOffset.of("+8")).toEpochMilli() - 1;
        // 起始时间为数据起始时间
        return calculateSolrCollections(hbaseDispatcherTimeConfig.getHotIndexStartTime(), endTime);
    }

    /**
     * 根据起始时间和结束时间计算 Collection （不区分冷热索引）
     *
     * @param startTime 起始时间（包含），小于 endTime
     * @param endTime   截止时间（不包含），大于 startTime
     * @return Solr Collection 列表，逗号隔开的字符串
     */
    @NotNull
    private String calculateSolrCollections(long startTime, long endTime) {
        Assert.isTrue(startTime < endTime, "startTime must be less than endTime");
        // 获取 Solr Collection 前缀
        String solrCollectionPrefix = getSolrCollectionPrefix();

        // 起始时间戳 -> 起始年月
        ZonedDateTime startZonedDateTime = Instant.ofEpochMilli(startTime).atZone(ZoneId.of("+8"));
        YearMonth startYearMonth = YearMonth.of(startZonedDateTime.getYear(), startZonedDateTime.getMonth());

        // 结束时间戳 -> 结束年月
        ZonedDateTime endZonedDateTime = Instant.ofEpochMilli(endTime - 1).atZone(ZoneId.of("+8"));
        YearMonth endYearMonth = YearMonth.of(endZonedDateTime.getYear(), endZonedDateTime.getMonth());

        // 从起始年月开始遍历到截止年月（包含临界年月）
        StringJoiner sj = new StringJoiner(",");
        while (startYearMonth.compareTo(endYearMonth) <= 0) {
            sj.add(solrCollectionPrefix + startYearMonth.toString().replace("-", ""));
            startYearMonth = startYearMonth.plusMonths(1);
        }
        return sj.toString();
    }

    protected abstract String getTableNamePrefix();

    protected abstract String getSolrCollectionPrefix();

    protected TableName getTableName(String date) {
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            return swipeTableName;
        }
        TableName tmpTableName = tableName;
        if (null == tmpTableName) {
            String tableNameKey = StringUtils.join(getTableNamePrefix(), date);
            tmpTableName = TABLE_NAMES.get(tableNameKey);
            if (null == tmpTableName) {
                synchronized (STRING_POOL.intern(tableNameKey)) {
                    tmpTableName = TABLE_NAMES.get(tableNameKey);
                    if (null == tmpTableName) {
                        tmpTableName = TableName.valueOf(tableNameKey);
                        TABLE_NAMES.put(tableNameKey, tmpTableName);
                    }
                }
            }
        }
        return tmpTableName;
    }

    @SneakyThrows
    public List<Map<String, Object>> rowFilterByIds(List<Pair<String, Long>> idCtimes, Set<String> filterColumns, int hBaseTimeout) {
        LinkedHashMap<ImmutablePair<TableName, Boolean>, List<Get>> getLists = new LinkedHashMap<>();
        Map<String, Integer> rowKeyPosInList = new HashMap<>(idCtimes.size());
        final AtomicInteger posInList = new AtomicInteger(0);
        idCtimes.forEach(idCtime -> {
            Pair<byte[], Long> formatId = SolrUtils.formatId(idCtime);
            rowKeyPosInList.put(WosaiJsonUtils.toJSONString(formatId.fst), posInList.getAndAdd(1));//记住rowKey位置
            Get get = new Get(formatId.fst);
            if (!CollectionUtils.isEmpty(filterColumns)) {
                SolrUtils.escapeQueryChars(filterColumns).forEach(column -> get.addColumn(FAMILY, Bytes.toBytes(column)));
            }
            TableName hbaseTableName = getTableName(new DateTime(formatId.snd).toString(CommonConstant.DAY_SDF_PATTERN_YYYYMM));
            getLists.computeIfAbsent(ImmutablePair.of(hbaseTableName,  hbaseTableName == swipeTableName ? true : hbaseDispatcherTimeConfig.isHostStorage(formatId.snd)), k -> new ArrayList<>()).add(get);
        });

        List<Result> resultList = new ArrayList<>(idCtimes.size());
        if (MapUtils.isNotEmpty(getLists)) {
            List<CompletableFuture<List<Result>>> futures = new ArrayList<>(getLists.size());
            getLists.forEach((tableNameInfo, getList) -> {
                CompletableFuture<List<Result>> future = CompletableFuture.supplyAsync(SupplierWrapper.of(() -> {
                        if (tableNameInfo.getRight()) {
                            return Arrays.asList(SolrHBaseUtils.batchGet(tableNameInfo.getLeft(), getList, hBaseTimeout));
                        } else {
                            return Arrays.asList(SolrHBaseUtils.coldBatchGet(tableNameInfo.getLeft(), getList));
                        }
                    }), ExecutorServiceSupport.getDefaultExecutorService());
                futures.add(future);
            });
            for (CompletableFuture<List<Result>> completableFuture : futures) {
                resultList.addAll(completableFuture.get());
            }
            //排序
            resultList = resultList.stream()
                    .filter(r -> r.getRow() != null)
                    .map(r -> new HbaseResult(r, rowKeyPosInList.get(WosaiJsonUtils.toJSONString(r.getRow()))))
                    .sorted(Comparator.comparing(HbaseResult::getPosition))
                    .collect(Collectors.toList());
        }

        Map<String, Object> initRow = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(filterColumns)) {
            filterColumns.forEach(column -> initRow.put(column, null));
        }

        return resultList.parallelStream()
                .filter(r -> Objects.nonNull(r.getRow()))
                .map(r -> buildEntryMap(r, initRow))
                .collect(Collectors.toList());
    }

    public abstract Map<String, Object> buildEntryMap(Result r, Map<String, Object> row);

    protected BoolQueryBuilder buildESInQuery(String tableColumn, List datas) {
        BoolQueryBuilder queryBuild = QueryBuilders.boolQuery();
        datas.forEach(data -> {
            queryBuild.should(QueryBuilders.matchQuery(tableColumn, data));
        });
        return queryBuild;
    }

    protected RangeQueryBuilder buildESRangeQuery(String tableColumn, Object start, Object end, boolean includeUpper) {
        RangeQueryBuilder range = QueryBuilders.rangeQuery(tableColumn);
        if (Objects.nonNull(start)) {
            range.gte(start);
        }
        if (Objects.nonNull(end)) {
            range = includeUpper ? range.lte(end) : range.lt(end);
        }
        return range;
    }

    protected BoolQueryBuilder buildFullWildcardQuery(String filed, List<String> values) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        values.forEach(value -> boolQueryBuilder.should(QueryBuilders.wildcardQuery(filed, "*" + value + "*")));
        return boolQueryBuilder;
    }

    protected BoolQueryBuilder buildExists(String field) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.existsQuery(field));
        return boolQueryBuilder;
    }

    public Map<String, Object> queryBySn(String merchantId, String sn, long start, long end) {
        Scan scan = new Scan();
        scan.setOneRowLimit();
        scan.withStartRow(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(start)), true);
        scan.withStopRow(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(end)), true);
        scan.setFilter(new RowFilter(CompareOperator.EQUAL, new RegexStringComparator(".*" + sn)));
        return SolrHBaseUtils.getHbaseData(TableName.valueOf(getTableNamePrefix() + DateTimeUtil.format(DateTimeUtil.MONTH_FORMAT, start)), scan, (result) -> buildEntryMap(result, Collections.emptyMap()));
    }
}
