package com.wosai.upay.transaction.util;

import avro.shaded.com.google.common.collect.Maps;
import avro.shaded.com.google.common.collect.Sets;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.wosai.app.dto.DepartmentInfo;
import com.wosai.app.dto.DepartmentStoreInfo;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.service.DepartmentService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.utils.WosaiJsonUtils;
import com.wosai.common.utils.transaction.TransactionEnhanceFields;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.mc.model.req.GetStoreListByMerchantUserIdReq;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.ChargeParamsConfig;
import com.wosai.profit.sharing.model.upay.ProfitSharing;
import com.wosai.profit.sharing.util.UpayProfitSharingUtil;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.bean.model.MetaBizModel;
import com.wosai.upay.core.model.CashDesk;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.*;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.StatementObjectConfigConstant;
import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.export.base.RunContext;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.*;
import com.wosai.upay.transaction.service.TransactionService;
import com.wosai.upay.transaction.service.service.common.ReflectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.common.utils.transaction.Transaction.MCH_DISCOUNT_ORIGIN_TYPE;
import static com.wosai.upay.transaction.util.LanguageUtil.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class StatementTransactionUtil {

    @Autowired
    private StoreService storeService;

    @Autowired
    @Qualifier("merchantUserDepartmentService")
    public DepartmentService departmentService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private BusinssCommonService businessCommonService;
    @Autowired
    private OssFileUploader ossFileUploader;

    @Autowired
    TransactionService transactionService;

    @Autowired
    CashDeskService cashDeskService;


    @Autowired
    private TradeComboDetailService tradeComboDetailService;

    private static String GET_ALL_TRADE_APP = "get_all_trade_app";

    private static String GET_ALL_META_BIZ_MODEL = "get_all_meta_biz_model";

    private static String DEFAULT_APP_NAME = "支付业务";


    @Autowired
    private MetaService metaService;


    public final LoadingCache<String, Map<Long, String>> TRADE_APP_ID_AND_NAME_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(60, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build(new CacheLoader<String, Map<Long, String>>() {

                @Override
                public Map<Long, String> load(String key) {
                    return loadTradeAppIdAndName();
                }
            });


    private Map<Long, String> loadTradeAppIdAndName() {
        Map<Long, String> allTradeAppIdAndNameMap = tradeComboDetailService.getAllTradeAppIdAndNameMap();

        return allTradeAppIdAndNameMap;

    }

    private Map<Long, String> getALLTradeAPPIdAndName()  {
        Map<Long, String> allTradeAppIdAndNames = new HashMap<>();
        try {
           allTradeAppIdAndNames = TRADE_APP_ID_AND_NAME_CACHE.get(GET_ALL_TRADE_APP);
        } catch (Exception e) {
            log.error("fail to get allTradeAppIdAndNames", e);
        }
        return allTradeAppIdAndNames;
    }




    public final LoadingCache<String, Map<String, String>> AllMetaBizModel = CacheBuilder.newBuilder()
            .expireAfterAccess(60, TimeUnit.MINUTES)
            .maximumSize(50000).concurrencyLevel(256).build(new CacheLoader<String, Map<String, String>>() {

                @Override
                public Map<String, String> load(String key) {
                    return loadAllMetaBizModel();
                }
            });


    private Map<String, String> loadAllMetaBizModel() {
        List<MetaBizModel> allMetaBizModel = metaService.getAllMetaBizModel();
        Map<String,String> metaBizModels = new HashMap<>();
        for(MetaBizModel model : allMetaBizModel){
            metaBizModels.put(model.getId(), model.getName());
        }
        return metaBizModels;

    }


    private Map<String, String> getAllMetaBizModel()  {
        Map<String, String> allMetaBizModel = new HashMap<>();
        try {
            allMetaBizModel = AllMetaBizModel.get(GET_ALL_META_BIZ_MODEL);
        } catch (Exception e) {
            log.error("fail to get getAllMetaBizModel", e);
        }
        return allMetaBizModel;
    }





    @Resource
    com.wosai.mc.service.StoreService merchantCenterStoreService;
    
    public void appendStatementTransactionDetail(List<Map<String, Object>> transactions, SXSSFSheet details) {
        //是否是KA对账单
        RunContext runContext = RunContext.currentContext();
        boolean isKAStatement = runContext.getIsKAStatement();
        //有跨主体退款流水,需要查询跨主体退款门店信息
        Map<String, Map> stores = Maps.newHashMap();
        boolean crossMchRefundEnable = RunContext.currentContext().crossMchRefundEnable();
        if (crossMchRefundEnable) {
            Set<String> storeIds = Sets.newHashSet();
            for (Map transaction : transactions) {
                if (TransactionUtil.isCrossMerchantRefund(transaction)) {
                    Map map = (Map) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS_CROSS_MCH_REFUND);
                    storeIds.add(BeanUtil.getPropString(map, "store_id"));
                }
            }
            for (String storeId : storeIds) {
                Map store = storeService.getStoreByStoreId(storeId);
                stores.put(BeanUtil.getPropString(store, ConstantUtil.KEY_ID), store);
            }
        }

        //是否展示收银台信息
        boolean containsCashDesk = RunContext.currentContext().getExportIncludes().contains(StatementTaskLog.INCLUDE_CASH_DESK + "");

        for (Map transaction : transactions) {
            int transactionType = MapUtil.getIntValue(transaction, Transaction.TYPE);

            long originalAmount = MapUtil.getLongValue(transaction, Transaction.ORIGINAL_AMOUNT);
            String currency = MapUtil.getString(transaction, TransactionParam.CURRENCY, "CNY");

            long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            long discountAmount = MapUtil.getLongValue(transaction, Transaction.WOSAI_FAVORABLE_AMOUNT);//收钱吧优惠
            Long paidAmount = MapUtil.getLong(transaction, Transaction.PAID_AMOUNT);
            long channelDiscount = MapUtil.getLongValue(transaction, Transaction.CHANNEL_AGENT_FAVORABLE_AMOUNT);
            long channelMchDiscount = MapUtil.getLongValue(transaction, Transaction.CHANNEL_MCH_FAVORABLE_AMOUNT);
            long channelMchTopUpDiscount = MapUtil.getLongValue(transaction, Transaction.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT);
            if (paidAmount == null) {
                paidAmount = effectiveAmount - (channelDiscount + channelMchDiscount + channelMchTopUpDiscount);
            }
            long actualReceiveAmount = MapUtil.getLongValue(transaction, Transaction.ACTUAL_RECEIVE_AMOUNT);
            long clearingAmount = MapUtil.getLongValue(transaction, Transaction.CLEARING_AMOUNT);
            long merchantDiscount = MapUtil.getLongValue(transaction, Transaction.MCH_FAVORABLE_AMOUNT);
            long tradeFee = MapUtil.getLongValue(transaction, Transaction.FEE);
            long sharingAmount = MapUtil.getLongValue(transaction, Transaction.SHARING_AMOUNT, 0);
            int sharingFlag = MapUtil.getIntValue(transaction, TransactionEnhanceFields.SHARING_FLAG.getField());
            String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG, "");
            String subPayway = MapUtil.getString(transaction, Transaction.SUB_PAYWAY);
            long tradeServiceSharingAmount = MapUtil.getLongValue(transaction, Transaction.TRADE_SERVICE_SHARING_AMOUNT, 0);
            Map sharingProfitDetail = buildSharingProfitDetail(transaction);

            if (TransactionTypeRelatedUtil.isNegativeType(transactionType)) {
                originalAmount = originalAmount * -1;
                paidAmount = paidAmount * -1;
                discountAmount *= -1;
                actualReceiveAmount *= -1;
                clearingAmount *= -1;
                merchantDiscount *= -1;
                channelDiscount *= -1;
                channelMchDiscount *= -1;
                channelMchTopUpDiscount *= -1;
                tradeFee *= -1;
                sharingAmount *= -1;
                tradeServiceSharingAmount *= -1;
            }
            double feeRate = 0.0;
            String feeRateString = MapUtil.getString(transaction, TransactionParam.FEE_RATE, "0.0");
            if (feeRateString.trim() != "") {
                feeRate = CommonUtil.formatMoney(feeRateString, 3);
            }
            /*"交易日期", "时间", "商户流水号", "支付方式", "商品名", "商户内部订单号", "商户订单号", "收款通道订单号", "交易类型",
                "交易状态", "付款账户", "货币类型", "交易金额", "商户优惠", "收钱吧优惠", "收款通道机构优惠",
                "收款通道商户预充值优惠", "收款通道商户免充值优惠", "消费者实付金额", "扣率%", "手续费", "实收金额", "技术服务费",
                "结算金额", "门店号", "商户门店号", "门店名称", "终端号", "商户终端号", "终端名称", "终端类型", "设备号", "操作员",
                "收银员","备注", "分账标识", "分账金额", "收钱吧商户优惠类", "应用方", "交易模式, "收银台"(可变)"*/
            List rowValue = new ArrayList();
            rowValue.add(CommonConstant.DAY_SDF.get().format(transaction.get(DaoConstants.CTIME)));
            rowValue.add(CommonConstant.TIME_SDF.get().format(transaction.get(DaoConstants.CTIME)));
            String clientTsn = null;
            String refundRequestNo = null;
            if (TransactionTypeRelatedUtil.isRefundType(transactionType)) {
                String client_tsn = MapUtil.getString(transaction, Transaction.CLIENT_TSN);
                if (StringUtils.hasText(client_tsn) && client_tsn.split(CommonConstant.PLACE_HOLDER).length == 2) {
                    int index = client_tsn.lastIndexOf(CommonConstant.PLACE_HOLDER);
                    clientTsn = client_tsn.substring(0, index);
                    refundRequestNo = client_tsn.substring(index + 1);
                }
            }

            if (TransactionTypeRelatedUtil.isRefundType(transactionType) && refundRequestNo != null) {
                rowValue.add(refundRequestNo);
            } else {
                rowValue.add(MapUtil.getString(transaction, Transaction.CLIENT_TSN));
            }
            Object depositReflect = transaction.get(Transaction.REFLECT);
            if ((depositReflect instanceof String) && org.apache.commons.lang3.StringUtils.contains((String) depositReflect, Transaction.REFLECT_DEPOSIT_TYPE)) {
                ReflectService reflectService = SpringUtil.getBean(ReflectService.class);
                depositReflect = reflectService.getReflectRemark(depositReflect);
                transaction.put(Transaction.REFLECT, depositReflect);
            }

            String chargeSource = (String) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS + "." + Transaction.SQB_CHARGE_SOURCE);
            String paywayStr = OrderUtil.getPaywayDesc(transaction.get(Transaction.PAYWAY).toString(), currency, getLanguage());
            if (!StringUtils.isEmpty(chargeSource)) {
                paywayStr = paywayStr + "-" + chargeSource;
            }
            //此处的payway , 若为其他方式记账，且存在自定义记账方式是 payway = paywayStr-'自定义记账方式名称'
            rowValue.add(paywayStr);
            rowValue.add(MapUtil.getString(transaction, Transaction.SUBJECT));
            if (TransactionTypeRelatedUtil.isRefundType(transactionType) && clientTsn != null) {
                rowValue.add(clientTsn);
            } else {
                rowValue.add(MapUtil.getString(transaction, Transaction.CLIENT_TSN));
            }
            rowValue.add(MapUtil.getString(transaction, Transaction.ORDER_SN));
            rowValue.add(MapUtil.getString(transaction, Transaction.TRADE_NO));
            rowValue.add(getValue(TransactionTypeRelatedUtil.getTransactionTypeLanguageKeyIgnoreDeposit(transactionType)));
            rowValue.add(getValue(OrderUtil.getTransactionStatusLanguageKey(BeanUtil.getPropString(transaction, Transaction.STATUS))));
            int payWay = MapUtils.getIntValue(transaction, Transaction.PAYWAY);
            String buyerUid = MapUtil.getString(transaction, Transaction.BUYER_UID);
            String buyerLogin = MapUtil.getString(transaction, Transaction.BUYER_LOGIN);
            if (payWay == 3) {
                rowValue.add(StringUtils.hasText(buyerUid) ? buyerUid : buyerLogin);
            } else {
                rowValue.add(buyerLogin);
            }
            rowValue.add(currency);
            rowValue.add(originalAmount / 100.0);
            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY) {
                rowValue.add(merchantDiscount / 100.0);
                rowValue.add(discountAmount / 100.0);//收钱吧优惠
                rowValue.add(channelDiscount / 100.0);//收款通道机构优惠
                rowValue.add(channelMchTopUpDiscount / 100.0);
                rowValue.add(channelMchDiscount / 100.0);
                //消费者实付金额
                rowValue.add(paidAmount / 100.0);
                rowValue.add(feeRate);
            }

            //银行卡excel “手续费”放在“实收金额”后面
            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
                //实收金额
                rowValue.add(actualReceiveAmount / 100.0);
                rowValue.add(tradeFee / 100.0);
            } else {
                rowValue.add(tradeFee / 100.0);
                //实收金额
                rowValue.add(actualReceiveAmount / 100.0);
            }
            if (!isKAStatement) {
                //技术服务费
                rowValue.add(tradeServiceSharingAmount / 100.0);
            }
            //结算金额
            rowValue.add(clearingAmount / 100.0);
            rowValue.add(MapUtil.getString(transaction, ConstantUtil.KEY_STORE_SN));
            rowValue.add(MapUtil.getString(transaction, "store_client_sn"));
            rowValue.add(MapUtil.getString(transaction, ConstantUtil.KEY_STORE_NAME));
            rowValue.add(MapUtil.getString(transaction, ConstantUtil.KEY_TERMINAL_SN));
            rowValue.add(MapUtil.getString(transaction, "terminal_client_sn"));
            rowValue.add(MapUtil.getString(transaction, ConstantUtil.KEY_TERMINAL_NAME));
            rowValue.add(getValue(OrderUtil.getTerminalTypeLanguageKey(BeanUtil.getPropString(transaction, "terminal_type"))));
            rowValue.add(MapUtil.getString(transaction, "terminal_device_fingerprint"));

            if (DBSelectContext.getContext().getSelectDb() != UpayQueryType.UPAY_SWIPE) {
                rowValue.add(MapUtil.getString(transaction, Transaction.OPERATOR));
                String operatorName = MapUtil.getString(transaction, "operator_name");
                // 交易中存在收银员(班次收银员)时，替换原先的操作人
                if (transaction.containsKey("cashier_name")) {
                    operatorName =  MapUtil.getString(transaction, "cashier_name");
                }
                rowValue.add(operatorName);
                Object reflect = transaction.get(Transaction.REFLECT);
                if (reflect instanceof Map) {
                    rowValue.add(CollectionUtils.isEmpty((Map) reflect) ? "" : WosaiJsonUtils.toJSONString(reflect));
                } else {
                    rowValue.add(Objects.isNull(reflect) ? "" : "" + reflect);
                }

            }

            if (crossMchRefundEnable) { //开启跨门店退款,返回以下字段
                // 操作门店号	操作商户门店号,操作门店名称,支付渠道,是否跨主体退货
                String opStoreSn = MapUtil.getString(transaction, ConstantUtil.KEY_STORE_SN);
                String opMchStoreSn = MapUtil.getString(transaction, "store_client_sn");
                String opStoreName = MapUtil.getString(transaction, ConstantUtil.KEY_STORE_NAME);
                boolean isCrossMerchantRefund = TransactionUtil.isCrossMerchantRefund(transaction);
                String crossRefund = isCrossMerchantRefund ? "是" : "否";
                //跨主体退货,展示操作的人
                if (isCrossMerchantRefund) {
                    Map map = (Map) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS_CROSS_MCH_REFUND);
                    Map store = stores.get(BeanUtil.getPropString(map, "store_id"));
                    if (store != null) {
                        opStoreSn = MapUtil.getString(store, ConstantUtil.KEY_SN);
                        opStoreName = MapUtil.getString(store, ConstantUtil.KEY_NAME);
                        opMchStoreSn = MapUtil.getString(store, ConstantUtil.KEY_CLIENT_SN);
                    }
                }
                rowValue.add(opStoreSn);
                rowValue.add(opMchStoreSn);
                rowValue.add(opStoreName);
                rowValue.add(crossRefund);
            }

            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
                rowValue.add(MapUtil.getString(transaction, LanguageUtil.PAY_TYPE, ""));
                rowValue.add(MapUtil.getString(transaction, LanguageUtil.BATCH_BILL_NO, ""));
                rowValue.add(MapUtil.getString(transaction, LanguageUtil.SYS_TRACE_NO, ""));
                rowValue.add(MapUtil.getString(transaction, LanguageUtil.BANK_TYPE, ""));
                rowValue.add(MapUtil.getString(transaction, LanguageUtil.LAKALA_MERC_ID, ""));
                rowValue.add(MapUtil.getString(transaction, LanguageUtil.LAKALA_TERM_ID, ""));
                rowValue.add(MapUtil.getString(transaction, LanguageUtil.TRADE_NO, ""));
                String channelFinishTime = MapUtil.getString(transaction, Transaction.CHANNEL_FINISH_TIME);
                if (StringUtils.hasText(channelFinishTime)) {
                    rowValue.add(2, CommonConstant.DETAIL_SDF.get().format(Long.parseLong(channelFinishTime)));
                } else {
                    rowValue.add(2, "");
                }

            }

            if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
                Object reflect = transaction.get(Transaction.REFLECT);
                if (reflect instanceof Map) {
                    rowValue.add(CollectionUtils.isEmpty((Map) reflect) ? "" : WosaiJsonUtils.toJSONString(reflect));
                } else {
                    rowValue.add(Objects.isNull(reflect) ? "" : "" + reflect);
                }
            }

            rowValue.add(sharingFlag == 1 ? "是" : "否");
            if (!isKAStatement) {
                // 分账金额
                rowValue.add(sharingAmount / 100.0);
            } else {
                rowValue.add((sharingAmount + tradeServiceSharingAmount) / 100.0);
            }
            rowValue.add(MapUtil.getString(transaction, MCH_DISCOUNT_ORIGIN_TYPE, ""));

            Long tradeApp = MapUtil.getLong(transaction, Transaction.TRADE_APP);
            String appName = MapUtil.getString(getALLTradeAPPIdAndName(), tradeApp);
            Map<String, List<String>> businessAttribute = ApolloUtil.getBusinessAttribute();
            String name = null;
            for (Map.Entry<String, List<String>> entry : businessAttribute.entrySet()) {
                List<String> value = entry.getValue();
                if(value.contains(tradeApp + "")){
                    name =  entry.getKey();
                    break;
                }
            }
            if(Objects.nonNull(name)) {
                appName = name + "-" + appName;
            }

            //应用方兜底逻辑
            if(Objects.isNull(appName)) {
                appName =  DEFAULT_APP_NAME;
            }

            rowValue.add(appName);
            rowValue.add(SubPaywayUtils.getSubPaywayDesc(subPayway));
            if (containsCashDesk) {
                rowValue.add(BeanUtil.getPropString(transaction, Transaction.CASH_DESK_NAME, ""));
            }
            String tsn = MapUtil.getString(transaction, Transaction.TSN);
            rowValue.add(tsn);
            String sqbBizModel = MapUtil.getString(transaction, Transaction.SQB_BIZ_MODEL);
            String bizModel = getAllMetaBizModel().get(sqbBizModel);
            if(!com.wosai.pantheon.util.StringUtil.isEmpty(bizModel)){
                rowValue.add(bizModel);
            } else {
                rowValue.add("");
            }
            String payChannel = TransactionUtil.getPayChannel(transaction);
            if (Order.PAYWAY_BANKCARD == MapUtil.getIntValue(transaction, Transaction.PAYWAY)) {
                payChannel = (productFlag.contains("ar") ? "外卡刷卡业务" : "刷卡业务") + payChannel;
            }
            rowValue.add(payChannel);
            if (!isKAStatement) {
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_ORDER) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_ORDER) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_FLOW_SERVICE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_FLOW_SERVICE) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_CAMPUS_DISTRIBUTION) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SMART_CAMPUS_DISTRIBUTION) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_THIRD_DISTRIBUTION) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_THIRD_DISTRIBUTION) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_COUPON_SALE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_COUPON_SALE) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_EQUITY_CARD_SALE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_EQUITY_CARD_SALE) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_CARD_RECHARGE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.SAAS_CARD_RECHARGE) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.HBFQ_INDIRECT_TIEXI) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.HBFQ_INDIRECT_TIEXI) / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.PAY_AFTER_USE) * -1 / 100.0 : MapUtil.getLongValue(sharingProfitDetail, LanguageUtil.PAY_AFTER_USE) / 100.0);

                ImmutablePair<Long, Long> receiverBusinessInfo = getReceiverBusinessInfo(transaction);
                //固定佣金、 起步价
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? receiverBusinessInfo.getLeft() * -1 / 100.0 : receiverBusinessInfo.getLeft() / 100.0);
                rowValue.add(TransactionTypeRelatedUtil.isNegativeType(transactionType) ? receiverBusinessInfo.getRight() * -1 / 100.0 : receiverBusinessInfo.getRight() / 100.0);
            }
            SheetHelper.appendLine(details, rowValue);
        }
    }

    public void appendStatementHbfqTransactionDetail(List<Map<String, Object>> transactions, SXSSFSheet details) {
        //有跨主体退款流水,需要查询跨主体退款门店信息
        Map<String, Map> stores = Maps.newHashMap();
        boolean crossMchRefundEnable = RunContext.currentContext().crossMchRefundEnable();
        if (crossMchRefundEnable) {
            Set<String> storeIds = Sets.newHashSet();
            for (Map transaction : transactions) {
                if (TransactionUtil.isCrossMerchantRefund(transaction)) {
                    Map map = (Map) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS_CROSS_MCH_REFUND);
                    storeIds.add(BeanUtil.getPropString(map, "store_id"));
                }
            }
            for (String storeId : storeIds) {
                Map store = storeService.getStoreByStoreId(storeId);
                stores.put(BeanUtil.getPropString(store, ConstantUtil.KEY_ID), store);
            }
        }

        for (Map transaction : transactions) {
            int transactionType = BeanUtil.getPropInt(transaction, Transaction.TYPE);

            long originalAmount = BeanUtil.getPropLong(transaction, Transaction.ORIGINAL_AMOUNT);
            String productFlag = BeanUtil.getPropString(transaction, Transaction.PRODUCT_FLAG, "");

            long effectiveAmount = BeanUtil.getPropLong(transaction, Transaction.EFFECTIVE_AMOUNT);
            long discountAmount = BeanUtil.getPropLong(transaction, Transaction.WOSAI_FAVORABLE_AMOUNT);//收钱吧优惠
            Long paidAmount = (Long) transaction.get(Transaction.PAID_AMOUNT);
            long channelDiscount = BeanUtil.getPropLong(transaction, Transaction.CHANNEL_AGENT_FAVORABLE_AMOUNT);
            long channelMchDiscount = BeanUtil.getPropLong(transaction, Transaction.CHANNEL_MCH_FAVORABLE_AMOUNT);
            long channelMchTopUpDiscount = BeanUtil.getPropLong(transaction, Transaction.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT);
            if (paidAmount == null) {
                paidAmount = effectiveAmount - (channelDiscount + channelMchDiscount + channelMchTopUpDiscount);
            }
            long actualReceiveAmount = BeanUtil.getPropLong(transaction, Transaction.ACTUAL_RECEIVE_AMOUNT);
            long clearingAmount = BeanUtil.getPropLong(transaction, Transaction.CLEARING_AMOUNT);
            long merchantDiscount = BeanUtil.getPropLong(transaction, Transaction.MCH_FAVORABLE_AMOUNT);
            long tradeFee = BeanUtil.getPropLong(transaction, Transaction.FEE);
            long sharingAmount = BeanUtil.getPropLong(transaction, Transaction.SHARING_AMOUNT, 0) + BeanUtil.getPropLong(transaction, Transaction.TRADE_SERVICE_SHARING_AMOUNT, 0);
            long hbfqDiscount = BeanUtil.getPropLong(transaction, Transaction.FQ_MCH_DISCOUNT_AMOUNT);
            int hbfqType = BeanUtil.getPropInt(transaction, Transaction.FQ_TYPE);
            boolean useHbfq = productFlag.contains(HbfqTransactionUtils.HBFQ_PRODUCT_FLAG);
            long fqNum = BeanUtil.getPropLong(transaction, TransactionParam.FQ_NUM, 0);
            long fqAmount = BeanUtil.getPropLong(transaction, HbfqTransactionUtils.FQ_AMOUNT, 0);
            //以前的数据没有product_flag的概念,只有hb_fq_num字段，这里useHbfq为true一定是花呗分期，
            //useHbfq如果为false不一定就是不是花呗分期，如果fq_num是空，hb_fq_num不是空 (信用卡分期一定会上送fq_num,而老版本的没有fq_num字段)
            if(useHbfq || fqNum == 0){
                 if(fqNum == 0){
                     fqNum = BeanUtil.getPropLong(transaction, TransactionParam.HB_FQ_NUM, 0);
                     useHbfq = true;
                 }
            }
            if (TransactionTypeRelatedUtil.isNegativeType(transactionType)) {
                originalAmount = originalAmount * -1;
                paidAmount = paidAmount * -1;
                discountAmount *= -1;
                actualReceiveAmount *= -1;
                clearingAmount *= -1;
                merchantDiscount *= -1;
                channelDiscount *= -1;
                channelMchDiscount *= -1;
                channelMchTopUpDiscount *= -1;
                tradeFee *= -1;
                sharingAmount *= -1;
                hbfqDiscount *= -1;
                fqAmount *= -1;
            }
            /*"交易日期", "时间", "门店名称", "商户流水号", "商户内部订单号", "商户订单号", "收款通道订单号", "交易类型",
                "交易状态", "收款金额", "收钱吧商户优惠", "收钱吧补贴优惠", "收款通道补贴优惠", "收款通道商户预充值优惠", 
                "收款通道商户免充值优惠", "实收金额", "手续费", "分账金额", "结算金额", "分期期数", "花呗分期上街贴息金额",
                "花呗分期付息类型"*/
            List rowValue = new ArrayList();
            rowValue.add(CommonConstant.DAY_SDF.get().format(transaction.get(DaoConstants.CTIME)));
            rowValue.add(CommonConstant.TIME_SDF.get().format(transaction.get(DaoConstants.CTIME)));
            rowValue.add(BeanUtil.getPropString(transaction, ConstantUtil.KEY_STORE_NAME));


            String clientTsn = null;
            String refundRequestNo = null;
            if (TransactionTypeRelatedUtil.isRefundType(transactionType)) {
                String client_tsn = BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN);
                if (StringUtils.hasText(client_tsn) && client_tsn.split(CommonConstant.PLACE_HOLDER).length == 2) {
                    int index = client_tsn.lastIndexOf(CommonConstant.PLACE_HOLDER);
                    clientTsn = client_tsn.substring(0, index);
                    refundRequestNo = client_tsn.substring(index + 1);
                }
            }

            if (TransactionTypeRelatedUtil.isRefundType(transactionType) && refundRequestNo != null) {
                rowValue.add(refundRequestNo);
            } else {
                rowValue.add(BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN));
            }

            if (TransactionTypeRelatedUtil.isRefundType(transactionType) && clientTsn != null) {
                rowValue.add(clientTsn);
            } else {
                rowValue.add(BeanUtil.getPropString(transaction, Transaction.CLIENT_TSN));
            }
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.ORDER_SN));
            rowValue.add(BeanUtil.getPropString(transaction, Transaction.TRADE_NO));
            rowValue.add(getValue(TransactionTypeRelatedUtil.getTransactionTypeLanguageKeyIgnoreDeposit(transactionType)));
            rowValue.add(getValue(OrderUtil.getTransactionStatusLanguageKey(BeanUtil.getPropString(transaction, Transaction.STATUS))));
            rowValue.add(originalAmount / 100.0);
            rowValue.add(merchantDiscount / 100.0);
            rowValue.add(discountAmount / 100.0);//收钱吧优惠
            rowValue.add(channelDiscount / 100.0);//收款通道机构优惠
            rowValue.add(channelMchTopUpDiscount / 100.0);
            rowValue.add(channelMchDiscount / 100.0);
            rowValue.add(actualReceiveAmount / 100.0);
            rowValue.add(tradeFee / 100.0);
            rowValue.add(sharingAmount / 100.0);
            rowValue.add(clearingAmount / 100.0);
            rowValue.add(useHbfq ? getValue(HB_FQ_TYPE) : getValue(CREDIT_FQ_TYPE));
            rowValue.add(fqNum);
            rowValue.add(fqAmount == 0 ? null : fqAmount / 100.0);
            rowValue.add(hbfqDiscount/100.0);
            rowValue.add(hbfqType == Transaction.FQ_TYPE_BUYER ? getValue(FQ_TYPE_BUYER) : getValue(FQ_TYPE_SELLER));

            SheetHelper.appendLine(details, rowValue);
        }
    }

    /**
     * 上传file
     */
    public String uploadStatementToOSS(File file, String ossFilepath) throws IOException {
        byte[] content = Files.readAllBytes(file.toPath());
        ByteArrayInputStream bais = new ByteArrayInputStream(content);
        ossFileUploader.uploadPrivateStaticsFile(ossFilepath, bais, content.length);
        return ossFilepath;
    }


    /**
     * 生成对账单汇总 excel 文件内容
     *
     * @param context
     * @param summarySheet
     * @param summary
     * @return
     */
    public void buildUpayStatementSummary(List<String> merchantIds, String merchantUserId, List<String> departmentIds, List<String> storeIds,
                                          Map context, SXSSFSheet summarySheet, Map summary, Map sheetParams) {
        //保存业务对象名称等基本信息
        Map storeBasicInfoMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.STORE_BASIC_INFO_MAP);
        Map cashDeskBasicInfoMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.CASH_DESK_BASIC_INFO_MAP);
        Map terminalBasicInfoMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.TERMINAL_BASIC_INFO_MAP);
        Map operatorBasicInfoMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.OPERATOR_BASIC_INFO_MAP);
        Map cashDeskCashierBasicInfoMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.CASH_DESK_CASHIER_BASIC_INFO_MAP);
        Map cashierBasicInfoMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.CASHIER_BASIC_INFO_MAP);

        //保存业务对象上下级关联关系
        Map<String, List> storeSnCashDesksMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.STORE_SN_CASHDESKS_MAP);
        Map<String, List> storeSnTerminalsMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.STORE_SN_TERMINALS_MAP);
        Map<String, List> terminalSnOperatorsMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.TERMINAL_SN_OPERATORS_MAP);
        Map<String, Map<String, List>> cashDeskStoreSnCashiersMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.CASH_DESK_STORE_SN_CASHIERS_MAP);
        Map<String, List> storeSnCashiersMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.STORE_SN_CASHIERS_MAP);

        //保存业务对象汇总数据
        Map<String, Object> merchantSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.MERCHANT_SUMMARY_MAP);
        Map paywaySummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.PAYWAY_SUMMARY_MAP);
        Map<String, Object> storeSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.STORE_SUMMARY_MAP);
        Map cashDeskSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.CASH_DESK_SUMMARY_MAP);
        Map terminalSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.TERMINAL_SUMMARY_MAP);
        Map operatorSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.OPERATOR_SUMMARY_MAP);
        Map unionSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.UNION_SUMMARY_MAP);
        Map cashDeskCashierSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.CASH_DESK_CASHIER_SUMMARY_MAP);
        Map cashierSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.CASHIER_SUMMARY_MAP);

        Map settlementSummaryMap = new LinkedHashMap();
        //apply_user_id 申请用户id
        String applyUserId = BeanUtil.getPropString(context, StatementTaskLog.APPLY_USER_ID);
        //请求系统
        int applySystem = BeanUtil.getPropInt(context, StatementTaskLog.APPLY_SYSTEM);
        //对账单类型
        int type = BeanUtil.getPropInt(context, StatementTaskLog.TYPE);
        //是否是KA对账单
        boolean isKAStatement = RunContext.currentContext().getIsKAStatement();
        //标题
        SheetHelper.addStatementSummaryTitle(context, summarySheet);
        int sheetType = BeanUtil.getPropInt(sheetParams, StatementObjectConfig.SHEET_TYPE);
        List<String> croList = (List) BeanUtil.getProperty(sheetParams, StatementObjectConfig.CRO);
        //如果是ka账单，那么不需要导出技术服务费和分账金额列； 反之不需要导出分账金额（含技术服务费） 列
        if (isKAStatement) {
            croList.removeAll(Lists.newArrayList(StatementObjectConfig.TRADE_SERVICE_SHARING_AMOUNT, StatementObjectConfig.MERCHANT_SHARING_AMOUNT, StatementObjectConfig.TRANSACTION_NET_AMOUNT, StatementObjectConfig.DEAL_TO_ACCOUNT_AMOUNT));
        } else {
            croList.remove(StatementObjectConfig.SHARING_AMOUNT);
        }
        List<Integer> paywayList = (List) BeanUtil.getProperty(sheetParams, StatementObjectConfig.PAYWAY);
        int terminalType = BeanUtil.getPropInt(sheetParams, StatementObjectConfig.TERMINAL_TYPE);

        //忽略关闭、禁用状态且数据为0的商户
        List<String> removeSns = StatementCommonUtil.removeUndisplayMerchant(merchantSummaryMap, businessCommonService);
        removeSns.forEach(merchantSummaryMap::remove);
        Set<String> merchantKeys = merchantSummaryMap.keySet();
        List<Pair<String, String>> merchantIdAndSn = Lists.newArrayList();
        merchantIds.forEach(id -> {
            Map<String, Object> merchantMinimalInfo = businessCommonService.getMerchantMinimalInfoById(id);
            String merchantSn = BeanUtil.getPropString(merchantMinimalInfo, Merchant.SN);
            //1. merchantIds对应的商户在查询出的结果集中，且不在需要移除的集合
            //2. merchantIds对应的商户不在查询出的结果集中，且商户状态是开启
            if ((merchantKeys.contains(merchantSn) && !removeSns.contains(merchantSn)) ||
                    (!merchantKeys.contains(merchantSn) && Merchant.STATUS_ENABLED == BeanUtil.getPropInt(merchantMinimalInfo, Merchant.STATUS))){
                merchantIdAndSn.add(Pair.of(id, merchantSn));
            }
        });

        List<Map> configMerchantStores = null;
        merchantIdAndSn.sort((o1, o2) -> org.apache.commons.lang3.StringUtils.compare(o1.getRight(), o2.getRight()));
        if (departmentIds != null && departmentIds.size() > 0) {
            //有部门时，部门门店对账
            merchantIdAndSn.forEach(idAndSn -> appendDepartmentSummarys(summarySheet, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, settlementSummaryMap, idAndSn.getLeft(), departmentIds, croList));
        } else {
            //进行门店汇总 (根据用户或者商户筛选出门店)
            if (!StringUtil.empty(merchantUserId)) {
                UcMerchantUserSimpleInfo simpleInfo = merchantUserServiceV2.getSimpleInfoById(merchantUserId);
                List<String> partStoreIds = merchantUserServiceV2.getStoreIdsByMerchantUserId(merchantUserId);
                SheetHelper.appendLine(summarySheet, Arrays.asList(getValue(ADMIN) + "[" + simpleInfo.getName() + "]" + getValue(TRANSACTION_SUMMARY)));
                appendStoresSummary(summarySheet, partStoreIds, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, settlementSummaryMap, croList);
            } else {
                SheetHelper.appendLine(summarySheet, getValues(STORE_TRANSACTION_SUMMARY));
                //msp 下载的交易对账单（不包含集团下的对账单）才需要按照用户的门店排序展示
                if (!StringUtils.isEmpty(applyUserId) && StatementTaskLog.APPLY_SYSTEM_MSP == applySystem && StatementTaskLog.TYPE_TRANSACTION == type) {
                    configMerchantStores = getStoreListByMerchantUserId(applyUserId);
                    appendStoresSummary(summarySheet, storeIds, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, settlementSummaryMap, croList, configMerchantStores);
                } else {
                    if (CollectionUtils.isEmpty(storeIds)) {
                        storeIds = getStoreIdsByMerchantId(merchantIds.get(0));
                    }
                    appendStoresSummary(summarySheet, storeIds, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, settlementSummaryMap, croList);
                }
            }
        }

        //结算资金汇总
        appendSettlementSummary(summarySheet, settlementSummaryMap, context);

        //简洁版
        if (sheetType == StatementObjectConfigConstant.CONCISE_TYPE) {
            return;
        }
        //自定义版
        if ((paywayList != null && paywayList.size() > 0)) {
            appendPaywaySummary(summarySheet, paywaySummaryMap, croList, paywayList);
        }
        //门店终端汇总
        if (terminalType != StatementObjectConfigConstant.NO_STORE_TERMINAL_TYPE) {
            // 门店终端交易汇总清单
            SheetHelper.appendLine(summarySheet, getValues(STORE_TERMINAL_TRANSACTION_SUMMARY_LIST));
            
            //终端汇总表头
            List<String> columnDescByTaskStyle = StatementSummaryStyleUtil.getTerminalColumnDescByTaskStyle();
            StatementSummaryStyleUtil.resetColumns(columnDescByTaskStyle, isKAStatement);
            SheetHelper.appendLine(summarySheet, columnDescByTaskStyle);

            List<String> terminalStoreSns = new ArrayList<>(storeSnTerminalsMap.keySet());
            terminalStoreSns = sortTerminalStoreSns(terminalStoreSns, configMerchantStores);
            for (String storeSn : terminalStoreSns) {
                StatementTransactionSummaryUtil.appendStoreSummaryByStyle(summarySheet, storeBasicInfoMap, storeSummaryMap, storeSn, isKAStatement);
                ArrayList<String> terminalSnList = (ArrayList<String>) BeanUtil.getProperty(storeSnTerminalsMap, storeSn);
                
                terminalSnList.sort(new StatementTransactionSummaryUtil.ByLength());
                for (String terminalSn : terminalSnList) {
                    StatementTransactionSummaryUtil.appendTerminalSummaryByStyle(summarySheet, terminalBasicInfoMap, terminalSummaryMap, terminalSn, isKAStatement);
                    
                    ArrayList<String> operatorList = (ArrayList<String>) BeanUtil.getProperty(terminalSnOperatorsMap, terminalSn);
                    if (null != operatorList && operatorList.size() > 0) {
                        operatorList.sort(new StatementTransactionSummaryUtil.ByLength());
                        for (String operatorSn : operatorList) {
                            StatementTransactionSummaryUtil.appendOperatorSummaryByStyle(summarySheet, operatorBasicInfoMap, operatorSummaryMap, operatorSn, isKAStatement);
                        }
                    }
                }
            }
            SheetHelper.appendLine(summarySheet, Arrays.asList());
        }

        appendUnionSummary(summarySheet, unionSummaryMap, storeSummaryMap, croList, paywayList, merchantIds, storeIds, configMerchantStores);

        //是否展示收银台信息
        boolean containsCashDesk = RunContext.currentContext().getExportIncludes().contains(StatementTaskLog.INCLUDE_CASH_DESK + "");
        if (containsCashDesk) {
            //门店收银台汇总
            appendCashDeskSummary(summarySheet, cashDeskSummaryMap, storeSnCashDesksMap, cashDeskBasicInfoMap, isKAStatement);

            // 收银台收银员展示
            if (!cashDeskCashierBasicInfoMap.isEmpty()) {
                appendCashDeskCashierSummary(summarySheet, cashDeskCashierSummaryMap, cashDeskStoreSnCashiersMap, cashDeskCashierBasicInfoMap, isKAStatement);
            }
        }
        // 班次收银员展示
        if (!cashierBasicInfoMap.isEmpty()) {
            appendCashierSummary(summarySheet, cashierSummaryMap, storeSnCashiersMap, cashierBasicInfoMap, isKAStatement);
        }
    }

    /**
     * 生成对账单汇总 excel 文件内容
     *
     * @param context
     * @param summarySheet
     * @param summary
     * @return
     */
    public void buildHbfqStatementSummary(List<String> merchantIds, String merchantUserId, List<String> departmentIds, List<String> storeIds,
                                          Map context, SXSSFSheet summarySheet, Map summary) {
        //保存业务对象名称等基本信息
        Map storeBasicInfoMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.STORE_BASIC_INFO_MAP);
        //保存业务对象汇总数据
        Map storeSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.STORE_SUMMARY_MAP);

        Map terminalSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.TERMINAL_SUMMARY_MAP);
        Map operatorSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.OPERATOR_SUMMARY_MAP);
        Map unionSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.UNION_SUMMARY_MAP);
        //标题
        HbfqTransactionUtils.addStatementSummaryTitle(context, summarySheet);
        if (CollectionUtils.isEmpty(storeIds)) {
            storeIds = getStoreIdsByMerchantId(merchantIds.get(0));
        }
        SheetHelper.appendLine(summarySheet, getValues(FQ_STORE_TRANSACTION_SUMMARY));
        appendHbfqStoresSummary(summarySheet, storeIds, storeBasicInfoMap, storeSummaryMap, unionSummaryMap);
    }

    private void appendDepartmentSummarys(SXSSFSheet summarySheet, Map storeBasicInfoMap, Map storeSummaryMap, Map unionSummaryMap, Map settlementSummaryMap, String merchantId, List<String> departmentIds, List<String> croList) {
        departmentIds.stream().sorted().forEach(departmentId -> {
            if (departmentId == null) {
                return;
            }
            List<String> kidDepartmentIds = appendDepartmentSummary(summarySheet, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, settlementSummaryMap, merchantId, departmentId, croList);
            appendDepartmentSummarys(summarySheet, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, settlementSummaryMap, merchantId, kidDepartmentIds, croList);
        });
    }

    private List<String> appendDepartmentSummary(SXSSFSheet summarySheet, Map storeBasicInfoMap, Map storeSummaryMap, Map unionSummaryMap, Map settlementSummaryMap, String merchantId, String departmentId, List<String> croList) {

        List<String> newCroList = StatementSummaryStyleUtil.reSetColumnsByStyle(croList);
        String departmentName = null;
        if (!StringUtils.isEmpty(departmentId)) {
            departmentName = departmentService.getParentDepartmentNameById(departmentId);
        }
        if (departmentName == null) {
            departmentName = BeanUtil.getPropString(merchantService.getMerchant(merchantId), Merchant.NAME);
        }
        List<DepartmentInfo> listKidDepartment = departmentService.listDepartmentTreeById(merchantId, departmentId);
        Collections.sort(listKidDepartment, Comparator.comparing(DepartmentInfo::getId));
        //部门xxx账单汇总
        SheetHelper.appendLine(summarySheet, Arrays.asList(getValue(DEPARTMENT) + "[" + departmentName + "]" + getValue(TRANSACTION_SUMMARY)));
        if (listKidDepartment == null || listKidDepartment.size() == 0) {
            List<DepartmentStoreInfo> listStores = departmentService.getDepartmentStoreByDepartmentId(departmentId);
            List<String> storeIds = listStores.stream().map(DepartmentStoreInfo::getStore_id).collect(Collectors.toList());;
            appendStoresSummary(summarySheet, storeIds, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, settlementSummaryMap, croList);
            return new ArrayList<>();
        }
        Map sumDepartmentMap = new LinkedHashMap();
        List columnDesc = new LinkedList();
        columnDesc.add(getValue(DEPARTMENT));
        columnDesc.addAll(getValueList(newCroList));
        SheetHelper.appendLine(summarySheet, columnDesc);

        List<String> kidDepartments = new LinkedList<>();
        for (DepartmentInfo kidDepartment : listKidDepartment) {
            kidDepartments.add(kidDepartment.getId());
            List<String> kidDepartmentIds = Arrays.asList(kidDepartment.getId());
            List<String> listStores = departmentService.listStoreSnsByDepartmentId(merchantId, kidDepartmentIds);
            Collections.sort(listStores);
            Map sumMap = new LinkedHashMap();
            for (String storeSn : listStores) {
                int type = 0;
                appendStoreSummary(summarySheet, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, storeSn, null, croList, sumMap, type, true);
            }
            List sumParam = new LinkedList();
            sumParam.add(kidDepartment.getName());
            for (String cro : newCroList) {
                sumParam.add(CommonUtil.getDoubleValue(sumMap, cro));
                sumDepartmentMap.put(cro, CommonUtil.getDoubleValue(sumDepartmentMap, cro) + CommonUtil.getDoubleValue(sumMap, cro));
            }
            SheetHelper.appendLine(summarySheet, sumParam);
        }
        List departmentSum = new LinkedList();
        departmentSum.add(getValue(SUMMARY));
        for (String cro : newCroList) {
            departmentSum.add(sumDepartmentMap.get(cro));
        }
        SheetHelper.appendLine(summarySheet, departmentSum);
        SheetHelper.appendLine(summarySheet, Arrays.asList());
        return kidDepartments;
    }


    private void appendPaywaySummary(SXSSFSheet summarySheet, Map paywaySummaryMap, List<String> croList, List<Integer> paywayList) {

        SheetHelper.appendLine(summarySheet, getValues(MERCHANT_TRANSACTION_SUMMARY));
        croList =StatementSummaryStyleUtil.reSetColumnsByStyle(croList);

        List columnPayway = new LinkedList();
        columnPayway.add(getValue(PAYMENT_TYPE));
        columnPayway.addAll(getValueList(croList));
        //汇总列
        List<Integer> cellIndex = Lists.newArrayList(columnPayway.indexOf(getValue(DEAL_TO_ACCOUNT_AMOUNT)), columnPayway.indexOf(getValue(STORE_IN_DEAL_TO_ACCOUNT_AMOUNT)));
        SheetHelper.appendLine(summarySheet, columnPayway, cellIndex, IndexedColors.RED.getIndex());
        for (Integer payway : paywayList) {
            Map paywaySummary = (Map) paywaySummaryMap.get(payway.toString());
            if (paywaySummary == null || paywaySummary.size() == 0) {
                paywaySummary = StatementTransactionSummaryUtil.getRecordTemplate();
            }
            List croParam = new LinkedList();
            croParam.add(OrderUtil.getPaywayDesc(payway, getLanguage()));
            for (String cro : croList) {
                switch (cro) {
                    case TRANSACTION_NO:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT));
                        break;
                    case TRANSACTION_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_AMOUNT) / 100.0);
                        break;
                    case REFUND_NO:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_COUNT));
                        break;
                    case REFUND_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_AMOUNT) / 100.0);
                        break;
                    case TRANSACTION_NET_AMOUNT:
                        croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_AMOUNT)) / 100.0);
                        break;
                    case MERCHANT_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                        break;
                    case WOSAI_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                        break;
                    case PAYMENT_TYPE_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                        break;
                    case MERCHANT_DISCOUNT_PREPAID_MODE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                        break;
                    case MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                        break;
                    case PAID_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0);
                        break;
                    case CHARGE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_FEE) / 100.0);
                        break;
                    case SHARING_AMOUNT:
                        croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.SHARING_AMOUNT, 0) + BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0)) / 100.0);
                        break;
                    case TRADE_SERVICE_SHARING_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0) / 100.0);
                        break;
                    case MERCHANT_SHARING_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.SHARING_AMOUNT, 0) / 100.0);
                        break;
                    case SETTLEMENT_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0);
                        break;
                    case DEAL_TO_ACCOUNT_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                        break;
                    case CROSS_MERCHANT_REFUND_NO:
                        if (RunContext.currentContext().crossMchRefundEnable()) {
                            croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CROSS_MCH_REFUND_COUNT));
                        }
                        break;
                    case CROSS_MERCHANT_REFUND_AMOUNT:
                        if (RunContext.currentContext().crossMchRefundEnable()) {
                            croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CROSS_MCH_REFUND_AMOUNT) / 100.0);
                        }
                        break;
                    case StatementSummary.PREFIX + TRANSACTION_NO:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_COUNT));
                        break;
                    case StatementSummary.PREFIX + TRANSACTION_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + REFUND_NO:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.REFUND_COUNT));
                        break;
                    case StatementSummary.PREFIX + REFUND_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + TRANSACTION_NET_AMOUNT:
                        croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT)) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + WOSAI_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + PAYMENT_TYPE_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_DISCOUNT_PREPAID_MODE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + PAID_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.RECEIVE_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + CHARGE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_FEE) / 100.0);
                        break;
                    case StatementSummary.PREFIX + SHARING_AMOUNT:
                        croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT, 0) + BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0)) / 100.0);
                        break;
                    case StatementSummary.PREFIX + TRADE_SERVICE_SHARING_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_SHARING_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT, 0) / 100.0);
                        break;
                    case StatementSummary.PREFIX + SETTLEMENT_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.INCOME_AMOUNT) / 100.0);
                        break;
                    case STORE_IN_DEAL_TO_ACCOUNT_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_NO:
                        if (RunContext.currentContext().crossMchRefundEnable()) {
                            croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CROSS_MCH_REFUND_COUNT));
                        }
                        break;
                    case StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_AMOUNT:
                        if (RunContext.currentContext().crossMchRefundEnable()) {
                            croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CROSS_MCH_REFUND_AMOUNT) / 100.0);
                        }
                        break;

                }
            }
            SheetHelper.appendLine(summarySheet, croParam, cellIndex, IndexedColors.RED.getIndex());
        }
        SheetHelper.appendLine(summarySheet, Arrays.asList());

    }

    //收银台汇总
    private void appendCashDeskSummary(SXSSFSheet summarySheet, Map cashDeskSummaryMap, Map<String, List> storeSnCashDesksMap, Map cashDeskBasicInfoMap, boolean isKAStatement) {

        // 门店收银台交易汇总
        SheetHelper.appendLine(summarySheet, getValues(STORE_CASH_DESK_TRANSACTION_SUMMARY_LIST));

        //收银台汇总表头
        List<String> columnDescByTaskStyle = StatementSummaryStyleUtil.getCashDeskColumnDescByTaskStyle();
        StatementSummaryStyleUtil.resetColumns(columnDescByTaskStyle, isKAStatement);
        SheetHelper.appendLine(summarySheet, columnDescByTaskStyle);

        Collections.sort(new ArrayList<>(storeSnCashDesksMap.keySet()), new StatementTransactionSummaryUtil.ByLength());
        for (String storeSn : storeSnCashDesksMap.keySet()) {
            Map store = storeService.getStoreByStoreSn(storeSn);
            String storeName = BeanUtil.getPropString(store, Store.NAME);
            SheetHelper.appendLine(summarySheet, Arrays.asList(storeName,""));

            ArrayList<String> cashDeskIdList = (ArrayList<String>) BeanUtil.getProperty(storeSnCashDesksMap, storeSn);

            cashDeskIdList.sort(new StatementTransactionSummaryUtil.ByLength());
            for (String cashDeskId : cashDeskIdList) {
                StatementTransactionSummaryUtil.appendCashDeskSummaryByStyle(summarySheet, cashDeskBasicInfoMap, cashDeskSummaryMap, cashDeskId, isKAStatement);
            }
        }
        SheetHelper.appendLine(summarySheet, Arrays.asList());
    }

    //收银台收银员汇总
    private void appendCashDeskCashierSummary(SXSSFSheet summarySheet, Map cashierSummaryMap, Map<String, Map<String, List>> storeSnCashiersMap, Map cashierBasicInfoMap, boolean isKAStatement) {
        // 收银员交易汇总
        SheetHelper.appendLine(summarySheet, getValues(STORE_CASH_DESK_CHANGE_SHIFTS_SUMMARY_LIST));

        //收银员汇总表头
        List<String> columnDescByTaskStyle = StatementSummaryStyleUtil.getCashDeskChangeShiftsColumnDescByTaskStyle();
        StatementSummaryStyleUtil.resetColumns(columnDescByTaskStyle, isKAStatement);
        SheetHelper.appendLine(summarySheet, columnDescByTaskStyle);

        Collections.sort(new ArrayList<>(storeSnCashiersMap.keySet()), new StatementTransactionSummaryUtil.ByLength());
        for (String storeSn : storeSnCashiersMap.keySet()) {
            Map store = storeService.getStoreByStoreSn(storeSn);
            String storeName = BeanUtil.getPropString(store, Store.NAME);
            SheetHelper.appendLine(summarySheet, Arrays.asList(storeName, ""));

            Map<String, List> cashDeskBatchs =storeSnCashiersMap.get(storeSn);
            Collections.sort(new ArrayList<>(cashDeskBatchs.keySet()), new StatementTransactionSummaryUtil.ByLength());
            for (String cashDeskId : cashDeskBatchs.keySet()) {
                ArrayList<String> batchIds = (ArrayList<String>) BeanUtil.getProperty(cashDeskBatchs, cashDeskId);
                // 部分班次因为结束时间不在对账单导出周期内，被移除后存在收银台交易班批次为空的情况
                if (CollectionUtil.isEmpty(batchIds)) {
                    continue;
                }
                Map cashDeskInfo = cashDeskService.getSimpleCashDeskById(cashDeskId);
                String cashDeskName = MapUtil.getString(cashDeskInfo, CashDesk.NAME);
                String cashDeskSn = MapUtil.getString(cashDeskInfo, CashDesk.SN);
                SheetHelper.appendLine(summarySheet, Arrays.asList("", cashDeskName, cashDeskSn));
                batchIds.sort((s1 , s2) -> {
                    Long i1 = new Long(s1);
                    Long i2 = new Long(s2);
                    return i1 > i2 ? -1 : ((i1== i2) ? 0 : 1);
                });
                for (String batchId : batchIds) {
                    StatementTransactionSummaryUtil.appendCashDeskChangeShiftsSummaryByStyle(summarySheet, cashierBasicInfoMap, cashierSummaryMap, batchId, isKAStatement);
                }
            }
        }
        SheetHelper.appendLine(summarySheet, Arrays.asList());
    }

    //收银员汇总
    private void appendCashierSummary(SXSSFSheet summarySheet, Map cashierSummaryMap, Map<String, List> storeSnCashiersMap, Map cashierBasicInfoMap, boolean isKAStatement) {
        // 收银员交易汇总
        SheetHelper.appendLine(summarySheet, getValues(STORE_CASHIER_TRANSACTION_SUMMARY_LIST));
        //收银员汇总表头
        List<String> columnDescByTaskStyle = StatementSummaryStyleUtil.getCashierColumnDescByTaskStyle();
        StatementSummaryStyleUtil.resetColumns(columnDescByTaskStyle, isKAStatement);
        SheetHelper.appendLine(summarySheet, columnDescByTaskStyle);

        Collections.sort(new ArrayList<>(storeSnCashiersMap.keySet()), new StatementTransactionSummaryUtil.ByLength());
        for (String storeSn : storeSnCashiersMap.keySet()) {
            Map store = storeService.getStoreByStoreSn(storeSn);
            String storeName = BeanUtil.getPropString(store, Store.NAME);
            SheetHelper.appendLine(summarySheet, Arrays.asList(storeName, ""));

            ArrayList<String> cashierList = (ArrayList<String>) BeanUtil.getProperty(storeSnCashiersMap, storeSn);
            cashierList.sort(new StatementTransactionSummaryUtil.ByLength());
            for (String cashier : cashierList) {
                StatementTransactionSummaryUtil.appendCashierSummaryByStyle(summarySheet, cashierBasicInfoMap, cashierSummaryMap, storeSn, cashier, isKAStatement);
            }
        }
        SheetHelper.appendLine(summarySheet, Arrays.asList());
    }

    private void appendUnionSummary(SXSSFSheet summarySheet, Map unionSummaryMap, Map storeSummaryMap, List<String> croList, List<Integer> paywayList,
                                    List<String> merchantIds, List<String> storeIds, List<Map> configStores) {

        croList = StatementSummaryStyleUtil.reSetColumnsByStyle(croList);

        //暂时不涉及银行卡报表
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            return;
        }

        SheetHelper.appendLine(summarySheet, getValues(MERCHANT_STORE_PAYWAY_TRANSACTION_SUMMARY));
        List columnPayway = new LinkedList();

        columnPayway.add(getValue(STORE_NAME));
        columnPayway.add(getValue(PAYMENT_TYPE));


        columnPayway.addAll(getValueList(croList));

        SheetHelper.appendLine(summarySheet, columnPayway);

        List<String> storeList = storeIds;
        //1、先判断是不是取用户配置的有序门店列表，并根据storeIds筛选
        if (CollectionUtil.isNotEmpty(configStores)) {
            storeList = sortStoreIds(storeIds, configStores);
        }

        //2、若storeList为空， 则获取商户下的门店列表
        if (CollectionUtil.isNotEmpty(merchantIds) && CollectionUtil.isEmpty(storeList)) {
            storeList = getStoreIdsByMerchantId(merchantIds.get(0));
        }

        for (String merchantId : merchantIds) {
            String merchantSn = BeanUtil.getPropString(merchantService.getMerchant(merchantId), Merchant.SN);

            for (String storeId : storeList) {
                Map store = storeService.getStore(storeId);
                String storeSn = BeanUtil.getPropString(store, Store.SN);
                String storeName = BeanUtil.getPropString(store, Store.NAME);
                Map storeSummary = (Map) storeSummaryMap.get(storeSn);
                //如果门店交易为0，且门店状态非开启状态，跳过
                if ((BeanUtil.getPropInt(store, Store.STATUS) != Store.STATUS_ENABLED &&
                                BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_AMOUNT) == 0 &&
                                BeanUtil.getPropLong(storeSummary, StatementSummary.REFUND_AMOUNT) == 0)){
                    continue;
                }
                SheetHelper.appendLine(summarySheet, Arrays.asList(storeName));

                for (Integer payway : paywayList) {
                    Map paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + payway);
                    if (paywaySummary == null || paywaySummary.size() == 0) {
                        paywaySummary = StatementTransactionSummaryUtil.getRecordTemplate();
                    }
                    List croParam = new LinkedList();
                    croParam.add("");
                    String paywayDesc = OrderUtil.getPaywayDesc(payway, getLanguage());
                    if (StringUtils.isEmpty(paywayDesc)) {
                        continue;
                    }
                    croParam.add(paywayDesc);
                    for (String cro : croList) {
                        switch (cro) {
                            case TRANSACTION_NO:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT));
                                break;
                            case TRANSACTION_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_AMOUNT) / 100.0);
                                break;
                            case REFUND_NO:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_COUNT));
                                break;
                            case REFUND_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_AMOUNT) / 100.0);
                                break;
                            case TRANSACTION_NET_AMOUNT:
                                croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_AMOUNT)) / 100.0);
                                break;
                            case MERCHANT_DISCOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                                break;
                            case WOSAI_DISCOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                                break;
                            case PAYMENT_TYPE_DISCOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                                break;
                            case MERCHANT_DISCOUNT_PREPAID_MODE:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                                break;
                            case MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                                break;
                            case PAID_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0);
                                break;
                            case CHARGE:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_FEE) / 100.0);
                                break;
                            case SHARING_AMOUNT:
                                croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.SHARING_AMOUNT, 0) + BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0)) / 100.0);
                                break;
                            case TRADE_SERVICE_SHARING_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0) / 100.0);
                                break;
                            case MERCHANT_SHARING_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.SHARING_AMOUNT, 0) / 100.0);
                                break;
                            case SETTLEMENT_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0);
                                break;
                            case DEAL_TO_ACCOUNT_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                                break;
                            case CROSS_MERCHANT_REFUND_NO:
                                if (RunContext.currentContext().crossMchRefundEnable()) {
                                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CROSS_MCH_REFUND_COUNT));
                                }
                                break;
                            case CROSS_MERCHANT_REFUND_AMOUNT:
                                if (RunContext.currentContext().crossMchRefundEnable()) {
                                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CROSS_MCH_REFUND_AMOUNT) / 100.0);
                                }
                                break;
                            case StatementSummary.PREFIX + TRANSACTION_NO:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_COUNT));
                                break;
                            case StatementSummary.PREFIX + TRANSACTION_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) / 100.0);
                                break;
                            case StatementSummary.PREFIX + REFUND_NO:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.REFUND_COUNT));
                                break;
                            case StatementSummary.PREFIX + REFUND_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT) / 100.0);
                                break;
                            case StatementSummary.PREFIX + TRANSACTION_NET_AMOUNT:
                                croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT)) / 100.0);
                                break;
                            case StatementSummary.PREFIX + MERCHANT_DISCOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                                break;
                            case StatementSummary.PREFIX + WOSAI_DISCOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                                break;
                            case StatementSummary.PREFIX + PAYMENT_TYPE_DISCOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                                break;
                            case StatementSummary.PREFIX + MERCHANT_DISCOUNT_PREPAID_MODE:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                                break;
                            case StatementSummary.PREFIX + MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                                break;
                            case StatementSummary.PREFIX + PAID_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.RECEIVE_AMOUNT) / 100.0);
                                break;
                            case StatementSummary.PREFIX + CHARGE:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_FEE) / 100.0);
                                break;
                            case StatementSummary.PREFIX + SHARING_AMOUNT:
                                croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT, 0) + BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0)) / 100.0);
                                break;
                            case StatementSummary.PREFIX + TRADE_SERVICE_SHARING_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0) / 100.0);
                                break;
                            case StatementSummary.PREFIX + MERCHANT_SHARING_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT, 0) / 100.0);
                                break;
                            case StatementSummary.PREFIX + SETTLEMENT_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.INCOME_AMOUNT) / 100.0);
                                break;
                            case STORE_IN_DEAL_TO_ACCOUNT_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                                break;
                            case StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_NO:
                                if (RunContext.currentContext().crossMchRefundEnable()) {
                                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CROSS_MCH_REFUND_COUNT));
                                }
                                break;
                            case StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_AMOUNT:
                                if (RunContext.currentContext().crossMchRefundEnable()) {
                                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CROSS_MCH_REFUND_AMOUNT) / 100.0);
                                }
                                break;
                        }
                    }
                    SheetHelper.appendLine(summarySheet, croParam);
                }
            }
        }
        SheetHelper.appendLine(summarySheet, Arrays.asList());
    }

    private void appendStoreSummary(SXSSFSheet summarySheet, Map storeBasicInfoMap, Map storeSummaryMap, Map unionSummaryMap, String storeSn, String storeName, List<String> croList, Map sumMap, int type, boolean addHeader) {
        Map storeSummary = (Map) storeSummaryMap.get(storeSn);
        Map store = storeService.getStoreByStoreSn(storeSn);
        //如果门店交易为0，且门店状态非开启状态，跳过
        if ((MapUtil.isEmpty(storeSummary) && BeanUtil.getPropInt(store, Store.STATUS) != Store.STATUS_ENABLED) ||
                (BeanUtil.getPropInt(store, Store.STATUS) != Store.STATUS_ENABLED &&
                        BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_AMOUNT) == 0 &&
                        BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_AMOUNT) == BeanUtil.getPropLong(storeSummary, StatementSummary.REFUND_AMOUNT))){
            return;
        }
        String merchantSn = BeanUtil.getPropString(merchantService.getMerchant(BeanUtil.getPropString(store, Store.MERCHANT_ID)), Merchant.SN);

        if(addHeader) {
            croList = StatementSummaryStyleUtil.getMerchantStatementStoreHeaderOrColumn(croList);
        }

        //增加商家记账、储值核销、现金储值充值、直清交易等的结算金额汇总
        List<String> allCroList = Lists.newArrayList(CHARGE_SETTLEMENT_AMOUNT, STORE_PAY_RECEIVE_AMOUNT, STORE_IN_CASH_SETTLEMENT_AMOUNT, STORE_IN_OTHER_CHARGE_SETTLEMENT_AMOUNT, LIQUIDATION_SETTLEMENT_AMOUNT, StatementSummary.PREFIX + LIQUIDATION_SETTLEMENT_AMOUNT, FOREIGN_CARD_SETTLEMENT_AMOUNT);
        allCroList.addAll(croList);

        Map paywaySummary;
        List croParam = new LinkedList();

        if (type == 1) {
            croParam.add(storeName);
        }
        for (String cro : allCroList) {
            switch (cro) {
                case TRANSACTION_NO:
                    long tradeCount = BeanUtil.getPropLong(sumMap, TRANSACTION_NO) + BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_COUNT);
                    sumMap.put(TRANSACTION_NO, tradeCount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_COUNT));
                    break;
                case TRANSACTION_AMOUNT:
                    double tradeAmount = CommonUtil.getDoubleValue(sumMap, TRANSACTION_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_AMOUNT) / 100.0;
                    sumMap.put(TRANSACTION_AMOUNT, tradeAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_AMOUNT) / 100.0);
                    break;
                case REFUND_NO:
                    long refundCount = BeanUtil.getPropLong(sumMap, REFUND_NO) + BeanUtil.getPropLong(storeSummary, StatementSummary.REFUND_COUNT);
                    sumMap.put(REFUND_NO, refundCount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.REFUND_COUNT));
                    break;
                case REFUND_AMOUNT:
                    double refundAmount = CommonUtil.getDoubleValue(sumMap, REFUND_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.REFUND_AMOUNT) / 100.0;
                    sumMap.put(REFUND_AMOUNT, refundAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.REFUND_AMOUNT) / 100.0);
                    break;
                case TRANSACTION_NET_AMOUNT:
                    double netAmount = CommonUtil.getDoubleValue(sumMap, TRANSACTION_NET_AMOUNT) + (BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.REFUND_AMOUNT)) / 100.0;
                    sumMap.put(TRANSACTION_NET_AMOUNT, netAmount);
                    croParam.add((BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.REFUND_AMOUNT)) / 100.0);
                    break;
                case MERCHANT_DISCOUNT:
                    double merchantDiscount = CommonUtil.getDoubleValue(sumMap, MERCHANT_DISCOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.MERCHANT_DISCOUNT) / 100.0;
                    sumMap.put(MERCHANT_DISCOUNT, merchantDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                    break;
                case WOSAI_DISCOUNT:
                    double shouqianbaDiscount = CommonUtil.getDoubleValue(sumMap, WOSAI_DISCOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0;
                    sumMap.put(WOSAI_DISCOUNT, shouqianbaDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                    break;
                case PAYMENT_TYPE_DISCOUNT:
                    double channelDiscount = CommonUtil.getDoubleValue(sumMap, PAYMENT_TYPE_DISCOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.CHANNEL_DISCOUNT) / 100.0;
                    sumMap.put(PAYMENT_TYPE_DISCOUNT, channelDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                    break;
                case MERCHANT_DISCOUNT_PREPAID_MODE:
                    double channelMchTopUpDiscount = CommonUtil.getDoubleValue(sumMap, MERCHANT_DISCOUNT_PREPAID_MODE) + BeanUtil.getPropLong(storeSummary, StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0;
                    sumMap.put(MERCHANT_DISCOUNT_PREPAID_MODE, channelMchTopUpDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                    break;
                case MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                    double channelMchDiscount = CommonUtil.getDoubleValue(sumMap, MERCHANT_DISCOUNT_NON_PREPAID_MODE) + BeanUtil.getPropLong(storeSummary, StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0;
                    sumMap.put(MERCHANT_DISCOUNT_NON_PREPAID_MODE, channelMchDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                    break;
                case PAID_AMOUNT:
                    double receiveAmount = CommonUtil.getDoubleValue(sumMap, PAID_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.RECEIVE_AMOUNT) / 100.0;
                    sumMap.put(PAID_AMOUNT, receiveAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.RECEIVE_AMOUNT) / 100.0);
                    break;
                case CHARGE:
                    double tradeFee = CommonUtil.getDoubleValue(sumMap, CHARGE) + BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_FEE) / 100.0;
                    sumMap.put(CHARGE, tradeFee);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_FEE) / 100.0);
                    break;
                case SHARING_AMOUNT:
                    double sharingAmount = CommonUtil.getDoubleValue(sumMap, SHARING_AMOUNT) + (BeanUtil.getPropLong(storeSummary, StatementSummary.SHARING_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT)) / 100.0;
                    sumMap.put(SHARING_AMOUNT, sharingAmount);
                    croParam.add((BeanUtil.getPropLong(storeSummary, StatementSummary.SHARING_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT)) / 100.0);
                    break;
                case TRADE_SERVICE_SHARING_AMOUNT:
                    double tradeServiceSharingAmount = CommonUtil.getDoubleValue(sumMap, TRADE_SERVICE_SHARING_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT) / 100.0;
                    sumMap.put(TRADE_SERVICE_SHARING_AMOUNT, tradeServiceSharingAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT) / 100.0);
                    break;
                case MERCHANT_SHARING_AMOUNT:
                    double merchantSharingAmount = CommonUtil.getDoubleValue(sumMap, MERCHANT_SHARING_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.SHARING_AMOUNT) / 100.0;
                    sumMap.put(MERCHANT_SHARING_AMOUNT, merchantSharingAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.SHARING_AMOUNT) / 100.0);
                    break;
                case SETTLEMENT_AMOUNT:
                    double incomeAmount = CommonUtil.getDoubleValue(sumMap, SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.INCOME_AMOUNT) / 100.0;
                    sumMap.put(SETTLEMENT_AMOUNT, incomeAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.INCOME_AMOUNT) / 100.0);
                    break;
                case DEAL_TO_ACCOUNT_AMOUNT:
                    double dealToAccountAmount = CommonUtil.getDoubleValue(sumMap, DEAL_TO_ACCOUNT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0;
                    sumMap.put(DEAL_TO_ACCOUNT_AMOUNT, dealToAccountAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                    break;
                case CROSS_MERCHANT_REFUND_NO:
                    long crossMchRefundNo = BeanUtil.getPropLong(sumMap, CROSS_MERCHANT_REFUND_NO) + BeanUtil.getPropLong(storeSummary, StatementSummary.CROSS_MCH_REFUND_COUNT);
                    sumMap.put(CROSS_MERCHANT_REFUND_NO, crossMchRefundNo);
                    if (RunContext.currentContext().crossMchRefundEnable()) {
                        croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.CROSS_MCH_REFUND_COUNT));
                    }
                    break;
                case CROSS_MERCHANT_REFUND_AMOUNT:
                    double crossMchRefundAmount = BeanUtil.getPropLong(sumMap, CROSS_MERCHANT_REFUND_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.CROSS_MCH_REFUND_AMOUNT) / 100.0;
                    sumMap.put(CROSS_MERCHANT_REFUND_AMOUNT, crossMchRefundAmount);
                    if (RunContext.currentContext().crossMchRefundEnable()) {
                        croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.CROSS_MCH_REFUND_AMOUNT) / 100.0);
                    }
                    break;
                case HBFQ_MCH_DISCOUNT_AMOUNT:
                    double hbfqDiscountAmount = CommonUtil.getDoubleValue(sumMap, HBFQ_MCH_DISCOUNT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.HBFQ_MCH_DISCOUNT_AMOUNT) / 100.0;
                    sumMap.put(HBFQ_MCH_DISCOUNT_AMOUNT, hbfqDiscountAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.HBFQ_MCH_DISCOUNT_AMOUNT) / 100.0);
                    break;

                case StatementSummary.PREFIX + TRANSACTION_NO:
                    long storeTradeCount = BeanUtil.getPropLong(sumMap, StatementSummary.PREFIX + TRANSACTION_NO) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_COUNT);
                    sumMap.put(StatementSummary.PREFIX + TRANSACTION_NO, storeTradeCount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_COUNT));
                    break;
                case StatementSummary.PREFIX + TRANSACTION_AMOUNT:
                    double storeTradeAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + TRANSACTION_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + TRANSACTION_AMOUNT, storeTradeAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + REFUND_NO:
                    long storeRefundCount = BeanUtil.getPropLong(sumMap, StatementSummary.PREFIX + REFUND_NO) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.REFUND_COUNT);
                    sumMap.put(StatementSummary.PREFIX + REFUND_NO, storeRefundCount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.REFUND_COUNT));
                    break;
                case StatementSummary.PREFIX + REFUND_AMOUNT:
                    double storeRefundAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + REFUND_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + REFUND_AMOUNT, storeRefundAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + TRANSACTION_NET_AMOUNT:
                    double storeNetAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + TRANSACTION_NET_AMOUNT) + (BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT)) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + TRANSACTION_NET_AMOUNT, storeNetAmount);
                    croParam.add((BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT)) / 100.0);
                    break;
                case StatementSummary.PREFIX + MERCHANT_DISCOUNT:
                    double storeMerchantDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + MERCHANT_DISCOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.MERCHANT_DISCOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + MERCHANT_DISCOUNT, storeMerchantDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + WOSAI_DISCOUNT:
                    double storeShouqianbaDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + WOSAI_DISCOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + WOSAI_DISCOUNT, storeShouqianbaDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + PAYMENT_TYPE_DISCOUNT:
                    double storeChannelDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + PAYMENT_TYPE_DISCOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_DISCOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + PAYMENT_TYPE_DISCOUNT, storeChannelDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + MERCHANT_DISCOUNT_PREPAID_MODE:
                    double storeChannelMchTopUpDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + MERCHANT_DISCOUNT_PREPAID_MODE) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + MERCHANT_DISCOUNT_PREPAID_MODE, storeChannelMchTopUpDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                    double storeChannelMchDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + MERCHANT_DISCOUNT_NON_PREPAID_MODE) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + MERCHANT_DISCOUNT_NON_PREPAID_MODE, storeChannelMchDiscount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + PAID_AMOUNT:
                    double storeReceiveAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + PAID_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.RECEIVE_AMOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + PAID_AMOUNT, storeReceiveAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.RECEIVE_AMOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + CHARGE:
                    double storeTradeFee = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + CHARGE) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_FEE) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + CHARGE, storeTradeFee);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_FEE) / 100.0);
                    break;
                case StatementSummary.PREFIX + SHARING_AMOUNT:
                    double storeSharingAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + SHARING_AMOUNT) + (BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT)) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + SHARING_AMOUNT, storeSharingAmount);
                    croParam.add((BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT)) / 100.0);
                    break;
                case StatementSummary.PREFIX + TRADE_SERVICE_SHARING_AMOUNT:
                    double storeTradeServiceSharingAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + TRADE_SERVICE_SHARING_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + TRADE_SERVICE_SHARING_AMOUNT, storeTradeServiceSharingAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary,  StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + MERCHANT_SHARING_AMOUNT:
                    double storeMerchantSharingAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + MERCHANT_SHARING_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + MERCHANT_SHARING_AMOUNT, storeMerchantSharingAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + SETTLEMENT_AMOUNT:
                    double storeIncomeAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.INCOME_AMOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + SETTLEMENT_AMOUNT, storeIncomeAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.INCOME_AMOUNT) / 100.0);
                    break;
                case STORE_IN_DEAL_TO_ACCOUNT_AMOUNT:
                    double storeInDealToAccountAmount = CommonUtil.getDoubleValue(sumMap, STORE_IN_DEAL_TO_ACCOUNT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0;
                    sumMap.put(STORE_IN_DEAL_TO_ACCOUNT_AMOUNT, storeInDealToAccountAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                    break;
                case StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_NO:
                    long storeCrossMchRefundNo = BeanUtil.getPropLong(sumMap, StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_NO) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CROSS_MCH_REFUND_COUNT);
                    sumMap.put(StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_NO, storeCrossMchRefundNo);
                    if (RunContext.currentContext().crossMchRefundEnable()) {
                        croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CROSS_MCH_REFUND_COUNT));
                    }
                    break;
                case StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_AMOUNT:
                    double storeCrossMchRefundAmount = BeanUtil.getPropLong(sumMap, StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CROSS_MCH_REFUND_AMOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + CROSS_MERCHANT_REFUND_AMOUNT, storeCrossMchRefundAmount);
                    if (RunContext.currentContext().crossMchRefundEnable()) {
                        croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.CROSS_MCH_REFUND_AMOUNT) / 100.0);
                    }
                    break;
                case StatementSummary.PREFIX + HBFQ_MCH_DISCOUNT_AMOUNT:
                    double storeHbfqDiscountAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + HBFQ_MCH_DISCOUNT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.HBFQ_MCH_DISCOUNT_AMOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + HBFQ_MCH_DISCOUNT_AMOUNT, storeHbfqDiscountAmount);
                    croParam.add(BeanUtil.getPropLong(storeSummary, StatementSummary.PREFIX + StatementSummary.HBFQ_MCH_DISCOUNT_AMOUNT) / 100.0);
                    break;


                case ALIPAY_NEW_NO:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.ALIPAY_NEW);
                    long alipayCount = BeanUtil.getPropLong(sumMap, ALIPAY_NEW_NO) + BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT);
                    sumMap.put(ALIPAY_NEW_NO, alipayCount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT));
                    break;
                case ALIPAY_NEW_PAID_AMOUNT:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.ALIPAY_NEW);
                    double alipayReceiveAmount = CommonUtil.getDoubleValue(sumMap, ALIPAY_NEW_PAID_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0;
                    sumMap.put(ALIPAY_NEW_PAID_AMOUNT, alipayReceiveAmount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0);
                    break;
                case ALIPAY_NEW_SETTLEMENT_AMOUNT:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.ALIPAY_NEW);
                    double alipayIncomeAmount = CommonUtil.getDoubleValue(sumMap, ALIPAY_NEW_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0;
                    sumMap.put(ALIPAY_NEW_SETTLEMENT_AMOUNT, alipayIncomeAmount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0);
                    break;
                case WECHAT_NO:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.WEIXIN);
                    double wechatCount = BeanUtil.getPropLong(sumMap, WECHAT_NO) + BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT);
                    sumMap.put(WECHAT_NO, wechatCount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT));
                    break;
                case WECHAT_PAID_AMOUNT:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.WEIXIN);
                    double wechatReceiveAmount = CommonUtil.getDoubleValue(sumMap, WECHAT_PAID_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0;
                    sumMap.put(WECHAT_PAID_AMOUNT, wechatReceiveAmount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0);
                    break;
                case WECHAT_SETTLEMENT_AMOUNT:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.WEIXIN);
                    double wechatIncomeAmount = CommonUtil.getDoubleValue(sumMap, WECHAT_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0;
                    sumMap.put(WECHAT_SETTLEMENT_AMOUNT, wechatIncomeAmount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0);
                    break;
                case UNIONPAY_CLOUD_FLASH_PAYMENT_NO:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.YLCODEPAY);
                    double unionpayCount = BeanUtil.getPropLong(sumMap, UNIONPAY_CLOUD_FLASH_PAYMENT_NO) + BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT);
                    sumMap.put(UNIONPAY_CLOUD_FLASH_PAYMENT_NO, unionpayCount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT));
                    break;
                case UNIONPAY_CLOUD_FLASH_PAYMENT_PAID_AMOUNT:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.YLCODEPAY);
                    double unionpayReceiveAmount = CommonUtil.getDoubleValue(sumMap, UNIONPAY_CLOUD_FLASH_PAYMENT_PAID_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0;
                    sumMap.put(UNIONPAY_CLOUD_FLASH_PAYMENT_PAID_AMOUNT, unionpayReceiveAmount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0);
                    break;
                case UNIONPAY_CLOUD_FLASH_PAYMENT_SETTLEMENT_AMOUNT:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.YLCODEPAY);
                    double unionpayIncomeAmount = CommonUtil.getDoubleValue(sumMap, UNIONPAY_CLOUD_FLASH_PAYMENT_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0;
                    sumMap.put(UNIONPAY_CLOUD_FLASH_PAYMENT_SETTLEMENT_AMOUNT, unionpayIncomeAmount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0);
                    break;
                case BANKCARD_PAYMENT_NO:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.BANK_CARD);
                    double bankCardCount = BeanUtil.getPropLong(sumMap, BANKCARD_PAYMENT_NO) + BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT);
                    sumMap.put(BANKCARD_PAYMENT_NO, bankCardCount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT));
                    break;
                case BANKCARD_PAYMENT_PAID_AMOUNT:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.BANK_CARD);
                    double bankCardPaidAmount = CommonUtil.getDoubleValue(sumMap, BANKCARD_PAYMENT_PAID_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0;
                    sumMap.put(BANKCARD_PAYMENT_PAID_AMOUNT, bankCardPaidAmount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0);
                    break;
                case BANKCARD_PAYMENT_SETTLEMENT_AMOUNT:
                    paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + StatementObjectConfig.BANK_CARD);
                    double bankCardSettlementAmount = CommonUtil.getDoubleValue(sumMap, BANKCARD_PAYMENT_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0;
                    sumMap.put(BANKCARD_PAYMENT_SETTLEMENT_AMOUNT, bankCardSettlementAmount);
                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0);
                    break;
                case CHARGE_SETTLEMENT_AMOUNT:
                    double chargeIncomeAmount = CommonUtil.getDoubleValue(sumMap, CHARGE_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.CHARGE_INCOME_AMOUNT) / 100.0;
                    sumMap.put(CHARGE_SETTLEMENT_AMOUNT, chargeIncomeAmount);
                    break;
                case STORE_PAY_RECEIVE_AMOUNT:
                    double storePayIncomeAmount = CommonUtil.getDoubleValue(sumMap, STORE_PAY_RECEIVE_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.STORE_PAY_RECEIVE_AMOUNT) / 100.0;
                    sumMap.put(STORE_PAY_RECEIVE_AMOUNT, storePayIncomeAmount);
                    break;
                case STORE_IN_CASH_SETTLEMENT_AMOUNT:
                    double cashStoreInIncomeAmount = CommonUtil.getDoubleValue(sumMap, STORE_IN_CASH_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.STORE_IN_CASH_INCOME_AMOUNT) / 100.0;
                    sumMap.put(STORE_IN_CASH_SETTLEMENT_AMOUNT, cashStoreInIncomeAmount);
                    break;
                case STORE_IN_OTHER_CHARGE_SETTLEMENT_AMOUNT:
                    double otherChargeStoreInIncomeAmount = CommonUtil.getDoubleValue(sumMap, STORE_IN_OTHER_CHARGE_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.STORE_IN_OTHER_CHARGE_INCOME_AMOUNT) / 100.0;
                    sumMap.put(STORE_IN_OTHER_CHARGE_SETTLEMENT_AMOUNT, otherChargeStoreInIncomeAmount);
                    break;
                case LIQUIDATION_SETTLEMENT_AMOUNT:
                    double liquidationIncomeAmount = CommonUtil.getDoubleValue(sumMap, LIQUIDATION_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(storeSummary, StatementSummary.LIQUIDATION_INCOME_AMOUNT) / 100.0;
                    sumMap.put(LIQUIDATION_SETTLEMENT_AMOUNT, liquidationIncomeAmount);
                    break;
                case StatementSummary.PREFIX + LIQUIDATION_SETTLEMENT_AMOUNT:
                    double storeInLiquidationIncomeAmount = CommonUtil.getDoubleValue(sumMap,StatementSummary.PREFIX + LIQUIDATION_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(storeSummary,StatementSummary.PREFIX + StatementSummary.LIQUIDATION_INCOME_AMOUNT) / 100.0;
                    sumMap.put(StatementSummary.PREFIX + LIQUIDATION_SETTLEMENT_AMOUNT, storeInLiquidationIncomeAmount);
                    break;
                case FOREIGN_CARD_SETTLEMENT_AMOUNT:
                    double foreignCardTradeIncomeAmount = CommonUtil.getDoubleValue(sumMap, FOREIGN_CARD_SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(storeSummary,StatementSummary.FOREIGN_CARD_INCOME_AMOUNT) / 100.0;
                    sumMap.put(FOREIGN_CARD_SETTLEMENT_AMOUNT, foreignCardTradeIncomeAmount);
                    break;
            }
        }
        if (type == 1) {
            List<String> tempCroList = Lists.newArrayList("storeName");
            tempCroList.addAll(croList);
            List<Integer> cellIndex = Lists.newArrayList(tempCroList.indexOf(DEAL_TO_ACCOUNT_AMOUNT), tempCroList.indexOf(STORE_IN_DEAL_TO_ACCOUNT_AMOUNT));
            SheetHelper.appendLine(summarySheet, croParam, cellIndex, IndexedColors.RED.getIndex());
        }
    }

    private void appendStoresSummary(SXSSFSheet summarySheet, List<String> storeIds, Map storeBasicInfoMap, Map storeSummaryMap, Map unionSummaryMap, Map settlementSummaryMap, List<String> croList, List<Map> configStores) {
        List<String> newCroList = StatementSummaryStyleUtil.getMerchantStatementStoreHeaderOrColumn(croList);
        List columnDesc = new LinkedList();
        columnDesc.add(getValue(STORE_NAME));
        columnDesc.addAll(getValueList(newCroList));
        List<Integer> cellIndex = Lists.newArrayList(columnDesc.indexOf(getValue(DEAL_TO_ACCOUNT_AMOUNT)), columnDesc.indexOf(getValue(STORE_IN_DEAL_TO_ACCOUNT_AMOUNT)));
        SheetHelper.appendLine(summarySheet, columnDesc, cellIndex, IndexedColors.RED.getIndex());
        Map sumMap = new LinkedHashMap();
        sumMap.put(STORE_NAME, getValue(SUMMARY));

        //忽略关闭、禁用状态且数据为0的门店

        List<String> removeSns = StatementCommonUtil.removeUndisplayMerchant(storeSummaryMap, businessCommonService);
        Set<String> storeKeys = storeSummaryMap.keySet();
        removeSns.forEach(storeSummaryMap::remove);
        List<Map<String, Object>> stores = getSortedStoreIdsByMerchantUserId(storeIds, storeKeys, removeSns, configStores);
        for (Map<String, Object> store : stores) {
            String storeSn = BeanUtil.getPropString(store, Store.SN);
            int storeType = 1;
            String storeName = BeanUtil.getPropString(store, Store.NAME);
            appendStoreSummary(summarySheet, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, storeSn, storeName, croList, sumMap, storeType, true);
        }

        // 汇总
        List sumParam = new LinkedList();
        sumParam.add(getValue(SUMMARY));
        for (String cro : newCroList) {
            sumParam.add(CommonUtil.getDoubleValue(sumMap, cro));
        }
        SheetHelper.appendLine(summarySheet, sumParam, cellIndex, IndexedColors.RED.getIndex());
        SheetHelper.appendLine(summarySheet, Arrays.asList());

        List<String> settleList = Lists.newArrayList(SETTLEMENT_AMOUNT, CHARGE_SETTLEMENT_AMOUNT, STORE_PAY_RECEIVE_AMOUNT, LIQUIDATION_SETTLEMENT_AMOUNT,  StatementSummary.PREFIX + SETTLEMENT_AMOUNT, STORE_IN_CASH_SETTLEMENT_AMOUNT, StatementSummary.PREFIX + LIQUIDATION_SETTLEMENT_AMOUNT, STORE_IN_OTHER_CHARGE_SETTLEMENT_AMOUNT, FOREIGN_CARD_SETTLEMENT_AMOUNT);

        sumMap.forEach((key, value) -> {
            if (settleList.contains(key)) {
                double sum = MapUtils.getDoubleValue(settlementSummaryMap, key, 0)  + Double.parseDouble(String.valueOf(value));
                settlementSummaryMap.put(key, sum);
            }
        });
    }

    private void appendStoresSummary(SXSSFSheet summarySheet, List<String> storeIds, Map storeBasicInfoMap, Map storeSummaryMap, Map unionSummaryMap, Map settlementSummaryMap, List<String> croList) {
        appendStoresSummary(summarySheet, storeIds, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, settlementSummaryMap, croList, null);
    }

    private void appendSettlementSummary(SXSSFSheet summarySheet, Map sumMap, Map context){
        boolean isIncludeStore = RunContext.currentContext().getExportIncludes().contains(StatementTaskLog.INCLUDE_STORE_IN + "");
        boolean foreignCardTradeStatus = MapUtil.getBoolean(context, CommonConstant.FOREIGN_CARD_TRADE_STATUS);
        //到账资金汇总
        SheetHelper.appendLine(summarySheet, getValues(foreignCardTradeStatus ? TO_ACCOUNT_FUNDS_SUMMARY_WITH_FOREIGN_CARD : TO_ACCOUNT_FUNDS_SUMMARY));
        List<String> settleList = Lists.newArrayList(TOTAL_SETTLEMENT_AMOUNT, CHARGE_SETTLEMENT_AMOUNT, LIQUIDATION_SETTLEMENT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT);
        double totalIncomeAmount = MapUtils.getDoubleValue(sumMap, SETTLEMENT_AMOUNT);
        double chargeIncomeAmount = MapUtils.getDoubleValue(sumMap, CHARGE_SETTLEMENT_AMOUNT);
        double storePayIncomeAmount = isIncludeStore ? MapUtils.getDoubleValue(sumMap, STORE_PAY_RECEIVE_AMOUNT) : 0d;
        double liquidationIncomeAmount = MapUtils.getDoubleValue(sumMap, LIQUIDATION_SETTLEMENT_AMOUNT);
        double accountAmount = totalIncomeAmount - chargeIncomeAmount - storePayIncomeAmount - liquidationIncomeAmount;
        List data = Lists.newArrayList(totalIncomeAmount, chargeIncomeAmount, liquidationIncomeAmount, accountAmount);
        if (isIncludeStore) {
            settleList = Lists.newArrayList(TOTAL_SETTLEMENT_AMOUNT, CHARGE_SETTLEMENT_AMOUNT, STORE_PAY_RECEIVE_AMOUNT, LIQUIDATION_SETTLEMENT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT);
            data = Lists.newArrayList(totalIncomeAmount, chargeIncomeAmount, storePayIncomeAmount, liquidationIncomeAmount, accountAmount);
        }
        List settleColumnDesc = new LinkedList(getValueList(settleList));
        int index = settleList.indexOf(DEAL_TO_ACCOUNT_AMOUNT);
        //成交到账金额 列 标红
        SheetHelper.appendLine(summarySheet, settleColumnDesc, Lists.newArrayList(index), IndexedColors.RED.getIndex());
        SheetHelper.appendLine(summarySheet, data, Lists.newArrayList(index), IndexedColors.RED.getIndex());
        //储值充值资金汇总
        double storeAccountAmount = 0;
        if (isIncludeStore) {
            double storeTotalIncomeAmount = MapUtils.getDoubleValue(sumMap, StatementSummary.PREFIX + SETTLEMENT_AMOUNT);
            double cashStoreInIncomeAmount = MapUtils.getDoubleValue(sumMap, STORE_IN_CASH_SETTLEMENT_AMOUNT);
            double otherChargeStoreInIncomeAmount = MapUtils.getDoubleValue(sumMap, STORE_IN_OTHER_CHARGE_SETTLEMENT_AMOUNT);
            double storeLiquidationIncomeAmount = MapUtils.getDoubleValue(sumMap, StatementSummary.PREFIX + LIQUIDATION_SETTLEMENT_AMOUNT);
            storeAccountAmount  = storeTotalIncomeAmount - cashStoreInIncomeAmount - otherChargeStoreInIncomeAmount - storeLiquidationIncomeAmount;

            List<String> storeSettleList = Lists.newArrayList(STORE_IN_TOTAL_SETTLEMENT_AMOUNT, STORE_IN_CASH_SETTLEMENT_AMOUNT, STORE_IN_OTHER_CHARGE_SETTLEMENT_AMOUNT, STORE_IN_LIQUIDATION_SETTLEMENT_AMOUNT,  STORE_IN_DEAL_TO_ACCOUNT_AMOUNT);

            List storeSettleColumnDesc = new LinkedList(getValueList(storeSettleList));
            List storeData = Lists.newArrayList(storeTotalIncomeAmount, cashStoreInIncomeAmount, otherChargeStoreInIncomeAmount, storeLiquidationIncomeAmount, storeAccountAmount);
            int storeIndex = storeSettleList.indexOf(STORE_IN_DEAL_TO_ACCOUNT_AMOUNT);
            //储值充值到账金额 列 标红
            SheetHelper.appendLine(summarySheet, storeSettleColumnDesc, Lists.newArrayList(storeIndex), IndexedColors.RED.getIndex());
            SheetHelper.appendLine(summarySheet, storeData, Lists.newArrayList(storeIndex), IndexedColors.RED.getIndex());
        }

        if (foreignCardTradeStatus) {
            List<String> storeSettleList = Lists.newArrayList(TOTAL_DEAL_TO_ACCOUNT_AMOUNT, DEAL_NO_FOREIGN_CARD_TO_ACCOUNT_AMOUNT, FOREIGN_CARD_TRADE_TO_ACCOUNT_AMOUNT);
            double foreignCardIncomeAmount = MapUtils.getDoubleValue(sumMap, FOREIGN_CARD_SETTLEMENT_AMOUNT);
            double noForeignCardIncomeAmount = accountAmount - foreignCardIncomeAmount;
            List storeData = Lists.newArrayList(accountAmount, noForeignCardIncomeAmount, foreignCardIncomeAmount);

            if (isIncludeStore) {
                storeSettleList.add(STORE_IN_DEAL_TO_ACCOUNT_AMOUNT);
                double totalAccountAmount = accountAmount + storeAccountAmount;
                storeData = Lists.newArrayList(totalAccountAmount, noForeignCardIncomeAmount, foreignCardIncomeAmount, storeAccountAmount);
            }
            List storeSettleColumnDesc = new LinkedList(getValueList(storeSettleList));

            //到账金额 行 标红
            SheetHelper.appendLine(summarySheet, storeSettleColumnDesc, IndexedColors.RED.getIndex());
            SheetHelper.appendLine(summarySheet, storeData, IndexedColors.RED.getIndex());
        } else {
            if (isIncludeStore) {
                List<String> storeSettleList = Lists.newArrayList(TOTAL_DEAL_TO_ACCOUNT_AMOUNT, DEAL_TO_ACCOUNT_AMOUNT, STORE_IN_DEAL_TO_ACCOUNT_AMOUNT);
                double totalAccountAmount = accountAmount + storeAccountAmount;
                List storeData = Lists.newArrayList(totalAccountAmount, accountAmount, storeAccountAmount);
                List storeSettleColumnDesc = new LinkedList(getValueList(storeSettleList));
                storeData = Lists.newArrayList(totalAccountAmount, accountAmount, storeAccountAmount);
                //到账金额 行 标红
                SheetHelper.appendLine(summarySheet, storeSettleColumnDesc, IndexedColors.RED.getIndex());
                SheetHelper.appendLine(summarySheet, storeData, IndexedColors.RED.getIndex());
            }
        }
        SheetHelper.appendLine(summarySheet, Arrays.asList());
    }

    private void appendHbfqStoresSummary(SXSSFSheet summarySheet, List<String> storeIds, Map storeBasicInfoMap, Map storeSummaryMap, Map unionSummaryMap) {
        List columnDesc = new LinkedList();
        List<String> colList = Arrays.asList(TRANSACTION_NO, TRANSACTION_AMOUNT, REFUND_NO, REFUND_AMOUNT,
                MERCHANT_DISCOUNT, WOSAI_DISCOUNT, PAYMENT_TYPE_DISCOUNT, MERCHANT_DISCOUNT_PREPAID_MODE,
                MERCHANT_DISCOUNT_NON_PREPAID_MODE, PAID_AMOUNT, CHARGE, SHARING_AMOUNT, SETTLEMENT_AMOUNT,
                HBFQ_MCH_DISCOUNT_AMOUNT);
        columnDesc.add(getValue(STORE_NAME));
        columnDesc.addAll(getValueList(colList));

        SheetHelper.appendLine(summarySheet, columnDesc);
        Map sumMap = new LinkedHashMap();
        sumMap.put(STORE_NAME, getValue(SUMMARY));
        for (String storeId : storeIds) {
            Map store = storeService.getStore(storeId);
            String storeSn = BeanUtil.getPropString(store, Store.SN);
            int storeType = 1;
            String storeName = BeanUtil.getPropString(store, Store.NAME);
            appendStoreSummary(summarySheet, storeBasicInfoMap, storeSummaryMap, unionSummaryMap, storeSn, storeName, colList, sumMap, storeType, false);
        }

        // 汇总
        List sumParam = new LinkedList();
        sumParam.add(getValue(SUMMARY));
        for (String cro : colList) {
            sumParam.add(CommonUtil.getDoubleValue(sumMap, cro));
        }
        SheetHelper.appendLine(summarySheet, sumParam);
        SheetHelper.appendLine(summarySheet, Arrays.asList());
    }

    public List<Map<String, Object>> retryGetMerchantTransactionList(String merchantId, String merchantUserId, List<String> departmentIds, String storeId, String terminalId, PageInfo pageInfo, Map queryFilter) throws Exception {
        int count = 5;
        Exception exception = null;
        while (count > 0) {
            count--;
            try {
                List<Map<String, Object>> transactionList = transactionService.getMerchantTransactionList(merchantId, merchantUserId, departmentIds, storeId, terminalId, pageInfo, queryFilter);
                TransactionTypeRelatedUtil.rewritePayWayForStore(transactionList);
                return transactionList;
            } catch (Exception e) {
                exception = e;
                try {
                    Thread.sleep(10 * 1000);
                } catch (InterruptedException iex) {
                }
            }
        }
        throw exception;
    }

    public List<String> getStoreIdsByMerchantId(String merchantId){
        List<String> storeIds = new ArrayList<String>();
        PageInfo pageInfo = new PageInfo(1, 9999, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)));
        ListResult stores = storeService.getSimpleStoreListByMerchantIdFromSlaveDb(merchantId, pageInfo);
        if (stores != null && stores.getRecords() != null) {
            stores
                .getRecords()
                .stream()
                .forEach(store -> storeIds.add(MapUtil.getString(store, DaoConstants.ID)));
        }
        return storeIds;
    }

    public List<Map> getStoreListByMerchantUserId(String merchantUserId) {

        GetStoreListByMerchantUserIdReq req = new GetStoreListByMerchantUserIdReq();
        req.setMerchant_user_id(merchantUserId);
        //获取商户用户配置的门店列表
        List<Map> storeListByMerchantUserId = new ArrayList<>();
        try {
            storeListByMerchantUserId = merchantCenterStoreService.getStoreListByMerchantUserId(req).getRecords();
        } catch (Exception exception) {
            log.error("get stores by merchant_user_id {} fail", merchantUserId);
        }

        return storeListByMerchantUserId;
    }



    public List<Map<String, Object>> getSortedStoreIdsByMerchantUserId(List<String> originalStoreIds, Set<String> summaryKeys, List<String> removeSns, List<Map> configStores) {

        List<Map<String, Object>> stores = Lists.newArrayList();

        //判断是否需要走用户配置获取门店列表
        if (CollectionUtil.isNotEmpty(configStores)) {
            configStores.forEach(store -> {
                String storeSn = BeanUtil.getPropString(store, Store.SN);
                //1. storeIds对应的门店在查询出的结果集中，且不在需要移除的集合
                //2. storeIds对应的门店不在查询出的结果集中，但是在返回的用户配置列表中
                if (!summaryKeys.contains(storeSn) || !removeSns.contains(storeSn)) {
                    stores.add(store);
                }
            });
        } else {
            //忽略关闭、禁用状态且数据为0的门店
            originalStoreIds.forEach(id -> {
                Map<String, Object> storeMinimalInfo = businessCommonService.getStoreMinimalInfoById(id);
                String storeSn = BeanUtil.getPropString(storeMinimalInfo, Merchant.SN);
                //1. storeIds对应的门店在查询出的结果集中，且不在需要移除的集合
                //2. storeIds对应的门店不在查询出的结果集中，且门店状态是开启
                if ((summaryKeys.contains(storeSn) && !removeSns.contains(storeSn)) ||
                        (!summaryKeys.contains(storeSn) && Store.STATUS_ENABLED == BeanUtil.getPropInt(storeMinimalInfo, Store.STATUS))) {
                    stores.add(storeMinimalInfo);
                }
            });

            stores.sort((o1, o2) -> org.apache.commons.lang3.StringUtils.compare(BeanUtil.getPropString(o1, Store.SN), BeanUtil.getPropString(o2, Store.SN)));
        }

        return stores;
    }

    public List<String> sortTerminalStoreSns(List<String> terminalStoreSns, List<Map> configMerchantStores) {

        //判断是否需要走用户配置获取门店列表
        if (CollectionUtil.isNotEmpty(configMerchantStores)) {
            List<String> sortedResult = new ArrayList<>();
            for (Map store : configMerchantStores) {
                String storeSn = BeanUtil.getPropString(store, Store.SN);
                if (!terminalStoreSns.contains(storeSn)) {
                    continue;
                }
                sortedResult.add(storeSn);
            }

            terminalStoreSns = sortedResult;
        } else {
            //否则根据门店号的长度进行排序
            Collections.sort(terminalStoreSns, new StatementTransactionSummaryUtil.ByLength());
        }

        return terminalStoreSns;
    }

    public List<String> sortStoreIds(List<String> storeIds, List<Map> configStores) {

        List<String> sortedResult = new ArrayList<>();
        if (CollectionUtil.isEmpty(storeIds)) {
            return  configStores.stream().map(item -> MapUtil.getString(item, DaoConstants.ID)).collect(Collectors.toList());
        }

        for (Map store : configStores) {
            String storeId = BeanUtil.getPropString(store, DaoConstants.ID);
            if (!storeIds.contains(storeId)) {
                continue;
            }
            sortedResult.add(storeId);
        }

        return sortedResult;
    }

    public static List<Map<String, Object>> extractSharingBookList(Map<String, Object> map) {
        //分账支出金额
        int type = MapUtil.getIntValue(map, Transaction.TYPE);
        long clearingAmount = MapUtil.getLongValue(map, Transaction.CLEARING_AMOUNT);
        long tradeFee = MapUtil.getLongValue(map, Transaction.FEE);
        boolean useRound = UpayProfitSharingUtil.useRound(MapUtil.getInteger(map, Transaction.PAYWAY), MapUtil.getInteger(map, Transaction.PROVIDER));
        Map<String, Object> profitSharing = MapUtil.getMap((Map) map.get(Transaction.EXTRA_PARAMS), Transaction.PROFIT_SHARING);
        List<Map<String, Object>> sharingBookMapList = new ArrayList<>();

        // 当前流水计算出来的结算金额已经减去了分账金额
        // 正向支付
        if (TransactionType.PAYMENT_TYPES.contains(type)) {
            sharingBookMapList = UpayProfitSharingUtil.calculateSharingPayAmount(profitSharing, clearingAmount, tradeFee, useRound);
        } else if (TransactionType.REFUND_TYPES.contains(type)) { // 反向退款/取消
            Long payClearingAmount = MapUtil.getLong(profitSharing, ProfitSharing.SHARING_PAY_CLEARING_AMOUNT);
            // payClearingAmount 不为空按新的方式计算
            if (payClearingAmount != null) {
                sharingBookMapList = UpayProfitSharingUtil.calculateSharingRestituteAmount(profitSharing, clearingAmount, tradeFee, payClearingAmount, useRound);
            } else { // payClearingAmount 为空，兼容历史数据
                sharingBookMapList = UpayProfitSharingUtil.calculateSharingPayAmount(profitSharing, clearingAmount, tradeFee, useRound);
            }
        }

        return sharingBookMapList;
    }

    //分账明细转换， 由receiver_id 转换为 sharing_flag, 并重新汇总数据
    public static Map<String, Object> buildSharingProfitDetail(Map<String, Object> map) {

        List<Map<String, Object>> sharingDetail = extractSharingBookList(map);

        Map<String, Object> sharingProfitDetailMap = new HashMap<>();
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(sharingDetail)) {
            Map<String, ImmutablePair<String, String>> sharingProfitMapping = ApolloUtil.getSharingProfitMapping();
            sharingDetail.forEach(item -> {
                String receiverId = MapUtil.getString(item, CommonConstant.SHARING_RECEIVER_ID);
                String sharingFlag = Objects.nonNull(sharingProfitMapping.get(receiverId)) ? sharingProfitMapping.get(receiverId).getLeft() : CommonConstant.SHARING_PROFIT_FLAG;
                if (sharingProfitDetailMap.containsKey(sharingFlag)) {
                    sharingProfitDetailMap.put(sharingFlag, MapUtil.getLongValue(sharingProfitDetailMap, sharingFlag) + MapUtil.getLongValue(item, ProfitSharing.RECEIVER_AMOUNT));
                } else {
                    sharingProfitDetailMap.put(sharingFlag, MapUtil.getLongValue(item, ProfitSharing.RECEIVER_AMOUNT));
                }
            });
        }

        return sharingProfitDetailMap;
    }

    /**
     * 获取接收方中的固定佣金和起步价
     * @param map
     * @return
     */
    public static ImmutablePair<Long, Long> getReceiverBusinessInfo(Map<String, Object> map) {
        List<Map<String, Object>> sharingBookMapList = extractSharingBookList(map);
        Long commission = 0L;
        Long minSharingAmount = 0L;
        if (CollectionUtil.isNotEmpty(sharingBookMapList)) {
            for (Map<String, Object> receiver : sharingBookMapList) {
                    if (receiver.containsKey("commission")) {
                        commission += MapUtil.getLongValue(receiver, "commission");
                    }
                    if (receiver.containsKey(ChargeParamsConfig.CHARGE_PARAMS_MIN_SHARING_AMOUNT)) {
                        minSharingAmount += MapUtil.getLongValue(receiver, ChargeParamsConfig.CHARGE_PARAMS_MIN_SHARING_AMOUNT);
                    }
                }
        }
        return ImmutablePair.of(commission, minSharingAmount);
    }
}
