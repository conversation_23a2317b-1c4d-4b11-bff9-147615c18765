package com.wosai.upay.transaction.util;

import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


/**
 * <AUTHOR>
 */
public class ZipUtil {
    private static final Logger logger = LoggerFactory.getLogger(ZipUtil.class);

    /**
     * 压缩单个文件
     */
    public static void zipFile(String filepath, String zippath) {
        try {
            File file = new File(filepath);
            File zipFile = new File(zippath);
            InputStream input = new FileInputStream(file);
            ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFile));
            zipOut.putNextEntry(new ZipEntry(file.getName()));
            int temp = 0;
            byte data[] = new byte[1024];
            while ((temp = input.read(data)) != -1) {
                zipOut.write(data, 0, temp);
            }
            input.close();
            zipOut.close();
        } catch (Exception e) {
            logger.error("compress file error: " + e.getMessage(), e);
        }
    }

    /**
     * 功能:压缩多个文件成一个zip文件
     *
     * @param srcfiles：源文件列表
     * @param zipfile：压缩后的文件
     */
    public static void zipFiles(List<File> srcfiles, File zipfile) {
        byte[] buf = new byte[1024];
        try {
            //ZipOutputStream类：完成文件或文件夹的压缩
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipfile));
            for (int i = 0; i < srcfiles.size(); i++) {
                File srcfile = srcfiles.get(i);
                FileInputStream in = new FileInputStream(srcfile);
                out.putNextEntry(new ZipEntry(srcfile.getName()));
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
                out.closeEntry();
                in.close();
            }
            out.close();
        } catch (Exception e) {
            logger.error("compress file error: " + e.getMessage(), e);
        }
    }

    public static void compressedFile(List<Pair<byte[], String>> srcList, File zipFile) {
        byte[] buf = new byte[1024];
        try {
            //ZipOutputStream类：完成文件或文件夹的压缩
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipFile));
            for (Pair<byte[], String> pair : srcList) {
                ByteArrayInputStream in = new ByteArrayInputStream(pair.getLeft());
                out.putNextEntry(new ZipEntry(pair.getRight()));
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
                out.closeEntry();
                in.close();
            }
            out.close();
        } catch (Exception e) {
            logger.error("compress file error: " + e.getMessage(), e);
        }
    }

}

