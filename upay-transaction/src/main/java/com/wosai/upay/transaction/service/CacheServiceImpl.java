package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.JacksonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */

@AutoJsonRpcServiceImpl
public class CacheServiceImpl implements CacheService {
    private Logger logger = LoggerFactory.getLogger(CacheServiceImpl.class);
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedisTemplate objectRedisTemplate;



    @Override
    public void deleteCache(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                redisTemplate.delete(pre + key);
            }
        } catch (Exception e) {
            logger.error("删除缓存异常pre[{}],key[{}] ", pre, key, e);
        }
    }

    @Override
    public void deleteCacheLike(String like) {
        try {
            Set<String> keys = redisTemplate.keys(like);
            for (String key : keys) {
                deleteCache("", key);
            }
        } catch (Exception e) {
            logger.error("like[{}] ", like, e);
        }
    }

    @Override
    public boolean hasCache(String pre, String key) {
        try {
            return redisTemplate.hasKey(pre + key);
        } catch (Exception e) {
            logger.error("判断缓存是否存在异常pre[{}],key[{}] ", pre, key, e);
        }
        return false;
    }

    @Override
    public void setCache(String pre, String key, Object value) {
        try {
            if (hasCache(pre, key)) {
                redisTemplate.delete(pre + key);
            }
            redisTemplate.boundValueOps(pre + key).set(JacksonUtil.toJsonString(value));
        } catch (Exception e) {
            logger.error("设置缓存异常pre[{}],key[{}],value[{}] ", pre, key, value, e);
        }
    }

    @Override
    public void setCache(String pre, String key, Object value, TimeUnit timeUnit, long time) {
        try {
            if (hasCache(pre, key)) {
                redisTemplate.delete(pre + key);
            }
            redisTemplate.boundValueOps(pre + key).set(JacksonUtil.toJsonString(value), time, timeUnit);
        } catch (Exception e) {
            logger.error("设置缓存异常pre[{}],key[{}],value[{}] ", pre, key, value, e);
        }
    }

    @Override
    public Map getCacheMap(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                if(str != null){
                    return JacksonUtil.toBean(str, Map.class);
                }
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }




    @Override
    public String getCacheString(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                return redisTemplate.boundValueOps(pre + key).get();
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }

    @Override
    public List getCacheList(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                return JacksonUtil.toBean(str, List.class);
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }

    @Override
    public boolean getCacheBoolean(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                return Boolean.valueOf(str);
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return false;
    }

    @Override
    public Integer getCacheInteger(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                return Integer.valueOf(str);
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }

    @Override
    public Long getCacheLong(String pre, String key) {
        try {
            if (hasCache(pre, key)) {
                String str = redisTemplate.boundValueOps(pre + key).get();
                return Long.valueOf(str);
            }
        } catch (Exception e) {
            logger.error("获取缓存异常pre[{}],key[{}] ", pre, key, e);
        }
        return null;
    }

    @Override
    public Object getObjectRedisCache(String redisKey) {
        try{
            return objectRedisTemplate.boundValueOps(redisKey).get();
        }catch (Exception e){
            logger.error("getHashRedisCache error : " + redisKey );
            return null;
        }
    }

    @Override
    public void setObjectRedisCache(String redisKey, Object value, long timeout, TimeUnit unit) {
        try{
            objectRedisTemplate.boundValueOps(redisKey).set(value);
            objectRedisTemplate.expire(redisKey, timeout,unit);
        }catch (Exception e){
            logger.error("setObjectRedisCache error : " + redisKey);
        }
    }

    public Map getHashRedisCache(String redisKey) {
        try {
            if (!objectRedisTemplate.hasKey(redisKey)) {
                return null;
            }
            return objectRedisTemplate.boundHashOps(redisKey).entries();
        } catch (Exception e) {
            logger.error("getHashRedisCache error : " + redisKey);
            return null;
        }
    }


    public void setHashRedisCache(String redisKey,Map redisValue ,long timeout, final TimeUnit unit) {
        try{
            if(MapUtil.isNotEmpty(redisValue)) {
                objectRedisTemplate.boundHashOps(redisKey).putAll(redisValue);
            }
            objectRedisTemplate.expire(redisKey,timeout,unit);
        }catch (Exception e){
            logger.error("setHashRedisCache error : " + redisKey);
        }
    }


}
