package com.wosai.upay.transaction.service.service.client;

import java.util.Map;
import java.util.concurrent.Future;

import com.wosai.middleware.carrier.CarrierItem;

/**
 * <AUTHOR>
 */
public interface IAsyncMerchantService {


    /**
     * 异步查询商户
     * @param merchantSn
     * @return
     */
    Future<Map> getMerchantBySn(String merchantSn, CarrierItem carrierItem);


    /**
     * 异步查询商户
     * @param merchantId
     * @return
     */
    Future<Map> getMerchant(String merchantId, CarrierItem carrierItem);
}
