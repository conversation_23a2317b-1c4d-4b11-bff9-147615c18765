package com.wosai.upay.transaction.helper;

import com.wosai.upay.transaction.enums.UpayQueryType;

public class DBSelectContext {

    private static final ThreadLocal<DBSelectContext> LOCAL = ThreadLocal.withInitial(()->new DBSelectContext());

    public static void removeContext() {
        LOCAL.remove();
    }

    public static DBSelectContext getContext() {
        return LOCAL.get();
    }


    private Boolean init;
    private UpayQueryType selectDb;

    private boolean finishTimeQuery;
    
    public void initDB(Integer selectDb){
        init = Boolean.TRUE;
        if(selectDb.equals(UpayQueryType.UPAY_SWIPE.getCode())){
            this.selectDb = UpayQueryType.UPAY_SWIPE;
        }else{
            this.selectDb = UpayQueryType.UPAY;
        }
    }

    public boolean getFinishTimeQuery() {
        return finishTimeQuery;
    }

    public void setFinishTimeQuery(boolean finishTimeQuery) {
        this.finishTimeQuery = finishTimeQuery;
    }

    public String getPrefix(){
        String prefix = "";
        if(selectDb == UpayQueryType.UPAY_SWIPE){
            prefix = "银行卡";
        }
        return prefix;
    }

    public Boolean getInit() {
        return init;
    }

    public UpayQueryType getSelectDb() {
        return selectDb;
    }

    public void setSelectDb(UpayQueryType selectDb) {
        this.selectDb = selectDb;
    }
}
