package com.wosai.upay.transaction.util;

import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.concurrent.*;

public class AsyncLoadThreadPool extends ThreadPoolExecutor {


    private static final Field THREAD_LOCAL_FIELD            = ReflectionUtils.findField(Thread.class, "threadLocals");
    private static final Field INHERITABLE_THREAD_LOCAL_FIELD = ReflectionUtils.findField(Thread.class,
            "inheritableThreadLocals");
    static {
        // 强制的声明accessible
        ReflectionUtils.makeAccessible(THREAD_LOCAL_FIELD);
        ReflectionUtils.makeAccessible(INHERITABLE_THREAD_LOCAL_FIELD);
    }

    public AsyncLoadThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    public AsyncLoadThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
    }

    public AsyncLoadThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
    }

    public AsyncLoadThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory, handler);
    }


    public Future<?> submit(AsyncLoadRunnable task) {
        if (task == null) {
            throw new NullPointerException();
        }
        // 使用自定义的Future
        AsyncLoadFuture<Void> ftask = new AsyncLoadFuture(task, null);
        execute(ftask);
        return ftask;
    }

    @Override
    protected void afterExecute(Runnable command, Throwable t) {
        // 在执行结束后清理下ThreadPool的属性，GC处理
        if (command instanceof AsyncLoadFuture) {
            AsyncLoadFuture afuture = (AsyncLoadFuture) command;
            recoverThreadLocal(THREAD_LOCAL_FIELD, afuture.getRunnerThread());
            recoverThreadLocal(INHERITABLE_THREAD_LOCAL_FIELD, afuture.getRunnerThread());

        }

        super.afterExecute(command, t);
    }

    private void recoverThreadLocal(Field field, Thread runner) {
        if (runner == null) {
            return;
        }
        // 清理runner线程的ThreadLocal，为下一个task服务
        ReflectionUtils.setField(field, runner, null);
    }
}
