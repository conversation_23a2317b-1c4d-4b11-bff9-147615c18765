package com.wosai.upay.transaction.service.model.query;

import org.springframework.util.CollectionUtils;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class StatusTypeSubPayWayQuery {

    private List<Integer> statusList;

    private List<Integer> notStatusList;

    private List<Integer> typeList;

    private List<Integer> notTypeList;

    private List<Integer> subPayWayList;

    private boolean isValid = true;

    public List<Integer> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
    }

    public List<Integer> getTypeList() {
        return typeList;
    }

    public void setTypeList(List<Integer> typeList) {
        this.typeList = typeList;
    }

    public List<Integer> getSubPayWayList() {
        return subPayWayList;
    }

    public void setSubPayWayList(List<Integer> subPayWayList) {
        this.subPayWayList = subPayWayList;
    }

    public List<Integer> getNotStatusList() {
        return notStatusList;
    }

    public void setNotStatusList(List<Integer> notStatusList) {
        this.notStatusList = notStatusList;
    }

    public boolean isValid() {
        return isValid;
    }



    public void setValid(boolean valid) {
        isValid = valid;
    }

    public List<Integer> getNotTypeList() {
        return notTypeList;
    }

    public void setNotTypeList(List<Integer> notTypeList) {
        this.notTypeList = notTypeList;
    }

    protected StatusTypeSubPayWayQuery clone() {
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
        if(!CollectionUtils.isEmpty(getStatusList())){
            statusTypeSubPayWayQuery.setStatusList(new ArrayList<>(getStatusList()));
        }
       if(!CollectionUtils.isEmpty(getTypeList())){
            statusTypeSubPayWayQuery.setTypeList(new ArrayList<>(getTypeList()));
       }


        if(!CollectionUtils.isEmpty(getSubPayWayList())){
            statusTypeSubPayWayQuery.setSubPayWayList(new ArrayList<>(getSubPayWayList()));
        }

        if(!CollectionUtils.isEmpty(getNotStatusList())){
            statusTypeSubPayWayQuery.setNotStatusList(new ArrayList<>(getNotStatusList()));
        }

        if(!CollectionUtils.isEmpty(getNotTypeList())){
            statusTypeSubPayWayQuery.setNotTypeList(new ArrayList<>(getNotTypeList()));
        }

        statusTypeSubPayWayQuery.setValid(isValid());

        return statusTypeSubPayWayQuery;

    }
}
