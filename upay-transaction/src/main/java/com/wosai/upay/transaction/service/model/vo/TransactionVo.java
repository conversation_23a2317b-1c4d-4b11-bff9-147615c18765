package com.wosai.upay.transaction.service.model.vo;


import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wosai.common.utils.transaction.Transaction;
import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.core.meta.ProductFlag;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.TransactionConstant;
import com.wosai.upay.transaction.enums.*;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.Payment;
import com.wosai.upay.transaction.model.TAccountRecord;
import com.wosai.upay.transaction.model.TAccountRecordDetail;
import com.wosai.upay.transaction.util.ConfigServiceUtils;
import com.wosai.upay.transaction.util.DateTimeUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TransactionVo {

    private String id;

    private String tsn;

    private String orderSn;

    private String tradeNo;

    private String channelTradeNo;

    private String bankTradeNo;

    private String appAuthShopId;

    private String merchantName;

    /**
     * 向支付通道请求的金额
     */
    private Long effectiveAmount;

    /**
     * 原始金额
     */
    private Long originalAmount;

    /**
     * 消费者实际支付金额
     */
    private Long paidAmount;

    /**
     * 收钱吧或正式商户在支付通道的实际收款金额
     */
    private Long receivedAmount;

    /**
     * 流水交易门店
     */
    private String storeName;


    /**
     * 门店号
     */
    private String storeSn;

    //-----------------------------------------------------

    /**
     * 自己红包优惠
     */
    private long sqbDiscount;

    /**
     * 商户自助发红包优惠
     */
    private long outerDiscount;


    /**
     * 实收金额
     */
    private long actualReceiveAmount;

    /**
     * 交易勾兑状态
     *
     * @see com.wosai.upay.transaction.constant.BlendingStatusConst
     */
    private Integer blendingStatus;

    /**
     * 交易手续费
     */
    private long tradeFee;

    /**
     * 交易费率
     */
    private Double feeRate;

    /**
     * 清算金额
     */
    private Long clearingAmount;

    /**
     * 商户红包优惠金额
     */
    private Long hongBaoWosaiMchTotal;

    /**
     * 商户立减优惠金额
     */
    private Long discountWosaiMchTotal;

    /**
     * 商户立减优惠类型
     */
    private String discountWoSaiMchType;


    /**
     * 收款通道商户免充值优惠
     */
    private Long channelMchFavorableAmount;

    /**
     * 收钱吧优惠金额
     */
    private Long wosaiFavorableAmount;

    /**
     * 收款通道机构优惠
     */
    private Long channelAgentFavorableAmount;


    /**
     * 收款通道商户充值优惠
     */
    private Long channelMchTopUpFavorableAmount;

    private String failText;

    //-----------------------------------------------------

    private String reflect;

    private Integer provider;

    private String payId;

    private String buyerName;

    private String buyerIcon;

    private String payAccount;

    private Boolean liquidationNextDay;

    private String storeId;

    private String merchantId;

    private Integer status;

    private Integer type;

    private String operator;

    private String operatorOrigin;

    private Integer payWay;

    private Integer subPayWay;

    private Long finishTime;

    private Long ctime;

    private String terminalId;

    private String terminalType;


    private String terminalName;

    private String cashDeskId;

    private String cashDeskName;

    private String vendorAppAppid;

    private Boolean isInstalment = false;

    private Long instalmentMerchantCharge = 0L;

    /**
     * 原交易手续费
     */
    private Long tradeFeeOriginal;

    /**
     * 交易费率
     */
    private Double feeRateOriginal;


    /**
     * 终端sn
     */
    private String terminalSn;

    /**
     * 流水类别 {@link TransactionCategory}
     */
    private int category;

    private String tag;

    private Boolean isDepositConsumeRefund;

    private String refernumber;

    private String authNo;

    private String sysTraceNo;

    private String batchBillNo;

    private Long channelFinishTime;

    private Integer sharingFlag;

    private Long sharingAmount;

    /**
     * 喔噻商户优惠
     */
    private long mchFavorableAmount;

    /**
     * 支付优惠明细(包含收钱吧与通道的)
     */
    private List<Map<String,Object>> payments;

    private String productFlag;

    private Map extraParams;

    /**
     * 花呗付息类型
     */
    private Integer hbfqType;

    /**
     * 花呗分期商户贴息金额
     */
    private Long hbfqMchDiscount;
    /**
     * 是否使用额度包
     */
    private boolean useQuota = false;

    /**
     * 通用开关
     */
    private String commonSwitch;

    /**
     * 收银员名称
     */
    private String cashierName;

    /**
     * 微信sub_appid，当payway=3时才有值
     */
    private String subAppid;

    /**
     * 间连收单机构商户号
     */
    private String providerMchId;

    /**
     * 订单信息
     */
    private Map<String, Object> orderInfo;

    //自定义记账方式名称
    private String chargeSource;


    private String tradeApp;

    public String getTradeApp() {
        return tradeApp;
    }

    public void setTradeApp(String tradeApp) {
        this.tradeApp = tradeApp;
    }

    //是否为勾兑完成
    private boolean isFix = false;

    public String getChargeSource() {
        return chargeSource;
    }

    public void setChargeSource(String chargeSource) {
        this.chargeSource = chargeSource;
    }

    public String getSubAppid() {
        return subAppid;
    }

    public void setSubAppid(String subAppid) {
        this.subAppid = subAppid;
    }

    public String getCommonSwitch() {
        return commonSwitch;
    }

    public void setCommonSwitch(String commonSwitch) {
        this.commonSwitch = commonSwitch;
    }

    public Long getHbfqMchDiscount() {
        return hbfqMchDiscount;
    }

    public void setHbfqMchDiscount(Long hbfqMchDiscount) {
        this.hbfqMchDiscount = hbfqMchDiscount;
    }

    public Integer getHbfqType() {
        return hbfqType;
    }

    public void setHbfqType(Integer hbfqType) {
        this.hbfqType = hbfqType;
    }

    public Map getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(Map extraParams) {
        this.extraParams = extraParams;
    }

    public String getProductFlag() {
        return productFlag;
    }

    public void setProductFlag(String productFlag) {
        this.productFlag = productFlag;
    }

    public Long getInstalmentMerchantCharge() {
        return instalmentMerchantCharge;
    }

    public void setInstalmentMerchantCharge(Long instalmentMerchantCharge) {
        this.instalmentMerchantCharge = instalmentMerchantCharge;
    }

    public Boolean getInstalment() {
        return isInstalment;
    }

    public void setInstalment(Boolean instalment) {
        isInstalment = instalment;
    }

    public boolean getIsFix() {
        return isFix;
    }

    public void setFix(boolean isFix) {
        this.isFix = isFix;
    }

    public Long getEffectiveAmount() {
        return effectiveAmount;
    }

    public void setEffectiveAmount(Long effectiveAmount) {
        this.effectiveAmount = effectiveAmount;
    }

    public String getTsn() {
        return tsn;
    }

    public void setTsn(String tsn) {
        this.tsn = tsn;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public Long getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(Long originalAmount) {
        this.originalAmount = originalAmount;
    }

    public Long getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Long paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Long getReceivedAmount() {
        return receivedAmount;
    }

    public void setReceivedAmount(Long receivedAmount) {
        this.receivedAmount = receivedAmount;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorOrigin() {
        return operatorOrigin;
    }

    public void setOperatorOrigin(String operatorOrigin) {
        this.operatorOrigin = operatorOrigin;
    }

    public Integer getPayWay() {
        return payWay;
    }

    public void setPayWay(Integer payWay) {
        this.payWay = payWay;
    }

    public Integer getSubPayWay() {
        return subPayWay;
    }

    public void setSubPayWay(Integer subPayWay) {
        this.subPayWay = subPayWay;
    }

    public Long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Long finishTime) {
        this.finishTime = finishTime;
    }

    public long getSqbDiscount() {
        return sqbDiscount;
    }

    public void setSqbDiscount(long sqbDiscount) {
        this.sqbDiscount = sqbDiscount;
    }

    public long getOuterDiscount() {
        return outerDiscount;
    }

    public void setOuterDiscount(long outerDiscount) {
        this.outerDiscount = outerDiscount;
    }

    public void setActualReceiveAmount(long actualReceiveAmount) {
        this.actualReceiveAmount = actualReceiveAmount;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getCashDeskId() {
        return cashDeskId;
    }

    public void setCashDeskId(String cashDeskId) {
        this.cashDeskId = cashDeskId;
    }

    public String getCashDeskName() {
        return cashDeskName;
    }

    public void setCashDeskName(String cashDeskName) {
        this.cashDeskName = cashDeskName;
    }

    public String getTerminalId() {
        return terminalId;
    }

    public void setTerminalId(String terminalId) {
        this.terminalId = terminalId;
    }

    public Long getActualReceiveAmount() {
        return actualReceiveAmount;
    }

    public long getTradeFee() {
        return tradeFee;
    }

    public void setTradeFee(long tradeFee) {
        this.tradeFee = tradeFee;
    }

    public Integer getBlendingStatus() {
        return blendingStatus;
    }

    public void setBlendingStatus(Integer blendingStatus) {
        this.blendingStatus = blendingStatus;
    }

    public Double getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(Double feeRate) {
        this.feeRate = feeRate;
    }

    public Long getClearingAmount() {
        return clearingAmount;
    }

    public void setClearingAmount(Long clearingAmount) {
        this.clearingAmount = clearingAmount;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getFailText() {
        return failText;
    }

    public void setFailText(String failText) {
        this.failText = failText;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public String getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getChannelTradeNo() {
        return channelTradeNo;
    }

    public void setChannelTradeNo(String channelTradeNo) {
        this.channelTradeNo = channelTradeNo;
    }

    public String getBankTradeNo() {
        return bankTradeNo;
    }

    public void setBankTradeNo(String bankTradeNo) {
        this.bankTradeNo = bankTradeNo;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getBuyerIcon() {
        return buyerIcon;
    }

    public void setBuyerIcon(String buyerIcon) {
        this.buyerIcon = buyerIcon;
    }

    public String getPayAccount() {
        return payAccount;
    }

    public void setPayAccount(String payAccount) {
        this.payAccount = payAccount;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Boolean getLiquidationNextDay() {
        return liquidationNextDay;
    }

    public void setLiquidationNextDay(Boolean liquidationNextDay) {
        this.liquidationNextDay = liquidationNextDay;
    }

    public String getReflect() {
        return reflect;
    }

    public void setReflect(String reflect) {
        this.reflect = reflect;
    }

    public Long getHongBaoWosaiMchTotal() {
        return hongBaoWosaiMchTotal;
    }

    public void setHongBaoWosaiMchTotal(Long hongBaoWosaiMchTotal) {
        this.hongBaoWosaiMchTotal = hongBaoWosaiMchTotal;
    }

    public Long getDiscountWosaiMchTotal() {
        return discountWosaiMchTotal;
    }

    public void setDiscountWosaiMchTotal(Long discountWosaiMchTotal) {
        this.discountWosaiMchTotal = discountWosaiMchTotal;
    }

    public Long getChannelMchFavorableAmount() {
        return channelMchFavorableAmount;
    }

    public void setChannelMchFavorableAmount(Long channelMchFavorableAmount) {
        this.channelMchFavorableAmount = channelMchFavorableAmount;
    }

    public Long getWosaiFavorableAmount() {
        return wosaiFavorableAmount;
    }

    public void setWosaiFavorableAmount(Long wosaiFavorableAmount) {
        this.wosaiFavorableAmount = wosaiFavorableAmount;
    }

    public Long getChannelAgentFavorableAmount() {
        return channelAgentFavorableAmount;
    }

    public void setChannelAgentFavorableAmount(Long channelAgentFavorableAmount) {
        this.channelAgentFavorableAmount = channelAgentFavorableAmount;
    }

    public Integer getProvider() {
        return provider;
    }

    public void setProvider(Integer provider) {
        this.provider = provider;
    }

    public String getAppAuthShopId() {
        return appAuthShopId;
    }

    public void setAppAuthShopId(String appAuthShopId) {
        this.appAuthShopId = appAuthShopId;
    }

    public String getStoreSn() {
        return storeSn;
    }

    public void setStoreSn(String storeSn) {
        this.storeSn = storeSn;
    }

    public TransactionVo() {

    }

    public Long getTradeFeeOriginal() {
        return tradeFeeOriginal;
    }

    public void setTradeFeeOriginal(Long tradeFeeOriginal) {
        this.tradeFeeOriginal = tradeFeeOriginal;
    }

    public Double getFeeRateOriginal() {
        return feeRateOriginal;
    }

    public void setFeeRateOriginal(Double feeRateOriginal) {
        this.feeRateOriginal = feeRateOriginal;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public Long getChannelMchTopUpFavorableAmount() {
        return channelMchTopUpFavorableAmount;
    }

    public void setChannelMchTopUpFavorableAmount(Long channelMchTopUpFavorableAmount) {
        this.channelMchTopUpFavorableAmount = channelMchTopUpFavorableAmount;
    }

    public String getDiscountWoSaiMchType() {
        return discountWoSaiMchType;
    }

    public void setDiscountWoSaiMchType(String discountWoSaiMchType) {
        this.discountWoSaiMchType = discountWoSaiMchType;
    }

    public String getTerminalName() {
        return terminalName;
    }

    public void setTerminalName(String terminalName) {
        this.terminalName = terminalName;
    }

    public String getVendorAppAppid() {
        return vendorAppAppid;
    }

    public void setVendorAppAppid(String vendorAppAppid) {
        this.vendorAppAppid = vendorAppAppid;
    }

    public String getTerminalSn() {
        return terminalSn;
    }

    public void setTerminalSn(String terminalSn) {
        this.terminalSn = terminalSn;
    }

    public String getAuthNo() {
        return authNo;
    }

    public void setAuthNo(String authNo) {
        this.authNo = authNo;
    }

    public String getSysTraceNo() {
        return sysTraceNo;
    }

    public void setSysTraceNo(String sysTraceNo) {
        this.sysTraceNo = sysTraceNo;
    }

    public String getBatchBillNo() {
        return batchBillNo;
    }

    public void setBatchBillNo(String batchBillNo) {
        this.batchBillNo = batchBillNo;
    }

    public Long getChannelFinishTime() {
        return channelFinishTime;
    }

    public void setChannelFinishTime(Long channelFinishTime) {
        this.channelFinishTime = channelFinishTime;
    }

    public Integer getSharingFlag() {
        return sharingFlag;
    }

    public void setSharingFlag(Integer sharingFlag) {
        this.sharingFlag = sharingFlag;
    }

    public Long getSharingAmount() {
        return sharingAmount;
    }

    public void setSharingAmount(Long sharingAmount) {
        this.sharingAmount = sharingAmount;
    }

    public long getMchFavorableAmount() {
        return mchFavorableAmount;
    }

    public void setMchFavorableAmount(long mchFavorableAmount) {
        this.mchFavorableAmount = mchFavorableAmount;
    }

    public void setUseQuota(boolean useQuota) {
        this.useQuota = useQuota;
    }

    public boolean getUseQuota() {
        return useQuota;
    }

    public List<Map<String,Object>> getPayments() {
        return payments;
    }

    public void setPayments(List<Map<String,Object>> payments) {
        this.payments = payments;
    }

    public TAccountRecord buildAccountRecord(ThreadLocal<Map<String, Object>> DETAIL_URL_PARAM, String payWayStr, Map<Integer, Object> paywayNames, Set<Integer> chargePayWayCodes, int offsetHour) {
        TAccountRecord tAccountRecord = new TAccountRecord();
        tAccountRecord.setCtime(getCtime());
        tAccountRecord.setFinishTime(getFinishTime());
        tAccountRecord.setMerchantId(getMerchantId());
        tAccountRecord.setOperatorName(getOperator());
        //支付宝特别处理
        if (getPayWay() == Order.PAYWAY_ALIPAY2) {
            setPayWay(Order.PAYWAY_ALIPAY);
        }
        //此处的payway , 若为其他方式记账，且存在自定义记账方式是 payway = paywayStr-'自定义记账方式名称'
        tAccountRecord.setPayWay(Objects.isNull(payWayStr) ? CommonConstant.DEFAULT_PAY_WAY_STR : payWayStr);

        //流水类别
        tAccountRecord.setCategory(TransactionCategory.getByPayWayAndProductFlag(getPayWay(), productFlag, chargePayWayCodes).getCode());
        //流水标记名称
        String tag = getTagName(paywayNames);
        if (StringUtil.isNotBlank(getChargeSource())) {
            tag = tag + "-" + getChargeSource();
        }
        tAccountRecord.setTag(tag);

        //异常订单标记名称
        tAccountRecord.setAbnormalTag(getAbnormalTagName(offsetHour));

        //实收金额
        tAccountRecord.setActualReceiveAmount(getActualReceiveAmount());
        //商户优惠金额
        tAccountRecord.setOuterDiscount(getOuterDiscount());
        //收钱吧优惠金额
        tAccountRecord.setSqbDiscount(getSqbDiscount());
        // 收钱吧商户优惠
        tAccountRecord.setWoSaiMchFavorableAmount(getMchFavorableAmount());
        tAccountRecord.setStoreSn(getStoreSn());
        // 优惠icon列表（包含product_flag）
        tAccountRecord.setDiscountIconList(Lists.newArrayList(getIcons(tAccountRecord))); // 优惠 icon 小图标列表

        //交易金额,原始订单金额
        tAccountRecord.setTotalFee(getOriginalAmount());

        if (TransactionStatus.SUCCESS.getCode().equals(getStatus())) {
            tAccountRecord.setStatus(TradeStatus.SUCCESS.getCode());
        } else {
            tAccountRecord.setStatus(TradeStatus.FAIL.getCode());
        }
        tAccountRecord.setStoreId(getStoreId());
        tAccountRecord.setSubPayWay(getSubPayWay());
        tAccountRecord.setId(getId());
        tAccountRecord.setTransactionSn(getTsn());
        tAccountRecord.setOrderSn(getOrderSn());
        Map<String, Object> detailUrlParam = DETAIL_URL_PARAM.get();
        String isStoreAccount = MapUtils.getString(detailUrlParam, CommonConstant.STORE_ACCOUNT_FLAG);
        tAccountRecord.setType(getType());
        //门店账本  预授权完成当做收款
        if (getType().equals(TransactionType.DEPOSIT_CONSUME.getCode())) {
            if (Boolean.valueOf(isStoreAccount)) {
                tAccountRecord.setType(TransactionType.PAYMENT.getCode());
            }
        }
        detailUrlParam.remove(CommonConstant.STORE_ACCOUNT_FLAG);
        tAccountRecord.setTerminalId(getTerminalId());
        tAccountRecord.setStoreName(getStoreName());
        tAccountRecord.setPayId(getPayId());
        tAccountRecord.setPayAccount(getPayAccount());
        tAccountRecord.setPaidAmount(Objects.isNull(getPaidAmount()) ? getEffectiveAmount() - (getChannelMchFavorableAmount() + getChannelMchTopUpFavorableAmount() + getChannelAgentFavorableAmount()) : getPaidAmount());
        tAccountRecord.setEffectiveAmount(getEffectiveAmount());
        tAccountRecord.setReflect(getReflect());
        tAccountRecord.setPayId(getPayId());
        tAccountRecord.setAuthNo(getAuthNo());
        tAccountRecord.setPayWayTradeNo(getChannelTradeNo());
        tAccountRecord.setSysTraceNo(getSysTraceNo());
        tAccountRecord.setBatchBillNo(getBatchBillNo());
        tAccountRecord.setInstalment(getInstalment());
        tAccountRecord.setInstalmentMerchantCharge(getInstalmentMerchantCharge());
        detailUrlParam.put(CommonConstant.TRANSACTION_ID, getId());
        detailUrlParam.put(CommonConstant.TRANSACTION_SN, getTsn());
        detailUrlParam.put(DaoConstants.CTIME, getCtime());
        detailUrlParam.put(CommonConstant.MERCHANT_ID_FOR_GROUP_QUERY, getMerchantId());
        tAccountRecord.setDetailUrl(buildDetailUrl(detailUrlParam));
        detailUrlParam.put(CommonConstant.STORE_ACCOUNT_FLAG, isStoreAccount);
        tAccountRecord.setChannelFinishTime(getChannelFinishTime());
        tAccountRecord.setTerminalSn(getTerminalSn());
        tAccountRecord.setTerminalName(getTerminalName());
        tAccountRecord.setRefernumber(getRefernumber());
        tAccountRecord.setIsDepositConsumeRefund(getIsDepositConsumeRefund());
        return tAccountRecord;
    }


    public String getTagName(Map<Integer, Object> payWayNames) {
        if ((Objects.equals(type, TransactionType.PAYMENT.getCode())
                    || Objects.equals(type, TransactionType.STORE_PAY.getCode()))
                && Objects.nonNull(productFlag)
                && productFlag.contains(ProductFlag.COMBINED_PAYMENT.getCode())) {
            return "组合支付收款";
        }

        if (Objects.equals(type, TransactionType.PAYMENT.getCode())
                || Objects.equals(type, TransactionType.DEPOSIT_CONSUME_CANCEL.getCode())
                || Objects.equals(type, TransactionType.DEPOSIT_FREEZE.getCode())
                || Objects.equals(type, TransactionType.DEPOSIT_CONSUME.getCode())) {
            return "收款";
        }
        if (Objects.equals(type, TransactionType.REFUND_REVOKE.getCode())
                || Objects.equals(type, TransactionType.REFUND.getCode())
                || Objects.equals(type, TransactionType.CANCEL.getCode())
                || Objects.equals(type, TransactionType.DEPOSIT_CANCEL.getCode())) {
            return "退款";
        }
        if (Objects.equals(type, TransactionType.STORE_IN.getCode())) {
            return "储值账户充值";
        }
        if (Objects.equals(type, TransactionType.IN_REFUND.getCode())) {
            return "储值账户充值退款";
        }
        if (Objects.equals(type, TransactionType.STORE_PAY.getCode())) {
            return "储值账户核销";
        }
        if (Objects.equals(type, TransactionType.STORE_REFUND.getCode())) {
            return "储值账户核销退款";
        }
        return appendType(MapUtil.getString(payWayNames, payWay, Strings.EMPTY));
    }

    public String getAbnormalTagName(int offsetHour) {

        //根据ctime获取流水创建日期的最后时间戳
        long todayEndMills = DateTimeUtil.getOneDayEnd(ctime - offsetHour * 3600000L) + offsetHour * 3600000L;
        //跨日完成交易， 即创建时间在临界时间点前5分钟内，且完成时间在临界时间点后5分钟内的交易
        if ((ctime > todayEndMills - 5 * 60 * 1000 && ctime <= todayEndMills) && (finishTime > todayEndMills && finishTime <= todayEndMills + 5 * 60 * 1000)) {
            return offsetHour > 0 ? null : "跨日";
        } else if (isFix && ctime <= todayEndMills && finishTime > todayEndMills) {
            return "勾兑";
        }

        return null;
    }

    private String appendType(String prefix) {
        if (Objects.equals(type, TransactionType.CHARGE.getCode())) {
            return prefix.replace("记账", Strings.EMPTY) + "记账";
        }
        if (Objects.equals(type, TransactionType.CHARGE_REFUND.getCode())) {
            return prefix.replace("记账", Strings.EMPTY) + "记账退款";
        }
        if (Objects.equals(type, TransactionType.ORDER_TAKE.getCode())) {
            return prefix + "外卖";
        }
        if (Objects.equals(type, TransactionType.ORDER_TAKE_REFUND.getCode())) {
            return prefix + "外卖退款";
        }
        return "";
    }

    /**
     * 设置 icon 小图标链接
     */
    private Set<String> getIcons(TAccountRecord tAccountRecord) {
        Set<String> iconList = Sets.newLinkedHashSet();

        if (tAccountRecord.getWoSaiMchFavorableAmount() != 0) {
            if (TransactionUtils.getJiuJiuDiscountOriginTypeMap().containsKey(getDiscountWoSaiMchType())) {
                iconList.add(CommonConstant.JIU_JIU_DISCOUNT_ICON);

            } else {
                iconList.add(CommonConstant.OUTER_DISCOUNT_ICON);
            }
        }

        // product_flag
        if (StringUtils.hasLength(getProductFlag())) {
            Set<String> productFlags = Arrays.stream(getProductFlag().split(","))
                    .filter(StringUtils::hasLength)
                    .collect(Collectors.toSet());

            // 电饱饱 icon
            if (productFlags.contains(Transaction.DIAN_BAO_BAO)) {
                iconList.add(CommonConstant.DIAN_BAO_BAO_ICON);
            }
            //券包
            if (productFlags.contains(ProductFlag.COUPON_BUY.getCode())) {
                iconList.add(CommonConstant.COUPON_BUY_ICON);
            }
            //权益卡
            if (productFlags.contains(ProductFlag.MEMBER_CARD_BUY.getCode())) {
                iconList.add(CommonConstant.MEMBER_CARD_BUY_ICON);
            }
            if(productFlags.contains(ProductFlag.FIENESS.getCode())){
                iconList.add(CommonConstant.FITNESS_ICON);
            }
        }

        // 收钱吧优惠 icon
        if (getSqbDiscount() != 0) {
            iconList.add(CommonConstant.SQB_DISCOUNT_ICON);
        }
        // 预授权 icon
        if (getType().equals(TransactionType.DEPOSIT_CONSUME.getCode())) {
            iconList.add(CommonConstant.DEPOSIT_ICON);
        }
        // 花呗分期 icon
        if (getPayWay() == Order.PAYWAY_ALIPAY && getInstalment()) {
            iconList.add(CommonConstant.HB_FQ_ICON);
        }
        // 花呗分期商家贴息 icon
        if (getPayWay() == Order.PAYWAY_ALIPAY 
                && Objects.nonNull(getHbfqType())
                && getHbfqType() == com.wosai.upay.transaction.model.Transaction.FQ_TYPE_SELLER) {
            iconList.add(CommonConstant.HB_FQ_DISCOUNT_ICON);
        }

        return iconList;
    }

    private String buildDetailUrl(Map<String, Object> detailUrlParam) {
        String detailUrl = "";
        int upayQueryType = MapUtils.getIntValue(detailUrlParam, CommonConstant.UPAY_QUERY_TYPE);
        if (UpayQueryType.UPAY.getCode() == upayQueryType) {
            detailUrl = ConfigServiceUtils.getRecordDetailUrl();
        } else if (UpayQueryType.UPAY_SWIPE.getCode() == upayQueryType) {
            detailUrl = ConfigServiceUtils.getSwipeRecordDetailUrl();
        }
        detailUrlParam.remove(null);
        return detailUrl + "?" + Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(detailUrlParam);
    }

    public TAccountRecordDetail buildAccountRecordDetail(Map<Integer, Object> payWayNames, Set<Integer> chargePayWayCodes) {
        TAccountRecordDetail tAccountRecordDetail = new TAccountRecordDetail();
        //商户id
        tAccountRecordDetail.setMerchantId(getMerchantId());
        //商户名称
        tAccountRecordDetail.setMerchantName(getMerchantName());

        tAccountRecordDetail.setAuthNo(getAuthNo());

        tAccountRecordDetail.setSysTraceNo(getSysTraceNo());

        tAccountRecordDetail.setBatchBillNo(getBatchBillNo());
        tAccountRecordDetail.setRefernumber(getRefernumber());

        tAccountRecordDetail.setChannelFinishTime(getChannelFinishTime());

        //门店id
        tAccountRecordDetail.setStoreId(getStoreId());
        //支付源
        tAccountRecordDetail.setPayWay(getPayWay());

        //自定义记账方式名称
        tAccountRecordDetail.setChargeSource(MapUtil.getString(getExtraParams(), TransactionConstant.SQB_CHARGE_SOURCE));
        //流水类别
        tAccountRecordDetail.setCategory(TransactionCategory.getByPayWayAndProductFlag(getPayWay(), productFlag, chargePayWayCodes).getCode());
        //流水标记名称
        tAccountRecordDetail.setTag(getTagName(payWayNames));

        //流水类型
        tAccountRecordDetail.setType(getType());
        //交易时间
        tAccountRecordDetail.setCtime(getCtime());
        //流水单号
        tAccountRecordDetail.setTransactionSn(getTsn());
        //交易手续费
        tAccountRecordDetail.setTradeFee(getTradeFee());
        //流水交易金额
        tAccountRecordDetail.setTotalFee(getOriginalAmount());
        //结算金额
        tAccountRecordDetail.setClearingAmount(getClearingAmount());
        //实收金额
        tAccountRecordDetail.setActualReceiveAmount(getActualReceiveAmount());
        //商户红包优惠金额
        tAccountRecordDetail.setHongBaoWosaiMchTotal(getHongBaoWosaiMchTotal());
        //商户立减优惠金额
        tAccountRecordDetail.setDiscountWosaiMchTotal(getDiscountWosaiMchTotal());
        //商户立减优惠类型
        tAccountRecordDetail.setDiscountWoSaiMchType(getDiscountWoSaiMchType());
        //收款通道商户免充值优惠
        tAccountRecordDetail.setChannelMchFavorableAmount(getChannelMchFavorableAmount());
        //收钱吧优惠金额
        tAccountRecordDetail.setWosaiFavorableAmount(getWosaiFavorableAmount());
        //有收款通道承担的优惠金额
        tAccountRecordDetail.setChannelAgentFavorableAmount(getChannelAgentFavorableAmount());
        //收款通道商户充值优惠
        tAccountRecordDetail.setChannelMchTopUpFavorableAmount(getChannelMchTopUpFavorableAmount());
        //流水交易终端或人员名称
        tAccountRecordDetail.setOperatorName(getOperator());
        //交易手续费
        tAccountRecordDetail.setTradeFee(getTradeFee());
        //实付金额
        tAccountRecordDetail.setPaidAmount(Objects.isNull(getPaidAmount()) ? getEffectiveAmount() - (getChannelMchFavorableAmount() + getChannelAgentFavorableAmount() + getChannelMchTopUpFavorableAmount()) : getPaidAmount());
        //向支付通道请求的金额
        tAccountRecordDetail.setEffectiveAmount(getEffectiveAmount());
        //交易费率
        tAccountRecordDetail.setTradeFeeRate(getFeeRate());
        //流水交易门店
        tAccountRecordDetail.setStoreName(getStoreName());
        //失败原因
        tAccountRecordDetail.setFailText(getFailText());
        //收钱吧单号
        tAccountRecordDetail.setOrderSn(getOrderSn());
        //交易终端类型
        tAccountRecordDetail.setTerminalType(getTerminalType());
        //银行单号
        tAccountRecordDetail.setBankTradeNo(getBankTradeNo());
        //收单机构订单号
        tAccountRecordDetail.setProviderTradeNo(getTradeNo());
        //支付源单号
        tAccountRecordDetail.setPayWayTradeNo(getChannelTradeNo());
        //付款人
        tAccountRecordDetail.setBuyerAccount(getPayAccount());
        // 付款人id（uid）
        tAccountRecordDetail.setBuyerUid(getPayId());
        // 付款人名称
        tAccountRecordDetail.setBuyerName(getBuyerName());
        // 付款人头像
        tAccountRecordDetail.setBuyerIcon(getBuyerIcon());
        //终端id
        tAccountRecordDetail.setTerminalId(getTerminalId());
        //终端sn
        tAccountRecordDetail.setTerminalSn(getTerminalSn());
        //终端名称
        tAccountRecordDetail.setTerminalName(getTerminalName());
        //备注
        tAccountRecordDetail.setTradeMemo(getReflect());
        //是否直连 provider 为空为直连
        tAccountRecordDetail.setDirect(Objects.isNull(getProvider()));
        //不为空为口碑
        tAccountRecordDetail.setAppAuthShopId(getAppAuthShopId());
        tAccountRecordDetail.setStoreSn(getStoreSn());
        //设置分期标志
        tAccountRecordDetail.setInstalment(getInstalment());
        //buyer_uid
        tAccountRecordDetail.setPayId(getPayId());
        //设置分期费用
        tAccountRecordDetail.setInstalmentMerchantCharge(getInstalmentMerchantCharge());
        //商户优惠
        tAccountRecordDetail.setOuterDiscount(getOuterDiscount());
        // tradeApp
        tAccountRecordDetail.setTradeApp(getTradeApp());
        /**
         * woSai商户优惠
         */
        tAccountRecordDetail.setWoSaiMchFavorableAmount(getMchFavorableAmount());
        // 收钱吧商户优惠明细
        if(getMchFavorableAmount() != 0 && getPayments() != null){
            List<Map<String, Object>> list = getPayments().stream().filter(payment -> CommonConstant.WOSAI_MCH_FAVORABLE_PAYMENT_TYPES.contains(MapUtil.getString(payment, Payment.TYPE))).collect(Collectors.toList());
            // original_name 是后来才加的， 如果为空，则根据original_type 映射一下
            for (Map<String, Object> map : list) {
                String originalName = MapUtil.getString(map, Payment.ORIGIN_NAME);
                String originalType = MapUtil.getString(map, Payment.ORIGIN_TYPE);
                if(StringUtil.isBlank(originalName)){
                    originalName = TransactionUtils.getJiuJiuDiscountOriginTypeMap().get(originalType);
                    map.put(Payment.ORIGIN_NAME, originalName != null ? originalName : Transaction.MCH_RED_PACK_DISCOUNT);
                }
            }
            tAccountRecordDetail.setWoSaiMchFavorableAmountList(list);
        }
        /**
         * 图标
         */
        if (tAccountRecordDetail.getWoSaiMchFavorableAmount() != 0) {
            String mchDiscountOriginType = TransactionUtils.getJiuJiuDiscountOriginTypeMap().get(getDiscountWoSaiMchType());
            if (StringUtils.hasText(mchDiscountOriginType)) {
                tAccountRecordDetail.setMchDiscountOriginType(mchDiscountOriginType);
            } else {
                tAccountRecordDetail.setMchDiscountOriginType(Transaction.MCH_RED_PACK_DISCOUNT);
            }
        }

        if (null != getFeeRateOriginal()) {
            //原交易手续费
            tAccountRecordDetail.setTradeFeeOriginal(getTradeFeeOriginal());
            //原交易费率
            tAccountRecordDetail.setFeeRateOriginal(getFeeRateOriginal());
        }

        if (TransactionStatus.SUCCESS.getCode().equals(getStatus())) {
            tAccountRecordDetail.setStatus(TradeStatus.SUCCESS.getCode());
        } else {
            tAccountRecordDetail.setStatus(TradeStatus.FAIL.getCode());

        }

        tAccountRecordDetail.setSharingFlag(getSharingFlag());
        tAccountRecordDetail.setProductFlag(getProductFlag());
        tAccountRecordDetail.setProductIcon(getProductIcon());
        tAccountRecordDetail.setBlendingStatus(getBlendingStatus());
        tAccountRecordDetail.setRefundFlag(MapUtil.getString(getExtraParams(), TransactionConstant.SQB_REFUND_FLAG));
        //是否使用额度包
        tAccountRecordDetail.setUseQuota(getUseQuota());

        //收银台
        tAccountRecordDetail.setCashDeskId(getCashDeskId());
        tAccountRecordDetail.setCashDeskName(getCashDeskName());
        // 收银员
        tAccountRecordDetail.setCashierName(getCashierName());
        // 微信sub_appid
        if (StringUtil.isNotBlank(getSubAppid())) {
            tAccountRecordDetail.setSubAppid(getSubAppid());
        }
        tAccountRecordDetail.setProviderMchId(getProviderMchId());
        tAccountRecordDetail.setIsDepositConsumeRefund(getIsDepositConsumeRefund());
        //订单创建时间
        setOrderInfoDetail(tAccountRecordDetail);
        tAccountRecordDetail.setOperatorOrigin(getOperatorOrigin());
        return tAccountRecordDetail;
    }

    private void setOrderInfoDetail(TAccountRecordDetail tAccountRecordDetail) {
        if (Objects.isNull(orderInfo)) {
            tAccountRecordDetail.setOrderCtime(ctime);
            tAccountRecordDetail.setOrderOriginalAmount(originalAmount);
            tAccountRecordDetail.setOrderEffectiveAmount(effectiveAmount);
        } else {
            tAccountRecordDetail.setOrderCtime(MapUtil.getLong(getOrderInfo(), DaoConstants.CTIME));
            tAccountRecordDetail.setOrderOriginalAmount(MapUtil.getLong(getOrderInfo(), Order.ORIGINAL_TOTAL));
            tAccountRecordDetail.setOrderEffectiveAmount(MapUtil.getLong(getOrderInfo(), Order.EFFECTIVE_TOTAL));
        }
    }
    /**
     * 获取产品/付款类型对应图标
     *
     * @return 图标链接
     */
    private String getProductIcon() {
        String productIcon = null;
        if (Transaction.DIAN_BAO_BAO.equals(getProductFlag())) {
            productIcon = CommonConstant.DIAN_BAO_BAO_ICON;
        }
        return productIcon;
    }

    public String getCashierName() {
        return cashierName;
    }

    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }

    public String getRefernumber() {
        return refernumber;
    }

    public void setRefernumber(String refernumber) {
        this.refernumber = refernumber;
    }

    public Boolean getIsDepositConsumeRefund() {
        return isDepositConsumeRefund;
    }

    public void setIsDepositConsumeRefund(Boolean isDepositConsumeRefund) {
        this.isDepositConsumeRefund = isDepositConsumeRefund;
    }

    public String getProviderMchId() {
        return providerMchId;
    }

    public void setProviderMchId(String providerMchId) {
        this.providerMchId = providerMchId;
    }

    public Map<String, Object> getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(Map<String, Object> orderInfo) {
        this.orderInfo = orderInfo;
    }
}
