package com.wosai.upay.transaction.util;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSErrorCode;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.model.*;
import com.ctrip.framework.apollo.ConfigService;
import com.wosai.middleware.aliyun.oss.DynamicCredentialsProvider;
import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.transaction.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.net.URLDecoder;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Created by kay on 2017/7/7.
 */
@Slf4j
public class OssFileUploader {

    private static Vault vault;

    public static String IMAGE_BUCKET_NAME = "wosai-images";

    private static final String OSS_CLIENT_CONF_END_POINT = "endpoint";

    private static final String DEFAULT_OSS_CLIENT_CONF = "{\"endpoint\":\"http://oss-cn-hangzhou-internal.aliyuncs.com\",\"accessKeyId\":\"\",\"secretAccessKey\":\"\"}";

    private static long EXPIRE = 1000 * 60 * 15;

    public static final String STATIC_BASE_URL = "https://images.wosaimg.com";
    private static final AtomicReference<OSSClient> INSTANCE_OSS_CLIENT = new AtomicReference<>();

    static {
        loadVault();
        onConfigChange();
        ConfigService.getAppConfig().addChangeListener(l -> {
            if (l.isChanged(CommonConstant.OSS_CLIENT_CONF)) {
                onConfigChange();
            }
        });
    }

    private static void loadVault(){
        try {
            vault = Vault.autoload();
        } catch (VaultException e) {
            log.error("load vault error!", e);
        }
    }

    public static void onConfigChange() {
        Map<String, String> ossClientConf = JsonUtil.jsonStrToObject(ConfigService.getAppConfig().getProperty(CommonConstant.OSS_CLIENT_CONF, DEFAULT_OSS_CLIENT_CONF), Map.class);
        if (vault == null) {
            loadVault();
        }
        CredentialsProvider credentialsProvider = new DynamicCredentialsProvider(vault);
        OSSClient ossClient = new OSSClient(ossClientConf.get(OSS_CLIENT_CONF_END_POINT), credentialsProvider, null);
        OSSClient oldOssClient = INSTANCE_OSS_CLIENT.get();
        INSTANCE_OSS_CLIENT.set(ossClient);
        if (oldOssClient != null) {
            oldOssClient.shutdown();
        }
    }

    public static void uploadPrivateStaticsFile(String key, InputStream input, long length) {
        uploadStaticsFile(key, input, length, CannedAccessControlList.Private);
    }

    /**
     * 上传文件
     * @param key
     * @param input
     * @param length
     */
    public static void uploadStaticsFile(String key, InputStream input, long length, CannedAccessControlList controlList) {
//        if (!exists(IMAGE_BUCKET_NAME, key)) {
            upload(IMAGE_BUCKET_NAME, key, input, length, controlList);
//        }
    }

    /**
     * 上传文件
     * @param bucketName
     * @param key
     * @param input
     * @param length
     */
    public static void upload(String bucketName, String key, InputStream input, long length, CannedAccessControlList controlList) {
        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(length);
        INSTANCE_OSS_CLIENT.get().putObject(bucketName, key, input, objectMeta);
        if(controlList != null){
            INSTANCE_OSS_CLIENT.get().setObjectAcl(bucketName, key, controlList);
        }
    }

    /**
     * 获取文件路径
     * @param key
     * @return
     */
    public static String getStaticsFileUrl(String key){
        if(key.contains("?")){
            key = key.substring(0, key.indexOf("?"));
        }
        if(key.startsWith("/")){
            key = key.substring(1);
        }
        if(exists(IMAGE_BUCKET_NAME, key)){
            return bucketUrl(IMAGE_BUCKET_NAME, key);
        }
        return key;
    }

    public static String getStaticsFileUrlExpire(String key,long time){
        if(key.contains("?")){
            key = key.substring(0, key.indexOf("?"));
        }
        if(key.startsWith("/")){
            key = key.substring(1);
        }
        if(exists(IMAGE_BUCKET_NAME, key)){
            return bucketUrl(IMAGE_BUCKET_NAME, key, time);
        }
        return key;
    }

    /**
     * 获取文件路径
     * @param bucketName
     * @param key
     * @return
     */
    public static String bucketUrl(String bucketName, String key, long expire){
        
        ObjectAcl objectAcl = INSTANCE_OSS_CLIENT.get().getObjectAcl(bucketName, key);
        if(ObjectPermission.Private == objectAcl.getPermission()){
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, key, HttpMethod.GET);
            request.setExpiration(new Date(System.currentTimeMillis() + expire));
            URL signedUrl = INSTANCE_OSS_CLIENT.get().generatePresignedUrl(request);
            return signedUrl.toString().replaceFirst("((http|https)+?://[\\s\\S]+?(.com)+?)", "");
        }
        return key;

    }

    /**
     * 获取文件路径
     * @param bucketName
     * @param key
     * @return
     */
    public static String bucketUrl(String bucketName, String key){
        return bucketUrl(bucketName,key,EXPIRE);
    }


    /**
     * 获取文件元信息
     * @param bucketName
     * @param key
     * @return
     */
    public static ObjectMetadata getObjectMetadata(String bucketName, String key) {
        return INSTANCE_OSS_CLIENT.get().getObjectMetadata(bucketName, key);
    }

    /**
     * 判断文件是否存在
     * @param bucketName
     * @param key
     * @return
     */
    public static boolean exists(String bucketName, String key) {
        try {
            getObjectMetadata(bucketName, key);
            return true;
        } catch (OSSException ex) {
            if (OSSErrorCode.NO_SUCH_KEY.equals(ex.getErrorCode()) || OSSErrorCode.NO_SUCH_BUCKET.equals(ex.getErrorCode())) {
                return false;
            } else {
                throw ex;
            }
        }
    }


    /**
     * 下载文件
     * @param bucketName
     * @param key
     * @param filename
     */
    public static void downloadFile(String bucketName, String key, String filename) {
        INSTANCE_OSS_CLIENT.get().getObject(new GetObjectRequest(bucketName, key),
                new File(filename));
    }



    public static void main(String[] args) throws Exception {
        String key = "portal/transaction/32290215-dfde-44e7-be80-ea0de23cc6ca/%E6%8B%89%E5%8D%A1%E6%8B%89syf%E6%B5%8B%E8%AF%95%E5%BA%97_%E4%BD%99%E9%A2%9D%E5%AF%B9%E8%B4%A6%E5%8D%95%E6%B1%87%E6%80%BB_2023-04-16_2023-04-17.xlsx";
//        OssFileUploader.getOSSClient();
//        File file = new File("/Users/<USER>/Downloads/withdrawModelChange100.avsc");
//        FileInputStream input = new FileInputStream(file);
//        OssFileUploader.uploadPrivateStaticsFile(key, input, file.length());
//        String mirror ="/Users/<USER>/Documents/mirror/2020-01-17";
//        String origin ="/Users/<USER>/Documents/origin/2020-01-17";
        String ossUrl = URLDecoder.decode(key);
//        System.out.println(OssFileUploader.getStaticsFileUrl(ossUrl));

        System.out.println(STATIC_BASE_URL + "/" + OssFileUploader.getStaticsFileUrl(ossUrl));

//        downloadFile(IMAGE_BUCKET_NAME, key, mirror + "/000dacb0-62e1-4bde-9049-68790160b150/流水明细2020-01-16_2020-01-17.zip" );
        System.err.println("1");
    }
}

