package com.wosai.upay.transaction.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.Digest;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.CrudUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.transaction.model.StatementSummary;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class StatementCommonUtil {
    public static String getQueryParamSign(String taskIncludes, Map<String, Object> content, String charset) throws MpayException {
        for (String key : new ArrayList<>(content.keySet())) {
            if (content.get(key) == null) {
                content.remove(key);
            }
        }

        long dateEnd = BeanUtil.getPropLong(content, ConstantUtil.KEY_DATE_END);
        // 导数据只允许导出30秒之前数据，防止导出机器时间较支付机器数据时间差较大而造成签名算好了，但是导出的excel中却缺少数据
        long allowExportTime = CrudUtil.getCurTime() - 30 * 1000L;
        if (dateEnd > allowExportTime) {
            content.put(ConstantUtil.KEY_DATE_END, allowExportTime);
        }
        List<String> keys = new ArrayList<>(content.keySet());
        Collections.sort(keys);

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keys.size(); ++i) {
            String key = keys.get(i);
            sb.append(key + "=" + content.get(key) + "&");
        }
        sb.append("includes=" + taskIncludes + "&");
        // -> content be empty
        return getMd5Sign(sb.substring(0, sb.length() - 1), charset);
    }

    public static String getMd5Sign(String content, String charset) throws MpayException {
        String md5 = "";
        try {
            md5 = Digest.md5((content).getBytes(charset));
        } catch (Exception e) {
            throw new MpayException("failed to generate md5 signature.", e);
        }
        return md5;
    }

    public static void appendLine(SXSSFSheet sheet, List values) {
        SheetUtil sheetUtil = new SheetUtil(sheet);
        for (int i = 0; i < values.size(); i++) {
            Object value = values.get(i);
            if (value == null) {
                values.set(i, "");
            } else if (value instanceof Double || value instanceof Float || value instanceof BigDecimal) {
                values.set(i, CommonUtil.formatMoney(value.toString(), 2));
            }
        }
        sheetUtil.appendRow(values);
    }

    public static List<String> removeUndisplayMerchant(Map<String, Object> merchantSummaryMap, BusinssCommonService businessCommonService) {
        List<String> unDisplayMerchantSns = merchantSummaryMap.entrySet().stream().filter(entry -> {
            long tradeAmount = BeanUtil.getPropLong(entry.getValue(), StatementSummary.TRADE_AMOUNT);
            long refundAmount = BeanUtil.getPropLong(entry.getValue(), StatementSummary.REFUND_AMOUNT);
            if (tradeAmount == refundAmount && tradeAmount == 0) {
                try {
                    Map merchant = businessCommonService.getMerchantMinimalInfoBySn(entry.getKey());
                    return BeanUtil.getPropInt(merchant, Merchant.STATUS) != Merchant.STATUS_ENABLED;
                } catch (Exception e) {
                    return false;
                }
            }
            return false;
        }).map(Map.Entry::getKey).collect(Collectors.toList());
        return unDisplayMerchantSns;
    }

    public static List<String> removeUndisplayStore(Map<String, Object> storeSummaryMap, BusinssCommonService businessCommonService) {
        List<String> unDisplayStoreSns = storeSummaryMap.entrySet().stream().filter(entry -> {
            long tradeAmount = BeanUtil.getPropLong(entry.getValue(), StatementSummary.TRADE_AMOUNT);
            long refundAmount = BeanUtil.getPropLong(entry.getValue(), StatementSummary.REFUND_AMOUNT);
            if (tradeAmount == refundAmount && tradeAmount == 0) {
                try {
                    Map store = businessCommonService.getStoreMinimalInfoBySn(entry.getKey());
                    return BeanUtil.getPropInt(store, Store.STATUS) != Store.STATUS_ENABLED;
                } catch (Exception e) {
                    return false;
                }
            }
            return false;
        }).map(Map.Entry::getKey).collect(Collectors.toList());
        return unDisplayStoreSns;
    }

}
