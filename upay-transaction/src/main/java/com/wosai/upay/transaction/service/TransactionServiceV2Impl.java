package com.wosai.upay.transaction.service;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphO;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonException;
import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.DatabaseQueryConstant;
import com.wosai.upay.transaction.constant.QueryFlagConstant;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.repository.DataRepository;
import com.wosai.upay.transaction.service.config.HbaseDispatcherTimeConfig;
import com.wosai.upay.transaction.service.dao.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.service.service.BizHistoryTransactionService;
import com.wosai.upay.transaction.service.service.client.IAsyncMerchantService;
import com.wosai.upay.transaction.service.service.client.IAsyncStoreService;
import com.wosai.upay.transaction.service.service.client.IAsyncTerminalService;
import com.wosai.upay.transaction.service.service.impl.AccountBookBaseServiceImpl;
import com.wosai.upay.transaction.util.*;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.MapUtils;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@AutoJsonRpcServiceImpl
public class TransactionServiceV2Impl implements TransactionServiceV2 {
    public static final Logger logger = LoggerFactory.getLogger(TransactionServiceV2Impl.class);


    @Autowired
    public DataRepository dataRepository;

    @Autowired
    public BusinessService businessService;

    @Autowired
    public IAsyncTerminalService asyncTerminalService;


    @Autowired
    public IAsyncStoreService asyncStoreService;


    @Autowired
    public IAsyncMerchantService asyncMerchantService;

    @Autowired
    private TransactionHBaseDao transactionHbaseDao;
    @Autowired
    HbaseDispatcherTimeConfig hbaseDispatcherTimeConfig;
    @Autowired
    private CacheService cacheService;

    @Autowired
    private  MetaCacheUtil metaCacheUtil;

    @Autowired
    private OdpsUtil odpsUtil;


    @Autowired
    private AccountBookBaseServiceImpl accountBookBaseService;

    @Autowired
    private BizHistoryTransactionService bizHistoryTransactionService;


    private final static List<String> FAIL_STATUS = Arrays.asList(Transaction.STATUS_CONSUME_ERROR + "",Transaction.STATUS_ABORTED + "", Transaction.STATUS_FAIL_PROTOCOL_1 + "", Transaction.STATUS_FAIL_PROTOCOL_2 + "",
            Transaction.STATUS_FAIL_IO_2 + "", Transaction.STATUS_FAIL_PROTOCOL_3 + "", Transaction.STATUS_FAIL_IO_3 + "", Transaction.STATUS_FAIL_ERROR + "",
            Transaction.STATUS_CANCEL_ERROR + "", Transaction.STATUS_REFUND_ERROR + "", Transaction.STATUS_ERROR_RECOVERY + "", Transaction.STATUS_ABORTING + "");

    private final static List<String> IN_PROG_STATUS = Arrays.asList(Transaction.STATUS_CREATED + "", Transaction.STATUS_IN_PROG + "");

    private final List queryFlags = Arrays.asList(
            QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_CASH_DESK_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO
    );


    private final List queryFlagsWithBrand = Arrays.asList(
            QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_CASH_DESK_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO,
            QueryFlagConstant.ORDER_DETAILS_ADD_BRAND_INFO
    );

    @Override
    public ListResult getTransactionList(PageInfo pageInfo, Map<String, Object> queryFilter) {
        PageInfoChecker.check(pageInfo);

        pageInfo = CommonUtil.setPageInfoDefaultValueIfNull(pageInfo);
        if (pageInfo.getPageSize() > DatabaseQueryConstant.MAX_PAGE_SIZE_LIMIT) {
            throw new BizException(BizException.CODE_EXCEED_MAX_PAGE_SIZE, "每页数据过多，禁止查询");
        }
        String payway = BeanUtil.getPropString(queryFilter, "payway");
        String terminalId = BeanUtil.getPropString(queryFilter, "terminal_id");
        List<String> terminalIds = (List) BeanUtil.getProperty(queryFilter, "terminal_ids");
        String terminalSn = BeanUtil.getPropString(queryFilter, "terminal_sn");
        String terminalName = BeanUtil.getPropString(queryFilter, "terminal_name");
        String clientSn = BeanUtil.getPropString(queryFilter, "client_sn");
        String storeId = BeanUtil.getPropString(queryFilter, "store_id");
        String storeSn = BeanUtil.getPropString(queryFilter, "store_sn");
        String storeName = BeanUtil.getPropString(queryFilter, "store_name");
        List<String> storeIds = (List) BeanUtil.getProperty(queryFilter, "store_ids");
        String merchantId = BeanUtil.getPropString(queryFilter, "merchant_id");
        String merchantSn = BeanUtil.getPropString(queryFilter, "merchant_sn");
        String merchantName = BeanUtil.getPropString(queryFilter, "merchant_name");
        String merchantAlias = BeanUtil.getPropString(queryFilter, "merchant_alias");
        List<String> merchantIds = (List) BeanUtil.getProperty(queryFilter, "merchant_ids");
        boolean selectCount = BeanUtil.getPropBoolean(queryFilter, "query_result_select_count", true);
        boolean selectResult = BeanUtil.getPropBoolean(queryFilter, "query_result_select_result", true);
        Long subPayway = CommonUtil.parseLong(BeanUtil.getProperty(queryFilter, "sub_payway"));
        Long status = CommonUtil.parseLong(BeanUtil.getProperty(queryFilter, "status"));
        String transactionSn = BeanUtil.getPropString(queryFilter, "transaction_sn");
        String tradeNo = BeanUtil.getPropString(queryFilter, "trade_no");
        String deviceFingerprint = BeanUtil.getPropString(queryFilter, "device_fingerprint");
        Long minTotalAmount = CommonUtil.parseLong(BeanUtil.getPropString(queryFilter, "min_total_amount"));
        Long maxTotalAmount = CommonUtil.parseLong(BeanUtil.getPropString(queryFilter, "max_total_amount"));
        String openId = BeanUtil.getPropString(queryFilter, "open_id");
        List<String> buyer_uids = (List) BeanUtil.getProperty(queryFilter, "buyer_uids");
        int hbaseTimeout = MapUtil.getIntValue(queryFilter, CommonConstant.HBASE_TIMEOUT);
        int solrTimeout = MapUtil.getIntValue(queryFilter, CommonConstant.SOLR_TIMEOUT);
        boolean includeBrand = false;
        if (queryFilter != null && queryFilter.containsKey("include_brand")) {
            includeBrand = BeanUtil.getPropBoolean(queryFilter, "include_brand");
        }
        List queryFlag = includeBrand ? queryFlagsWithBrand : queryFlags;
        CarrierItem carrierItem = new CarrierItem(TraceContext.traceId());
        Set<String> allMerchantIds = new HashSet<>();

        Set<String> allStoreIds = new HashSet<>();

        Set<String> allTerminalIds = new HashSet<>();

        if (!StringUtil.empty(merchantId)) {
            allMerchantIds.add(merchantId);
        }

        if (!CollectionUtils.isEmpty(merchantIds)) {
            allMerchantIds.addAll(merchantIds);
        }

        Future<Map> merchantSnFuture = null;
        if(StringUtils.hasText(merchantSn)){
            merchantSnFuture = asyncMerchantService.getMerchantBySn(merchantSn, carrierItem);
        }

        Future<List<Map>> merchantNameFuture = null;
        if(StringUtils.hasText(merchantName)){
            merchantNameFuture = businessService.asynGetMerchantInfosByMerchantName(merchantName, carrierItem);
        }

        Future<List<Map>> merchantAliasFuture = null;
        if(StringUtils.hasText(merchantAlias)){
            merchantAliasFuture = businessService.asynGetMerchantInfosByMerchantAlias(merchantAlias, carrierItem);
        }

        if (!Objects.isNull(merchantSnFuture)) {
            Map<String, Object> merchant;
            try {
                merchant = merchantSnFuture.get();
            } catch (Exception e) {
                throw ErrorMessageEnum.GET_MERCHANT_ERROR.getBizException();
            }
            if (Objects.isNull(merchant)) {
                return ListResult.emptyListResult();
            }
            allMerchantIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
        }

        if (!Objects.isNull(merchantNameFuture)) {
            List<Map> merchantInfos;
            try {
                merchantInfos = merchantNameFuture.get();
            }  catch (Exception e) {
                throw ErrorMessageEnum.GET_MERCHANT_ERROR.getBizException();
            }
            if (CollectionUtils.isEmpty(merchantInfos)) {
                return ListResult.emptyListResult();
            }
            for (Map merchant : merchantInfos) {
                allMerchantIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
            }
        }

        if (!Objects.isNull(merchantAliasFuture)) {
            List<Map> merchantInfos;
            try {
                merchantInfos = merchantAliasFuture.get();
            } catch (Exception e) {
                throw ErrorMessageEnum.GET_MERCHANT_ERROR.getBizException();
            }
            if (CollectionUtils.isEmpty(merchantInfos)) {
                return ListResult.emptyListResult();
            }
            for (Map merchant : merchantInfos) {
                allMerchantIds.add(BeanUtil.getPropString(merchant, DaoConstants.ID));
            }
        }

        List<String> storeIdList = Lists.newArrayList();
        if(!CollectionUtils.isEmpty(storeIds)){
            storeIdList.addAll(storeIds);
        }
        if(StringUtils.hasText(storeId)){
            storeIdList.add(storeId);
        }

        List<Future<Map>> storeIdsFuture = null;
        if(!CollectionUtils.isEmpty(storeIdList)){
            storeIdsFuture = new ArrayList<>(storeIdList.size());
            for(String s : storeIdList){
                storeIdsFuture.add(asyncStoreService.getStore(s, carrierItem));
            }
        }
        //全部为空，直接返回
        if(!CollectionUtils.isEmpty(storeIdsFuture)){
            Map store;
            boolean isAllEmpty = true;
            Set<String> tempMerchantIds = new HashSet<>();
            for(Future<Map> f : storeIdsFuture){
                try {
                    store = f.get();
                } catch (Exception e) {
                    throw ErrorMessageEnum.GET_STORE_ERROR.getBizException();
                }
                if (!Objects.isNull(store)) {
                    isAllEmpty = false;
                    if (CollectionUtils.isEmpty(allMerchantIds)) {
                        allStoreIds.add(BeanUtil.getPropString(store, DaoConstants.ID));
                        tempMerchantIds.add(BeanUtil.getPropString(store, Store.MERCHANT_ID));
                    }else if (allMerchantIds.contains(BeanUtil.getPropString(store, Store.MERCHANT_ID))){
                        allStoreIds.add(BeanUtil.getPropString(store, DaoConstants.ID));
                    }
                }
            }
            allMerchantIds.addAll(tempMerchantIds);
            if(isAllEmpty){
                return ListResult.emptyListResult();
            }

        }

        Future<Map> storeSnFuture = null;
        if(StringUtils.hasText(storeSn)){
            storeSnFuture = asyncStoreService.getStoreByStoreSn(storeSn, carrierItem);
        }

        if (!Objects.isNull(storeSnFuture)) {
            Map<String, Object> store;
            try {
                store = storeSnFuture.get();
            } catch (Exception e) {
                throw ErrorMessageEnum.GET_STORE_ERROR.getBizException();
            }
            if (Objects.isNull(store)) {
                return ListResult.emptyListResult();
            }
            if (CollectionUtils.isEmpty(allMerchantIds)) {
                allStoreIds.add(BeanUtil.getPropString(store, DaoConstants.ID));
                allMerchantIds.add(BeanUtil.getPropString(store, Store.MERCHANT_ID));
            }else if (allMerchantIds.contains(BeanUtil.getPropString(store, Store.MERCHANT_ID))){
                allStoreIds.add(BeanUtil.getPropString(store, DaoConstants.ID));
            }
        }

        String searchStoreOrTerminalMerchantId = allMerchantIds.size() == 1 ? allMerchantIds.iterator().next() : null;// 仅merchantId过滤到一个时才这么做

        Future<List<Map>> storeInfosByStoreNameFuture = null;
        if (!StringUtil.empty(storeName)) {
            storeInfosByStoreNameFuture =  businessService.asynGetStoreInfosByStoreName(searchStoreOrTerminalMerchantId, storeName, carrierItem);
        }

        Future<Map> merchantCurrency = null;
        if (!StringUtil.empty(searchStoreOrTerminalMerchantId)) {
            merchantCurrency = asyncMerchantService.getMerchant(searchStoreOrTerminalMerchantId, carrierItem);
        }

        if (!StringUtil.empty(storeName)) {
            List<Map> storeInfos;
            try {
                storeInfos = storeInfosByStoreNameFuture.get();
            } catch (Exception e) {
                throw ErrorMessageEnum.GET_STORE_ERROR.getBizException();
            }
            if (CollectionUtils.isEmpty(storeInfos)) {
                return ListResult.emptyListResult();
            }
            Set<String> tempMerchantIds = new HashSet<>();
            for (Map localStore : storeInfos) {
                if (CollectionUtils.isEmpty(allMerchantIds)) {
                    allStoreIds.add(BeanUtil.getPropString(localStore, DaoConstants.ID));
                    tempMerchantIds.add(BeanUtil.getPropString(localStore, Store.MERCHANT_ID));
                }else if (allMerchantIds.contains(BeanUtil.getPropString(localStore, Store.MERCHANT_ID))){
                    allStoreIds.add(BeanUtil.getPropString(localStore, DaoConstants.ID));
                }
            }
            allMerchantIds.addAll(tempMerchantIds);
        }

        Future<Map> terminalSnFuture = null;
        if(StringUtils.hasText(terminalSn)){
            terminalSnFuture = asyncTerminalService.getTerminalBySn(terminalSn, carrierItem);
        }

        if (!Objects.isNull(terminalSnFuture)) {
            Map<String, Object> terminal;
            try {
                terminal = terminalSnFuture.get();
            } catch (Exception e) {
                throw ErrorMessageEnum.GET_TERMINAL_ERROR.getBizException();
            }
            if(CollectionUtils.isEmpty(terminal)){
                return ListResult.emptyListResult();
            }
            if (CollectionUtils.isEmpty(allMerchantIds)) {
                allTerminalIds.add(BeanUtil.getPropString(terminal, DaoConstants.ID));
                allMerchantIds.add(BeanUtil.getPropString(terminal, Store.MERCHANT_ID));
            }else if (allMerchantIds.contains(BeanUtil.getPropString(terminal, Store.MERCHANT_ID))){
                allTerminalIds.add(BeanUtil.getPropString(terminal, DaoConstants.ID));
            }
        }

        Future<Map> terminalIdFuture = null;
        if(StringUtils.hasText(terminalId)){
            terminalIdFuture = asyncTerminalService.getTerminal(terminalId, carrierItem);
        }

        if (!Objects.isNull(terminalIdFuture)) {
            Map<String, Object> terminal;
            try {
                terminal = terminalIdFuture.get();
            } catch (Exception e) {
                throw ErrorMessageEnum.GET_TERMINAL_ERROR.getBizException();
            }
            if (CollectionUtils.isEmpty(terminal)) {
                return ListResult.emptyListResult();
            }
            if (CollectionUtils.isEmpty(allMerchantIds)) {
                allTerminalIds.add(BeanUtil.getPropString(terminal, DaoConstants.ID));
                allMerchantIds.add(BeanUtil.getPropString(terminal, Store.MERCHANT_ID));
            }else if (allMerchantIds.contains(BeanUtil.getPropString(terminal, Store.MERCHANT_ID))){
                allTerminalIds.add(BeanUtil.getPropString(terminal, DaoConstants.ID));
            }
        }
        if (terminalIds != null) {
            allTerminalIds.addAll(terminalIds);
        }

        Future<List<Map>> terminalInfosByTerminalNameFuture = null;
        if(!StringUtil.empty(terminalName)){
            terminalInfosByTerminalNameFuture = businessService.asynGetTerminalInfosByTerminalName(searchStoreOrTerminalMerchantId, terminalName, carrierItem);
        }
        if (!Objects.isNull(terminalInfosByTerminalNameFuture)) {
            List<Map> terminalInfos;
            try {
                terminalInfos = terminalInfosByTerminalNameFuture.get();
            } catch (Exception e) {
                throw ErrorMessageEnum.GET_TERMINAL_ERROR.getBizException();
            }
            if (CollectionUtils.isEmpty(terminalInfos)) {
                return ListResult.emptyListResult();
            }
            Set<String> tempMerchantIds = new HashSet<>();
            for (Map terminal : terminalInfos) {
                if (CollectionUtils.isEmpty(allMerchantIds)) {
                    allTerminalIds.add(BeanUtil.getPropString(terminal, DaoConstants.ID));
                    tempMerchantIds.add(BeanUtil.getPropString(terminal, Store.MERCHANT_ID));
                }else if (allMerchantIds.contains(BeanUtil.getPropString(terminal, Store.MERCHANT_ID))){
                    allTerminalIds.add(BeanUtil.getPropString(terminal, DaoConstants.ID));
                }
            }
            allMerchantIds.addAll(tempMerchantIds);
        }

        Future<List<Map>> terminalInfosByDeviceFingerprintFuture = null;
        if (!StringUtil.empty(deviceFingerprint)) {
            terminalInfosByDeviceFingerprintFuture =  businessService.asynGetTerminalInfosByDeviceFingerprint(searchStoreOrTerminalMerchantId, deviceFingerprint, carrierItem);
        }

        if (!Objects.isNull(terminalInfosByDeviceFingerprintFuture)) {
            List<Map> terminalInfos;
            try {
                terminalInfos = terminalInfosByDeviceFingerprintFuture.get();
            } catch (Exception e) {
                throw ErrorMessageEnum.GET_TERMINAL_ERROR.getBizException();
            }
            if (CollectionUtils.isEmpty(terminalInfos)) {
                return ListResult.emptyListResult();
            }
            Set<String> tempMerchantIds = new HashSet<>();
            for (Map terminal : terminalInfos) {
                if (CollectionUtils.isEmpty(allMerchantIds)) {
                    allTerminalIds.add(BeanUtil.getPropString(terminal, DaoConstants.ID));
                    tempMerchantIds.add(BeanUtil.getPropString(terminal, Store.MERCHANT_ID));
                }else if (allMerchantIds.contains(BeanUtil.getPropString(terminal, Store.MERCHANT_ID))){
                    allTerminalIds.add(BeanUtil.getPropString(terminal, DaoConstants.ID));
                }
            }
            allMerchantIds.addAll(tempMerchantIds);
        }

        TransactionHBaseQuery transactionHbaseQuery = new TransactionHBaseQuery();

        if (allMerchantIds.size() > 0) {
            transactionHbaseQuery.setMerchantIds(new ArrayList<>(allMerchantIds));
        }


        if (!StringUtil.empty(clientSn)) {
            transactionHbaseQuery.setClientTsns(Lists.newArrayList(clientSn));
        }
        if (allTerminalIds.size() > 0) {
            transactionHbaseQuery.setTerminals(new ArrayList<>(allTerminalIds));
        }
        if (allStoreIds.size() > 0) {
            transactionHbaseQuery.setStoreIds(new ArrayList<>(allStoreIds));
        }
        if (StringUtils.hasText(payway)) {
            List<Integer> payways = Lists.newArrayList(payway.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
            transactionHbaseQuery.setPayWays(payways);
        }
        if (BeanUtil.getProperty(queryFilter, "provider") != null) {
            if ("null".equals(BeanUtil.getProperty(queryFilter, "provider"))) {
                transactionHbaseQuery.setProviderIsNull(true);
            } else {
                String provider = MapUtil.getString(queryFilter, "provider");
                if (StringUtils.hasText(provider)) {
                    List<Integer> providerList = Lists.newArrayList(provider.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
                    transactionHbaseQuery.setProviders(providerList);
                }
            }
        }

        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();

        if (subPayway != null) {
            statusTypeSubPayWayQuery.setSubPayWayList(Lists.newArrayList(subPayway.intValue()));
        }
        if (BeanUtil.getProperty(queryFilter, "type") != null) {
            statusTypeSubPayWayQuery.setTypeList(Lists.newArrayList(CommonUtil
                    .parseLong(BeanUtil.getProperty(queryFilter, "type")).intValue()));
        }
        if (status != null) {
            if (status == 404) {
                statusTypeSubPayWayQuery.setStatusList(CommonUtil.stringToIntList(FAIL_STATUS));
            } else if (status == 0) {
                statusTypeSubPayWayQuery.setStatusList(CommonUtil.stringToIntList(IN_PROG_STATUS));
            } else {
                statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(status.intValue()));
            }
        }
        transactionHbaseQuery.getStatusTypeSubPayWayQueries()
                .put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);

        transactionHbaseQuery.setTransactionSn(transactionSn);
        transactionHbaseQuery.setTradeNo(tradeNo);
        transactionHbaseQuery.setMinOriginalAmount(minTotalAmount);
        transactionHbaseQuery.setMaxOriginalAmount(maxTotalAmount);
        //数据权限管控
        businessService.checkDataPermission(pageInfo, queryFilter);
        transactionHbaseQuery.setStartTime(pageInfo.getDateStart());
        transactionHbaseQuery.setEndTime(pageInfo.getDateEnd());
        transactionHbaseQuery.setSolrTimeout(solrTimeout);
        transactionHbaseQuery.sethBaseTimeout(hbaseTimeout);
        if (StringUtils.hasText(openId)) {
            transactionHbaseQuery.setOpenId(Lists.newArrayList(openId.split(",")));
        }
        if (!CollectionUtils.isEmpty(buyer_uids) && buyer_uids.size() > 0) {
            transactionHbaseQuery.setBuyerUids(buyer_uids);
        }
        long totalCount = 0;
        List list = new ArrayList<>();
        // 进入到流水详情页后，会重新进行一次流水查询，此时参数只有流水号
        if (transactionHbaseQuery.getStartTime() == null 
                && transactionHbaseQuery.getEndTime() == null
                && !StringUtil.empty(transactionSn)) {
            Map<String, Object> transaction = cacheService.getCacheMap(CommonConstant.CACHE_KEY_COLD_ORDER, transactionSn);
            if (transaction != null) {
                transactionHbaseQuery.setStartTime(MapUtil.getLongValue(transaction, DaoConstants.CTIME));
                transactionHbaseQuery.setEndTime(DateTimeUtil.getOneDayEnd(transactionHbaseQuery.getStartTime()));
                transactionHbaseQuery.setMerchantIds(Arrays.asList(MapUtil.getString(transaction, Transaction.MERCHANT_ID)));
            }
        }
        // 历史交易查询校验
        if (transactionHbaseQuery.getStartTime() != null && transactionHbaseQuery.getStartTime() < hbaseDispatcherTimeConfig.getHotIndexStartTime()) {
            if (StringUtil.empty(transactionSn)) {
                throw new BizException(CommonException.CODE_ACCESS_DENIED, String.format("%s年前交易仅支持按收钱吧单号查询", hbaseDispatcherTimeConfig.getHotIndexLimitYear()));
            } else if (CollectionUtils.isEmpty(transactionHbaseQuery.getMerchantIds())) {
                throw new BizException(CommonException.CODE_ACCESS_DENIED, String.format("%s年前交易查询需要输入商户号或门店号或终端号", hbaseDispatcherTimeConfig.getHotIndexLimitYear()));
            } else if (transactionHbaseQuery.getEndTime() == null || DateTimeUtil.getOneDayEnd(transactionHbaseQuery.getStartTime()) < transactionHbaseQuery.getEndTime()) {
                throw new BizException(CommonException.CODE_ACCESS_DENIED, String.format("%s年前交易仅支持按天查询", hbaseDispatcherTimeConfig.getHotIndexLimitYear()));
            } else if (transactionHbaseQuery.getMerchantIds().size() > 1) {
                return new ListResult(0, Collections.emptyList());
            }
            String queryMerchantId = transactionHbaseQuery.getMerchantIds().get(0);
            Map<String, Object> transaction = transactionHbaseDao.queryBySn(queryMerchantId, transactionSn, transactionHbaseQuery.getStartTime(), transactionHbaseQuery.getEndTime());
            if (transaction != null) {
                totalCount = 1;
                list.add(transaction);
                businessService.addBusinessInfoWithQueryFlag(list, queryFlag);
                TransactionUtil.transformReflect(transaction);
                TransactionUtil.jsonFormatOrder(transaction);
                transaction.put(LanguageUtil.PAY_FUND_CHANNEL, TransactionUtil.getPayChannel(transaction));
                transaction.put(LanguageUtil.USE_QUOTA, TransactionUtil.useQuota(transaction));
                TransactionUtil.expandTransactionItemsPayments(transaction, false);
                TransactionUtil.expandTransactionInfo(transaction);
                TransactionUtil.calculateExtendFields(transaction);
                TransactionUtils.expandMchDiscountOriginType(transaction);
                TransactionUtil.expandSharingBooks(transaction, businessService, null);
                TransactionUtil.removeBlobFields(transaction);
                // 支付类交易缓存1小时，退款时使用
                if (MapUtil.getInteger(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT
                            && MapUtil.getInteger(transaction, Transaction.STATUS) == Transaction.STATUS_SUCCESS) {
                    Long ctime = MapUtils.getLong(transaction, DaoConstants.CTIME);
                    String orderId = MapUtils.getString(transaction, Transaction.ORDER_ID);
                    byte[] rowKey = Bytes.add(Bytes.toBytes(queryMerchantId), Bytes.toBytes(ctime), Bytes.toBytes(orderId));
                    cacheService.setCache(CommonConstant.CACHE_KEY_COLD_ORDER, MapUtil.getString(transaction, Transaction.ORDER_SN), MapUtil.hashMap(DaoConstants.ID, Hex.encodeHexString(rowKey),
                            Transaction.MERCHANT_ID, queryMerchantId,
                            DaoConstants.CTIME, ctime
                        ), TimeUnit.HOURS, 1);
                }
            }
        } else {
            //根据备注筛选出tsn
            List<String> memos = new ArrayList<>();
            // 多个备注以空格隔开（暂时不分隔！！！）
            String memo = BeanUtil.getPropString(queryFilter, Transaction.REFLECT);
            if (!StringUtils.isEmpty(memo)) {
                memos = Lists.newArrayList(memo);
            }

            List<String> transactionSnsByMemo;
            if (CollectionUtil.isNotEmpty(memos)) {
                TransactionHBaseQuery transactionMemoHbaseQuery = new TransactionHBaseQuery();
                if (allMerchantIds.size() > 0) {
                    transactionMemoHbaseQuery.setMerchantIds(new ArrayList<>(allMerchantIds));
                }
                transactionMemoHbaseQuery.setTradeMemos(memos);
                //最多查询500条
                transactionMemoHbaseQuery.setLimit(500);
                transactionMemoHbaseQuery.setStartTime(transactionHbaseQuery.getStartTime());
                transactionMemoHbaseQuery.setEndTime(transactionHbaseQuery.getEndTime());
                transactionSnsByMemo = transactionHbaseDao.queryListByMemo(transactionMemoHbaseQuery);

                if (CollectionUtil.isNotEmpty(transactionSnsByMemo) && transactionSnsByMemo.size() > 0) {
                    transactionHbaseQuery.setTransactionSns(transactionSnsByMemo);
                } else {
                    return new ListResult(totalCount, new ArrayList<>());
                }
            }

            if (selectCount) {
                //统计总条数
                totalCount = transactionHbaseDao.count(transactionHbaseQuery);
            }

            if ((!selectCount || totalCount > 0) && selectResult) {
                transactionHbaseQuery.getOrderBys().addAll(pageInfo.getOrderBy());
                int limit = pageInfo.getPageSize();
                int offset = (pageInfo.getPage() - 1) * limit;
                transactionHbaseQuery.setLimit(limit);
                transactionHbaseQuery.setOffset(offset);

                list = transactionHbaseDao.queryList(transactionHbaseQuery);
                formatTransactions(list, queryFlag);
            }
        }
        return new ListResult(totalCount, list);
    }

    @Override
    public ListResult getTransactionListV2(PageInfo pageInfo, Map<String, Object> queryFilter) {
        boolean queryOdps = false;
        ListResult transactionResult = new ListResult();
        List list = new ArrayList<>();
        Exception ex = null;
        try {
            // 查询原有3年内数据
            transactionResult = getTransactionList(pageInfo, queryFilter);
        } catch (Exception e) {
            queryOdps = true;
            ex = e;
        }
        if (!CollectionUtil.isEmpty(transactionResult.getRecords())) {
            return transactionResult;
        }
        String tradeNo = BeanUtil.getPropString(queryFilter, "provider_trade_no", "");
        String channelTradeNo = BeanUtil.getPropString(queryFilter, "channel_trade_no", "");
        List queryFlag = queryFlags;
        long totalCount = 0;
        if (queryOdps || CollectionUtil.isEmpty(transactionResult.getRecords())) {
            totalCount = 1;
            // 如果收款渠道订单号和支付源订单号都为空且存在返回异常则返回原有异常
            if (StringUtils.isEmpty(tradeNo) && StringUtils.isEmpty(channelTradeNo) && queryOdps) {
                throw new RuntimeException(ex.getMessage(), ex);
            } else if (StringUtils.isEmpty(tradeNo) && StringUtils.isEmpty(channelTradeNo)) {
                return new ListResult(0, Collections.emptyList());
            }

            if (!SphO.entry("query-history-transaction-Hbase", EntryType.IN, 1, StringUtils.isEmpty(channelTradeNo) ? tradeNo : channelTradeNo)) {
                throw new RuntimeException("请求过于频繁,请稍后重试");
            }
            try {
                String cacheKey = channelTradeNo + "_" + tradeNo;
                Map<String, Object> cacheTransaction = cacheService.getCacheMap(CommonConstant.CACHE_KEY_TRANSACTION, cacheKey);
                if (cacheTransaction != null) {
                    list.add(cacheTransaction);
                }
                if (!CollectionUtils.isEmpty(list)) {
                    return new ListResult(totalCount, list);
                }

                List<String> dateList = new ArrayList<>();
                if (!StringUtils.isEmpty(tradeNo)) {
                    dateList.addAll(DateTimeUtil.extractDateWithContext(tradeNo));
                }
                if (!StringUtils.isEmpty(channelTradeNo)) {
                    dateList.addAll(DateTimeUtil.extractDateWithContext(channelTradeNo));
                }
                if(CollectionUtils.isEmpty(dateList)){
                    return new ListResult(0, Collections.emptyList());
                }
                List<Map<String, Object>> result = bizHistoryTransactionService.getOdpsResult(dateList, tradeNo, channelTradeNo);
                List<Map<String, Object>> transactions = new ArrayList<>();
                if (!CollectionUtils.isEmpty(result)) {
                    transactions.addAll(result);
                }
                logger.info("查询历史交易数据，结果：{}", result);
                if (!CollectionUtils.isEmpty(transactions)) {
                    List<Map<String, Object>> transactionList = bizHistoryTransactionService.queryTransactionDetails(Arrays.asList(transactions.get(0)), queryFlag, true);
                    list.addAll(transactionList);
                    if (!CollectionUtil.isEmpty(list)) {
                        cacheService.setCache(CommonConstant.CACHE_KEY_TRANSACTION, cacheKey, list.get(0), TimeUnit.MINUTES, 2);
                        return new ListResult(totalCount, list);
                    }
                }
            } catch (Exception e) {
                logger.error("查询历史交易数据异常", e);
                throw new RuntimeException("查询历史交易数据异常");
            } finally {
                SphO.exit(1);
            }
        }
        if (queryOdps && CollectionUtils.isEmpty(list)) {
            throw new RuntimeException(ex.getMessage(), ex);
        }
        return new ListResult(totalCount, list);
    }



    private void formatTransactions(Object transactions, List queryFlags) {
        List<Map<String, Object>> list = new LinkedList();
        Map<String, Object> localReceiverCache = null;
        if (transactions instanceof List) {
            list = (List) transactions;
            localReceiverCache = new HashMap<>();
        } else if (transactions instanceof Map) {
            list.add((Map<String, Object>) transactions);
        } else {
            return;
        }
        businessService.addBusinessInfoWithQueryFlag(list, queryFlags);
        for (Map<String, Object> transaction : list) {
            TransactionUtil.transformReflect(transaction);
            TransactionUtil.jsonFormatOrder(transaction);
            transaction.put(LanguageUtil.PAY_FUND_CHANNEL, TransactionUtil.getPayChannel(transaction));
            transaction.put(LanguageUtil.USE_QUOTA, TransactionUtil.useQuota(transaction));
            TransactionUtil.expandTransactionItemsPayments(transaction, false);
            TransactionUtil.expandTransactionInfo(transaction);
            TransactionUtil.revertEntPayStatus(transaction);
            TransactionUtil.calculateExtendFields(transaction);
            TransactionUtils.expandMchDiscountOriginType(transaction);
            TransactionUtil.expandSharingBooks(transaction, businessService, localReceiverCache);
            TransactionUtil.removeBlobFields(transaction);
            bizHistoryTransactionService.expandTransactionMetaInfo(transaction);
        }
    }

    /**
     * 查询订单
     *
     * @param orderSn
     * @return
     */

    @Override
    public List getTransactionListByOrderSn(String orderSn) {

        TransactionHBaseQuery transactionHbaseQuery = new TransactionHBaseQuery();
        transactionHbaseQuery.setOrderSns(Lists.newArrayList(orderSn));
        transactionHbaseQuery.getOrderBys().add(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC));
        transactionHbaseQuery.setLimit(500);

        List transactions = transactionHbaseDao.queryList(transactionHbaseQuery);
        formatTransactions(transactions, Arrays.asList(
                QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO
        ));
        return transactions;
    }

    @Override
    public List getTransactionListByOrderSn(Map<String, Object> queryFilter) {
        String orderSn = MapUtil.getString(queryFilter, Transaction.ORDER_SN);
        long ctime = MapUtil.getLongValue(queryFilter, DaoConstants.CTIME);
        TransactionHBaseQuery transactionHbaseQuery = new TransactionHBaseQuery();
        transactionHbaseQuery.setOrderSns(Lists.newArrayList(orderSn));
        transactionHbaseQuery.getOrderBys().add(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC));
        transactionHbaseQuery.setLimit(500);
        // 查最近的流水信息
        List<Map<String, Object>> transactions = transactionHbaseDao.queryList(transactionHbaseQuery);
        boolean historyFlag = MapUtils.getBooleanValue(queryFilter, "history_flag", false);
        // 存放结果
        List<Map<String, Object>> transactionList = new ArrayList<>();
        transactionList.addAll(transactions);
        // 如果是历史查询则查询ODPS
        if (historyFlag) {
            // 前一天
            String previousDayStr = DateTimeUtil.previousDayFormat(ctime);
            // 后一天
            String nextDayStr = DateTimeUtil.nextDayFormat(ctime);
            String sql = odpsUtil.getSqlByName("odps/transactionByOrderSn.sql");
            String replace = sql.replace("{pt_start}", previousDayStr);
            replace = replace.replace("{pt_end}", nextDayStr);
            String querySql = replace.replace("{order_sn}", orderSn);
            logger.info("查询历史交易数据，sql：{}", querySql);
            long startTime = System.currentTimeMillis();
            List<Map<String, Object>> result = odpsUtil.getResult(querySql);
            long endTime = System.currentTimeMillis();
            logger.info("查询历史交易数据，耗时：{}ms", endTime - startTime);
            if (!CollectionUtils.isEmpty(result)) {
                transactionList.addAll(result);
            } else {
                return transactionList;
            }
        }
        try {
            transactionList = bizHistoryTransactionService.queryTransactionDetails(transactionList, Arrays.asList(
                    QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                    QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                    QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                    QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
                    QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO
            ), historyFlag);
        } catch (Exception e) {
            logger.error("构造流水详情信息异常", e);
        }
        return transactionList;
    }

    @Override
    public List<Map<String, Object>> getTransactionListByTsns(List<String> merchantIds, List<String> tsns, long startTime, long endTime) {
        TransactionHBaseQuery transactionHbaseQuery = new TransactionHBaseQuery();
        transactionHbaseQuery.setTransactionSns(tsns);
        transactionHbaseQuery.setMerchantIds(merchantIds);
        transactionHbaseQuery.setStartTime(startTime);
        transactionHbaseQuery.setEndTime(endTime);
        transactionHbaseQuery.getOrderBys().add(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC));
        List<Map<String, Object>> transactions = transactionHbaseDao.queryList(transactionHbaseQuery);
        formatTransactions(transactions, Arrays.asList(
                QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO
        ));
        return transactions;
    }

    @Override
    public Map<String, Object> getTransactionDetail(Map<String, Object> queryFilter) {
        boolean includeBrand = false;
        if (queryFilter != null && queryFilter.containsKey("include_brand")) {
            includeBrand = BeanUtil.getPropBoolean(queryFilter, "include_brand");
        }
        List queryFlag = includeBrand ? queryFlagsWithBrand : queryFlags;
        List<Map<String, Object>> transactions = new ArrayList<>();
        String queryMerchantId = MapUtil.getString(queryFilter, Transaction.MERCHANT_ID);
        String transactionId = MapUtil.getString(queryFilter, Transaction.ID);
        long ctime = MapUtil.getLongValue(queryFilter, DaoConstants.CTIME);
        Map<String, Object> transaction = accountBookBaseService.queryOneByTid(queryMerchantId, ctime, transactionId);
        if (transaction == null) {
            return new HashMap<>();
        }
        transactions.add(transaction);
        try {
            formatTransactions(transactions, queryFlag);
        } catch (Exception e) {
            logger.error("构造流水详情信息异常", e);
        }
        return transaction;
    }
}
