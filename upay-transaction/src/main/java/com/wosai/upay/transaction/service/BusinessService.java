package com.wosai.upay.transaction.service;

import com.alibaba.csp.sentinel.util.function.Tuple2;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.*;
import com.wosai.app.backend.api.consts.BasicConsts;
import com.wosai.app.backend.api.service.IStaffService;
import com.wosai.app.common.PageFilter;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.dto.permissions.AnalyzeDataPermissionResponseDTO;
import com.wosai.app.dto.permissions.QueryAnalyzeDataPermissionRequestDTO;
import com.wosai.app.enums.OptionTypeEnum;
import com.wosai.app.enums.PlatformEnum;
import com.wosai.app.service.MerchantUserDataPermissionsService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.cua.brand.business.api.dto.request.QueryBrandsDTO;
import com.wosai.cua.brand.business.api.dto.response.BrandSimpleInfoDTO;
import com.wosai.cua.brand.business.api.facade.BrandFacade;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.middleware.carrier.CarrierItem;
import com.wosai.middleware.hera.toolkit.trace.TraceContext;
import com.wosai.mpay.util.TracingUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.service.IMerchantGrayService;
import com.wosai.service.exception.ServiceException;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.bean.request.ChangeShiftsBatchQueryRequest;
import com.wosai.upay.core.bean.request.ChangeShiftsCashierQueryInfo;
import com.wosai.upay.core.bean.request.ChangeShiftsCashierQueryRequest;
import com.wosai.upay.core.bean.response.ChangeShiftsBatchQueryResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsCashierQueryResponse;
import com.wosai.upay.core.bean.response.ChangeShiftsInfo;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.model.upay.Withdraw;
import com.wosai.upay.core.service.*;
import com.wosai.upay.job.service.T9Service;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.QueryFlagConstant;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;
import com.wosai.upay.transaction.enums.LoadType;
import com.wosai.upay.transaction.enums.PermissionType;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.model.param.AccountRecordParam;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import com.wosai.upay.transaction.service.remote.RemoteReceiverService;
import com.wosai.upay.transaction.service.service.client.IAccountStoreService;
import com.wosai.upay.transaction.service.service.common.CashDeskInfoService;
import com.wosai.upay.transaction.util.*;
import entity.common.PageListResult;
import entity.request.CustomerRelationOriginReq;
import entity.response.CustomerRelationOriginResp;
import facade.ICustomerRelationFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
public class BusinessService {
    @Autowired
    public MerchantService merchantService;
    @Autowired
    public StoreService storeService;
    @Autowired
    public TerminalService terminalService;
    @Autowired
    public CashDeskInfoService cashDeskInfoService;

    @Autowired
    public CashDeskService cashDeskService;
    
    @Autowired
    private IStaffService appStaffService;

    @Autowired
    private IAccountStoreService accountStoreService;


    private static final Interner<String> STRING_CONST_POOL = Interners.newWeakInterner();

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RemoteReceiverService receiverService;
    @Autowired
    MerchantUserServiceV2 merchantUserServiceV2;

    @Autowired
    private IMerchantGrayService merchantGrayService;

    @Autowired
    private ChangeShiftsService changeShiftsService;

    @Resource
    private BusinssCommonService businessCommonService;

    @Resource
    private T9Service t9Service;

    @Resource
    private ICustomerRelationFacade customerRelationFacade;

    @Resource
    private MerchantUserDataPermissionsService merchantUserDataPermissionsService;

    @Autowired
    private BrandFacade brandFacade;

    private static final String RECEIVER_CACHE_KEY = "receiver_cache_key:";

    private static final String MERCHANT_FOREIGN_CARD_TRADE_STATUS_PREFIX = "FC::";

    public static final Logger logger = LoggerFactory.getLogger(BusinessService.class);

    private static final Map<Integer, Object> payWayNameMap = Maps.newHashMap();
    private static final Map<Integer, String> payWayNameEnMap = new HashMap<>();
    private static final Map<Integer, Object> payWayIconMap = new HashMap<>();
    private static final Map<Integer, Object>  payWayGrayIconMap = new HashMap<>();
    private static final Map<Integer, Object>  chargePayWayMap = new HashMap<>();

    /**
     * core-b元数据缓存
     */
    @PostConstruct
    public void initMeta(){
        initPayWayInfo();
        new Timer("meta-flush").scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                try {
                    initPayWayInfo();
                } catch (Exception e) {
                    log.error("flush meta error", e);
                }
            }
        }, new Date(), TimeUnit.MINUTES.toMillis(10));
    }

    private void initPayWayInfo() {

        List<Map<String, Object>> allMetaPayWays = businessCommonService.getAllMetaPayways();
        allMetaPayWays.forEach(item -> {
            int payWayCode = MapUtil.getInteger(item, DaoConstants.ID);
            String payWayName = MapUtil.getString(item, MetaPayway.NAME);
            String payWayNameEn = MapUtil.getString(item, MetaPayway.NAME_EN);
            payWayNameMap.put(payWayCode, payWayName);
            payWayNameEnMap.put(payWayCode, payWayNameEn);

            String payWayIcon = MapUtil.getString(item, MetaPayway.ICON);
            String payWayGrayIcon = MapUtil.getString(item, MetaPayway.GRAY_ICON);
            payWayIconMap.put(payWayCode, payWayIcon);
            payWayGrayIconMap.put(payWayCode, payWayGrayIcon);

            if (MapUtil.getBoolean(item, MetaPayway.IS_CHARGE)) {
                chargePayWayMap.put(payWayCode, payWayName);
            }
        });
    }

    /**
     * 获取元数据
     * @return
     */
    public Map<Integer, Object> getAllPayWayName() {
        return payWayNameMap;
    }

    public Map<Integer, String> getAllPayWayNameEn() {
        return payWayNameEnMap;
    }

    public Map<Integer, Object> getAllPayWayIcon() {
        return payWayIconMap;
    }

    public Map<Integer, Object> getAllPayWayGrayIcon() {
        return payWayGrayIconMap;
    }

    public Map<Integer, Object> getAllChargePayWay() {
        return chargePayWayMap;
    }

    /**
     * 获取商户下用户的基本信息，操作员添加姓名
     *
     * @param merchantIds
     */
    @Async
    public Future<Map<String, Object>> getMerchantOperatorList(Set<String> merchantIds, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        if (CollectionUtils.isEmpty(merchantIds)) {
            return new AsyncResult<Map<String, Object>>(new HashMap(16));
        }
        int startPage = 1;
        int maxSize = 100;
        Map<String, Object> userNameMap = new HashMap<String, Object>(64);
        List<UcMerchantUserSimpleInfo> users;
        //接口校验最大只能传100个
        do {
            PageFilter pageFilter = new PageFilter(startPage++, maxSize);
            QueryMerchantUserReq queryMerchantUserReq = new QueryMerchantUserReq();
            queryMerchantUserReq.setMerchant_ids(new ArrayList<>(merchantIds));
            com.wosai.web.api.ListResult<UcMerchantUserSimpleInfo> listResult = merchantUserServiceV2.pageMerchantUserSimpleInfo(pageFilter, queryMerchantUserReq);
            users = listResult.getRecords();
            if (users != null) {
                for (UcMerchantUserSimpleInfo user : users) {
                    String key = "o:" + user.getUc_user_id();
                    userNameMap.put(key, CollectionUtil.hashMap(
                            "operator_name", user.getName()
                    ));
                }
            }
        } while (users != null && users.size() == maxSize);
        return new AsyncResult<>(userNameMap);
    }

    public List<Map> getStoreInfosByStoreName(String merchantId, String storeName) {
        Map criteria = CollectionUtil.hashMap(
                "store_name", storeName
        );
        if (merchantId != null) {
            criteria.put("merchant_id", merchantId);
        }
        ListResult listResult = storeService.findStores(new PageInfo(1, 50, null, null), criteria);
        if (listResult != null) {
            return listResult.getRecords();
        }
        return null;
    }


    @Async
    public Future<List<Map>> asynGetStoreInfosByStoreName(String merchantId, String storeName, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<List<Map>>(getStoreInfosByStoreName(merchantId, storeName));
    }

    public List<Map> getTerminalInfosByTerminalName(String merchantId, String terminalName) {
        Map criteria = CollectionUtil.hashMap(
                Terminal.NAME, terminalName
        );
        if (merchantId != null) {
            criteria.put("merchant_id", merchantId);
        }
        ListResult listResult = terminalService.findTerminals(new PageInfo(1, 50, null, null), criteria);
        if (listResult != null) {
            return listResult.getRecords();
        }
        return null;
    }


    @Async
    public Future<List<Map>> asynGetTerminalInfosByTerminalName(String merchantId, String terminalName, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<List<Map>>(getTerminalInfosByTerminalName(merchantId, terminalName));
    }


    public List<Map> getTerminalInfosByDeviceFingerprint(String merchantId, String deviceFingerprint) {
        Map criteria = CollectionUtil.hashMap(
                Terminal.DEVICE_FINGERPRINT, deviceFingerprint
        );
        if (merchantId != null) {
            criteria.put("merchant_id", merchantId);
        }
        ListResult listResult = terminalService.findTerminals(new PageInfo(1, 10, null, null), criteria);
        if (listResult != null) {
            return listResult.getRecords();
        }
        return null;
    }

    public Map getTerminalInfoByDeviceFingerprint( String deviceFingerprint) {

        Map result = terminalService.getTerminalByDeviceFingerprint(deviceFingerprint);
        if (MapUtil.isNotEmpty(result)) {
            return result;
        }
        return null;
    }

    @Async
    public Future<List<Map>> asynGetTerminalInfosByDeviceFingerprint(String merchantId, String deviceFingerprint, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<List<Map>>(getTerminalInfosByDeviceFingerprint(merchantId, deviceFingerprint));
    }

    public List<Map> getMerchantInfosByMerchantAlias(String alias) {
        ListResult listResult = merchantService.findMerchants(new PageInfo(1, 10, null, null), CollectionUtil.hashMap(
                "merchant_alias", alias
        ));
        if (listResult != null) {
            return listResult.getRecords();
        }
        return null;
    }

    @Async
    public Future<List<Map>> asynGetMerchantInfosByMerchantAlias(String alias, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<List<Map>>(getMerchantInfosByMerchantAlias(alias));
    }

    public List<Map> getMerchantInfosByMerchantName(String merchantName) {
        ListResult listResult = merchantService.findMerchants(new PageInfo(1, 10, null, null), CollectionUtil.hashMap(
                "merchant_name", merchantName
        ));
        if (listResult != null) {
            return listResult.getRecords();
        }
        return null;
    }


    @Async
    public Future<List<Map>> asynGetMerchantInfosByMerchantName(String merchantName, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        return new AsyncResult<List<Map>>(getMerchantInfosByMerchantName(merchantName));
    }
    
    
    /**
     * 获取终端，门店或者商户信息
     *
     * @param id
     * @param flag terminal/store/merchant
     * @return
     */
    @Async
    public Future<Map<String, Object>> getFromCache(String id, String flag, Cache<String, Map<String, Object>> multiThreadCache, CarrierItem carrierItem) {
        if (StringUtil.empty(id)) {
            return new AsyncResult<Map<String, Object>>(new HashMap(16));
        }
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        Map<String, Object> map = null;

        Cache<String, Map<String, Object>> cached = null;
        if(Objects.isNull(multiThreadCache)){
            throw new IllegalArgumentException("cache can not is null");
        }
        cached = multiThreadCache;
        if ("operator".equals(flag)) {
            String key = "o:" + id;
            map = cached.getIfPresent(key);
            return CollectionUtils.isEmpty(map) ? new AsyncResult<Map<String, Object>>(CollectionUtil.hashMap("operator_name", id)) :new AsyncResult<>(map);
        } else if ("terminal".equals(flag)) {
            String key = "t:" + id;
            map = cached.getIfPresent(key);
            if (!CollectionUtils.isEmpty(map)) {
                return new AsyncResult<>(map);
            }
            synchronized (STRING_CONST_POOL.intern(key)){
                map = cached.getIfPresent(key);
                if (!CollectionUtils.isEmpty(map)) {
                    return new AsyncResult<>(map);
                }
                map = terminalService.getTerminalByTerminalId(id);
                if(null != map){
                    cached.put(key, map);
                }
                return new AsyncResult<>(map);
            }
        } else if ("cashDesk".equals(flag)) {
            String key = "c:" + id;
            map = cached.getIfPresent(key);
            if (!CollectionUtils.isEmpty(map)) {
                return new AsyncResult<>(map);
            }
            synchronized (STRING_CONST_POOL.intern(key)){
                map = cached.getIfPresent(key);
                if (!CollectionUtils.isEmpty(map)) {
                    return new AsyncResult<>(map);
                }
                map = cashDeskService.getSimpleCashDeskById(id);
                if(null != map){
                    cached.put(key, map);
                }
                return new AsyncResult<>(map);
            }
        } else if ("store".equals(flag)) {
            String key = "s:" + id;
            map = cached.getIfPresent(key);
            if (!CollectionUtils.isEmpty(map)) {
                return new AsyncResult<>(map);
            }
            synchronized (STRING_CONST_POOL.intern(key)){
                map = cached.getIfPresent(key);
                if (!CollectionUtils.isEmpty(map)) {
                    return new AsyncResult<>(map);
                }
                map = storeService.getStoreByStoreId(id);
                if(null != map){
                    cached.put(key, map);
                }
                return new AsyncResult<>(map);
            }
        } else if ("merchant".equals(flag)) {
            String key = "m:" + id;
            map = cached.getIfPresent(key);
            if (!CollectionUtils.isEmpty(map)) {
                return new AsyncResult<>(map);
            }
            synchronized (STRING_CONST_POOL.intern(key)){
                map = cached.getIfPresent(key);
                if (!CollectionUtils.isEmpty(map)) {
                    return new AsyncResult<>(map);
                }
                map = merchantService.getMerchantByMerchantId(id);
                if(null != map){
                    cached.put(key, map);
                }
                return new AsyncResult<>(map);
            }

        } else if ("brand".equals(flag)) {
            String key = "b:" + id;
            map = cached.getIfPresent(key);
            if (!CollectionUtils.isEmpty(map)) {
                return new AsyncResult<>(map);
            }
            synchronized (STRING_CONST_POOL.intern(key)) {
                map = cached.getIfPresent(key);
                if (!CollectionUtils.isEmpty(map)) {
                    return new AsyncResult<>(map);
                }
                QueryBrandsDTO queryBrandsDTO = new QueryBrandsDTO();
                queryBrandsDTO.setMerchantId(id);
                //根据商户id查询品牌信息
                List<BrandSimpleInfoDTO> brandInfoListByMerchantId = brandFacade.getBrandInfoListByMerchantId(queryBrandsDTO);
                if (!CollectionUtils.isEmpty(brandInfoListByMerchantId)) {
                    BrandSimpleInfoDTO brandSimpleInfoDTO = brandInfoListByMerchantId.get(0);
                    // 父级品牌id不为空,说明商户关联在子品牌下边
                    if (!StringUtil.empty(brandSimpleInfoDTO.getParentId())) {
                        map = CollectionUtil.hashMap("sub_brand_name", brandSimpleInfoDTO.getName(),
                                "sub_brand_id", brandSimpleInfoDTO.getBrandId(),
                                "sub_brand_sn", brandSimpleInfoDTO.getSn());
                    }
                }
                if (null != map) {
                    cached.put(key, map);
                }
                return new AsyncResult<>(map);
            }
        }
        else {
            return null;
        }

    }

    /**
     * 添加终端，门店，商户，服务商信息
     *
     * @param orderOrTransactions
     */
    public void addBusinessInfo(Object orderOrTransactions) {
        addBusinessInfoWithQueryFlag(orderOrTransactions, Arrays.asList(
                QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO
        ));

    }


    private Cache<String, Map<String, Object>> getBusinessInfoCache() {
        return BUSINESS_INFO_CACHE.get();
    }


    /**
     * 用于记录翻译业务operator，已经查过的merchantId.
     */
    private static final ThreadLocal<Cache<String, Map<String, Object>>> BUSINESS_INFO_CACHE = new ThreadLocal<Cache<String, Map<String, Object>>>() {
        public Cache<String, Map<String, Object>> initialValue() {
            return CacheBuilder.newBuilder()
                    .maximumSize(1000).build();
        }
    };



    public void addBusinessInfoWithQueryFlag(Object orderOrTransactions, List queryFlag) {
        List<Map<String, Object>> orderList = new ArrayList();
        if (orderOrTransactions instanceof Map) {
            orderList.add((Map<String, Object>) orderOrTransactions);
        } else if (orderOrTransactions instanceof List) {
            orderList = (List) orderOrTransactions;
        }
        boolean queryOperator = queryFlag.contains(QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO);
        boolean queryCashDesk = queryFlag.contains(QueryFlagConstant.ORDER_DETAILS_ADD_CASH_DESK_INFO);
        boolean queryTerminal = queryFlag.contains(QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO);
        boolean queryStore = queryFlag.contains(QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO);
        boolean queryMerchant = queryFlag.contains(QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO);
        boolean queryCashier = queryFlag.contains(QueryFlagConstant.ORDER_DETAILS_ADD_CASHIER_INFO);
        boolean queryBrand = queryFlag.contains(QueryFlagConstant.ORDER_DETAILS_ADD_BRAND_INFO);
        CarrierItem carrierItem = new CarrierItem(TraceContext.traceId());
        Cache<String, Map<String, Object>> cached = getBusinessInfoCache();
        Set<String> merchantIds = new HashSet<>();
        for (Map<String, Object> order : orderList) {
            String merchantId = MapUtil.getString(order, Order.MERCHANT_ID, "");
            if(StringUtils.hasLength(merchantId) && CollectionUtils.isEmpty(cached.getIfPresent(merchantId))){
                merchantIds.add(merchantId);
            }
        }
        BusinessService businessService = ApplicationContextUtil.getBean(BusinessService.class);
        Future<Map<String, Object>> operatorListFuture = null;
        if (queryOperator && merchantIds.size() != 0) {
             operatorListFuture = businessService.getMerchantOperatorList(merchantIds, carrierItem);
        }
        List<Future<Map<String, Object>>> orders = Lists.newArrayList();

        for(Map<String, Object> order : orderList){
            Future<Map<String, Object>> terminalFuture = null;
            if (queryTerminal) {
                terminalFuture = businessService.getFromCache(MapUtil.getString(order, Order.TERMINAL_ID, ""), "terminal", cached, carrierItem);
            }
            Future<Map<String, Object>> storeFuture = null;
            if (queryStore) {
                storeFuture = businessService.getFromCache(MapUtil.getString(order, Order.STORE_ID, ""), "store", cached, carrierItem);
            }
            Future<Map<String, Object>> merchantFuture = null;
            if (queryMerchant) {
                merchantFuture = businessService.getFromCache(MapUtil.getString(order, Order.MERCHANT_ID, ""), "merchant", cached, carrierItem);
            }
            Future<Map<String, Object>> cashDeskFuture = null;
            String commonSwitch;
            if (order.containsKey(Transaction.CONFIG_SNAPSHOT)) {
                Map<String, Object> configSnapShot = MapUtil.getMap(order, Transaction.CONFIG_SNAPSHOT, new HashMap());
                commonSwitch = MapUtil.getString(configSnapShot, TransactionParam.COMMON_SWITCH);
            } else {
                commonSwitch = MapUtil.getString(order, TransactionParam.COMMON_SWITCH);
            }
            boolean isCashDesk = TransactionUtil.isCommonSwitchOpen(commonSwitch, TransactionParam.TYPE_COMMON_SWITCH_CASH_DESK);
            if (queryCashDesk && isCashDesk) {
                String sn = (String)(order.containsKey(Transaction.TSN) ? order.get(Transaction.TSN) : order.get(Order.SN));
                String cashDeskId = cashDeskInfoService.getCashDeskIdByTsn(sn);
                cashDeskFuture = businessService.getFromCache(cashDeskId, "cashDesk", cached, carrierItem);
            }
            Future<Map<String, Object>> brandFuture = null;
            if (queryBrand) {
                brandFuture = businessService.getFromCache(MapUtil.getString(order, Order.MERCHANT_ID, ""), "brand", cached, carrierItem);
            }
            orders.add(businessService.buildOrder(queryTerminal, queryCashDesk, queryStore, queryMerchant, queryCashier, queryBrand, order, terminalFuture, cashDeskFuture, storeFuture, merchantFuture, brandFuture, carrierItem));
        }

        if (!Objects.isNull(operatorListFuture)) {
            Map operatorList = null;
            try {
                operatorList = operatorListFuture.get();
            } catch (Exception e) {
                log.warn("get operator error", e);
                throw ErrorMessageEnum.GET_OPERATOR_ERROR.getBizException();
            }
            if(!CollectionUtils.isEmpty(operatorList)){
                cached.putAll(operatorList);
            }
        }


        Map<String, Object> orderMap = null;
        for(Future<Map<String, Object>> orderFuture : orders) {
            try {
                orderMap = orderFuture.get();
            } catch (Exception e) {
                logger.error("构建订单信息出错", e);
                throw ErrorMessageEnum.BULID_ORDER_ERROR.getBizException();
            }
        }

        if (queryOperator) {
            for(Map<String, Object> order : orderList) {
                String operatorId = MapUtil.getString(order, Order.OPERATOR, "");
                String operatorName = getOperatorNameFromCache(BeanUtil.getPropString(orderMap, "merchant_id"), operatorId);
                order.put("operator_name", operatorName);
            }
        }




    }

    @Async
    public Future<Map<String, Object>> buildOrder(boolean queryTerminal, boolean queryCashDesk, boolean queryStore, boolean queryMerchant, boolean queryCashier, boolean queryBrand,
            Map<String, Object> order, Future<Map<String, Object>> terminalFuture, Future<Map<String, Object>> cashDeskFuture,
            Future<Map<String, Object>> storeFuture, Future<Map<String, Object>> merchantFuture,Future<Map<String, Object>> brandFuture, CarrierItem carrierItem) {
        TracingUtil.storeThreadLocalTraceInfo(carrierItem);
        if (queryTerminal) {
            Map<String, Object> terminal = null;
            try {

                terminal = terminalFuture.get();
            } catch (Exception e) {
                logger.error("获取终端信息处出错", e);
                throw ErrorMessageEnum.GET_TERMINAL_ERROR.getBizException();
            }
            order.put("terminal_sn", BeanUtil.getPropString(terminal, Terminal.SN));
            order.put("terminal_name", BeanUtil.getPropString(terminal, Terminal.NAME));
            order.put("terminal_type", BeanUtil.getPropString(terminal, Terminal.TYPE));
            order.put("terminal_device_fingerprint", BeanUtil.getPropString(terminal, Terminal.DEVICE_FINGERPRINT));
            order.put("terminal_client_sn", BeanUtil.getPropString(terminal, Terminal.CLIENT_SN));
        }
        if (queryCashDesk && cashDeskFuture != null) {
            Map<String, Object> cashDesk = null;
            try {
                cashDesk = cashDeskFuture.get();
            } catch (Exception e) {
                logger.error("获取收银台信息出错", e);
                throw ErrorMessageEnum.GET_CASH_DESK_ERROR.getBizException();
            }
            order.put("cash_desk_id", BeanUtil.getPropString(cashDesk, DaoConstants.ID));
            order.put("cash_desk_name", BeanUtil.getPropString(cashDesk, CashDesk.NAME));
        }
        if(queryStore){
            Map<String, Object> store = null;
            try {
                store = storeFuture.get();
            } catch (Exception e) {
                logger.error("获取门店信息处出错", e);
                throw ErrorMessageEnum.GET_STORE_ERROR.getBizException();
            }
            order.put("store_sn", BeanUtil.getPropString(store, Store.SN));
            order.put("store_name", BeanUtil.getPropString(store, Store.NAME));
            order.put("store_client_sn", BeanUtil.getPropString(store, Store.CLIENT_SN));
        }
        if(queryMerchant){
            Map<String, Object> merchant = null;
            try {
                merchant = merchantFuture.get();
            } catch (Exception e) {
                logger.error("获取商户信息处出错", e);
                throw ErrorMessageEnum.GET_MERCHANT_ERROR.getBizException();
            }
            order.put("merchant_sn", BeanUtil.getPropString(merchant, Store.SN));
            order.put("merchant_name", BeanUtil.getPropString(merchant, Store.NAME));
            order.put("merchant_currency", BeanUtil.getPropString(merchant, Merchant.CURRENCY,"CNY"));
        }
        if (queryCashier) {
            setCashierName(order);
        }
        if (queryBrand) {
            Map<String, Object> brand = null;
            try {
                brand = brandFuture.get();
            } catch (Exception e) {
                logger.error("获取品牌信息处出错", e);
                throw ErrorMessageEnum.GET_MERCHANT_ERROR.getBizException();
            }
            order.put("sub_brand_name", BeanUtil.getPropString(brand, "sub_brand_name"));
            order.put("sub_brand_sn", BeanUtil.getPropString(brand, "sub_brand_sn"));
            order.put("sub_brand_id", BeanUtil.getPropString(brand, "sub_brand_id"));
        }
        return new AsyncResult<Map<String, Object>>(order);
    }

    public void addMerchantInfoForWithdraws(List<Map<String, Object>> withdrawList) {
        CarrierItem carrierItem = new CarrierItem(TraceContext.traceId());
        Cache<String, Map<String, Object>> cached = getBusinessInfoCache();
        for(Map<String, Object> withdraw : withdrawList){
            Future<Map<String, Object>> merchantFuture = getFromCache(MapUtil.getString(withdraw, Withdraw.MERCHANT_ID, ""), "merchant", cached, carrierItem);
            Map<String, Object> merchant;
            try {
                merchant = merchantFuture.get();
            } catch (Exception e) {
                logger.error("获取商户信息处出错", e);
                throw ErrorMessageEnum.GET_MERCHANT_ERROR.getBizException();
            }
            withdraw.put("merchant_sn", MapUtil.getString(merchant, Merchant.SN));
            withdraw.put("merchant_name", MapUtil.getString(merchant, Merchant.NAME));
        }
    }

    private String getOperatorNameFromCache(String merchantId, String operatorId) {
		if (StringUtil.empty(operatorId) 
				|| !operatorId.matches("(\\w{8}(-\\w{4}){3}-\\w{12}?)")) {
            return null;
        }

        Cache<String, Map<String, Object>> cached = getBusinessInfoCache();
		String key = "o:" + merchantId;
        if (CollectionUtils.isEmpty(cached.getIfPresent(key))) {
            QueryMerchantUserReq req = new QueryMerchantUserReq()
                    .setMerchant_id(merchantId)
                    .setRoles(Arrays.asList(BasicConsts.SUPER_ADMIN, BasicConsts.ADMIN, BasicConsts.CASHIER));
            List<UcMerchantUserInfo> staffs = merchantUserServiceV2.getMerchantUser(req);
            Map<String, Object> resultMap = new HashMap(16);
            if(WosaiCollectionUtils.isNotEmpty(staffs)) {
                staffs.forEach(o -> resultMap.put(o.getUcUserInfo().getUc_user_id(),o.getUcUserInfo().getNickname()));
                cached.put(key, resultMap);
            }
        }
        return BeanUtil.getPropString(cached.getIfPresent(key), operatorId , "店员");
	}

    public void getStoreStaff(TransactionQuery query, FutureTask<Map<String, String>> storeStaffTask, Map<String, String> storeStaffMap) {
        if (query.getLoadTypes().contains(LoadType.STORE_STAFF)) {
            try {
                storeStaffMap.putAll(storeStaffTask.get());
            } catch (Exception e) {
                log.error("findStoreStaffByMerchant error", e);
                throw new RuntimeException(e);
            }
        }
    }


    public FutureTask<Map<String, String>> getStoreStaffFutureTask(TransactionQuery query) {
        //预热数据
        FutureTask<Map<String, String>> storeStaffTask = null;
        CarrierItem carrierItem = new CarrierItem(TraceContext.traceId());
        if (query.getLoadTypes().contains(LoadType.STORE_STAFF)) {
            storeStaffTask = new FutureTask<>(new BaseAsyncTask<Map<String, String>>() {
                @Override
                public Map<String, String> run() {
                    TracingUtil.storeThreadLocalTraceInfo(carrierItem);
                    if (StringUtils.isEmpty(query.getMerchantId())){
                        return accountStoreService.findStoreStaffByMerchantIds(Sets.newHashSet(query.getMerchantIdList()));
                    }
                    return accountStoreService.findStoreStaffByMerchant(query.getMerchantId());
                }
            });
            ExecutorServiceSupport.getDefaultExecutorService().submit(storeStaffTask);
        }
        return storeStaffTask;
    }

    /**
     * 获取分账接收方属性
     * @param receiverIds
     * @return
     */
    public Map<String, Map<String, String>> getReceiverInfo(List<String> receiverIds){
        return getReceiverInfo(receiverIds, null);
    }

    /**
     * 获取分账接收方属性
     * @param receiverIds
     * @return
     */
    public Map<String, Map<String, String>> getReceiverInfo(List<String> receiverIds, Map<String, Object> localReceiverCache){
        List<Object> cache = null;
        if (MapUtil.isNotEmpty(localReceiverCache)) {
            cache = new ArrayList();
            for (String receiverId : receiverIds) {
                cache.add(localReceiverCache.get(receiverId));
            }
        } else {
            cache = redisTemplate.executePipelined(new SessionCallback<Map<String, Object>>() {
                @Override
                public <String, Object> Map execute(RedisOperations<String, Object> operations) throws DataAccessException {
                    HashOperations hashOperations = operations.opsForHash();
                    for (java.lang.String key : receiverIds) {
                        hashOperations.entries(RECEIVER_CACHE_KEY + key);
                    }
                    return null;
                }});
        }
        Map<String, Map<String, String>> result = new HashMap<String, Map<String, String>>();
        List<String> needQueryReceiverIds = new ArrayList<String>();
        for (int i = 0; i < receiverIds.size(); i++) {
            String recevierId = receiverIds.get(i);
            Map<String, String> cacheInfo = (Map<java.lang.String, java.lang.String>) cache.get(i);
            if(MapUtil.isEmpty(cacheInfo)) {
                needQueryReceiverIds.add(recevierId);
                continue;
            } else if (localReceiverCache != null && !localReceiverCache.containsKey(recevierId)) {
                localReceiverCache.put(recevierId, cacheInfo);
            }
            result.put(recevierId,  cacheInfo);
        }
        if(needQueryReceiverIds.size() > 0) {
            Map<String, Map<String, String>> queryRecivierInfos = receiverService.getReceiverTypeAndAliasByIds(needQueryReceiverIds);
            if(queryRecivierInfos.size() > 0) {
                redisTemplate.executePipelined(new SessionCallback<Map<String, Object>>() {
                    @Override
                    public <String, Object> Map execute(RedisOperations<String, Object> operations) throws DataAccessException {
                        HashOperations hashOperations = operations.opsForHash();
                        queryRecivierInfos.forEach((k, v) ->{
                            String key = (String) (RECEIVER_CACHE_KEY + k);
                            hashOperations.putAll(key, v);
                            operations.expire(key, 1, TimeUnit.HOURS);
                        });
                        return null;
                    }});
                if (localReceiverCache != null) {
                    queryRecivierInfos.forEach((k, v) ->{
                        localReceiverCache.put(k, v);
                    });
                }
            }
            result.putAll(queryRecivierInfos);
        }
        return result;
    }

    //商户灰度判断
    public Boolean isGrayExist(String type, String merchantId) {
        return merchantGrayService.isExist(MapUtil.hashMap("type", type, "merchant_id", merchantId));
    }

    public void setCashierName(Map<String, Object> orderOrTransaction) {
        if (orderOrTransaction == null || orderOrTransaction.isEmpty()) {
            return;
        }
        try {
            ChangeShiftsCashierQueryRequest request = new ChangeShiftsCashierQueryRequest();
            request.setCashierQueryInfos(Arrays.asList(new ChangeShiftsCashierQueryInfo(MapUtil.getLongValue(orderOrTransaction, DaoConstants.CTIME), MapUtil.getString(orderOrTransaction, Order.MERCHANT_ID), MapUtil.getString(orderOrTransaction, Order.TERMINAL_ID), MapUtil.getString(orderOrTransaction, "cash_desk_id"))));
            ChangeShiftsCashierQueryResponse response = changeShiftsService.getChangeShiftsCashier(request);
            if (response != null && response.getChangeShiftsInfos() != null && !response.getChangeShiftsInfos().isEmpty()
                    && response.getChangeShiftsInfos().get(0) != null) {
                orderOrTransaction.put("cashier_name", response.getChangeShiftsInfos().get(0).getCashierName());
            }
        } catch (Exception e) {
            logger.error("获取收银员信息失败", e);
            throw ErrorMessageEnum.ERROR_GETTING_DATA.getBizException();
        }
    }

    public void setCashierInfo(List<Map<String, Object>> orderOrTransactions) {
        setCashierInfo(orderOrTransactions, false);
    }

    public void setCashierInfo(List<Map<String, Object>> orderOrTransactions, boolean replaceOperatorName) {
        if (orderOrTransactions == null || orderOrTransactions.isEmpty()) {
            return;
        }
        ChangeShiftsBatchQueryRequest request = new ChangeShiftsBatchQueryRequest();
        request.setTradeCashierQuery(Boolean.TRUE);
        request.setAccessCashDesk(Boolean.TRUE);
        request.setCsMerchantId(MapUtil.getString(orderOrTransactions.get(0), Transaction.MERCHANT_ID));
        request.setStartDate(MapUtil.getLongValue(orderOrTransactions.get(orderOrTransactions.size() -1), DaoConstants.CTIME));
        request.setEndDate(MapUtil.getLongValue(orderOrTransactions.get(0), DaoConstants.CTIME));
        request.setPageSize(5000);
        ChangeShiftsBatchQueryResponse response = changeShiftsService.getChangeShiftsList(request);
         
        Map<String, ImmutablePair<ChangeShiftsInfo, TreeRangeMap<Long, ChangeShiftsInfo>>> cashierInfos = new HashMap<>();
        response
            .getRecords()
            .stream()
            .collect(Collectors.groupingBy(ChangeShiftsInfo::getServiceId))
            .forEach((k, v) -> {
                TreeRangeMap<Long, ChangeShiftsInfo> rangeMap = TreeRangeMap.create();
                v.stream().forEach(changeShifts -> {
                    rangeMap.put(Range.openClosed(changeShifts.getStartDate(), changeShifts.getEndDate()), changeShifts);
                });
                cashierInfos.put(k, ImmutablePair.of(v.get(0), rangeMap));
            });
        for (Map<String, Object> orderOrTransaction : orderOrTransactions) {
            String terminalId = MapUtil.getString(orderOrTransaction, Transaction.TERMINAL_ID);
            String cashDeskId = MapUtil.getString(orderOrTransaction, "cash_desk_id");
            ImmutablePair<ChangeShiftsInfo, TreeRangeMap<Long, ChangeShiftsInfo>> useConfig = null;
            if (!StringUtil.empty(cashDeskId)) {
                useConfig = cashierInfos.get(cashDeskId);
            }
            if (useConfig == null && !StringUtil.empty(terminalId)) {
                useConfig = cashierInfos.get(terminalId);
            }
            if (useConfig != null) {
                ChangeShiftsInfo changeInfo = useConfig.getRight().get(MapUtil.getLongValue(orderOrTransaction, DaoConstants.CTIME));
                if (changeInfo != null) {
                    // 只有收银台才需要批次汇总信息
                    if (Objects.equals(changeInfo.getType(), ChangeShifts.TYPE_CASHDESK)) {
                        orderOrTransaction.put("cashier_batch_id", changeInfo.getId());
                        orderOrTransaction.put("change_shifts_batch", changeInfo.getBatchSn());
                        orderOrTransaction.put("change_shifts_start_date", changeInfo.getStartDate());
                        orderOrTransaction.put("change_shifts_end_date", changeInfo.getEndDate());
                    }
                    orderOrTransaction.put("cashier_id", changeInfo.getCashierId());
                    orderOrTransaction.put("cashier_phone", changeInfo.getCashierPhone());
                    orderOrTransaction.put("cashier_name", changeInfo.getCashierName());
                    if (replaceOperatorName) {
                        orderOrTransaction.put("operator_name", changeInfo.getCashierName());
                    }
                }
            }
        }
    }

    public static final Cache<String, Boolean> FOREIGN_CARD_TRADE_STATUS_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.HOURS)
            .maximumSize(50000).recordStats().build();

    public Boolean isForeignCardTradeStatus(String  merchantId) {

        Boolean isForeignCardTrade = FOREIGN_CARD_TRADE_STATUS_CACHE.getIfPresent(MERCHANT_FOREIGN_CARD_TRADE_STATUS_PREFIX + merchantId);

        if (!Objects.isNull(isForeignCardTrade)){
            return isForeignCardTrade;
        }

        boolean result = false;
        Map<String, Object> merchant = businessCommonService.getMerchantMinimalInfoById(merchantId);
        if (MapUtils.isNotEmpty(merchant)) {
            String mchSn = MapUtils.getString(merchant, Merchant.SN);
            Boolean status = t9Service.getForeignCardStatus(mchSn);
            if (Objects.nonNull(status)) {
                result = status;
            }
        }
        FOREIGN_CARD_TRADE_STATUS_CACHE.put(MERCHANT_FOREIGN_CARD_TRADE_STATUS_PREFIX + merchantId, result);
        return result;
    }

    public boolean findForeignCardStatusByMerchantIds(Set<String> merchantIds) {
        // 使用共享变量来记录是否开通储值
        boolean result = false;
        try {
            for (String merchantId : merchantIds) {
                Boolean aBoolean = isForeignCardTradeStatus(merchantId);
                if (aBoolean) {
                    //一旦有一个商户开通了外卡交易，即认为这一组商户均要进行外卡交易汇总处理
                    result = true;
                    break;
                }
            }
        }catch (Exception e){
            logger.error("findStoreStaffByMerchant error: "+e.getMessage());
        }
        return result;
    }

    /**
     * 用于记录商户是否是KA商户
     */
    public final LoadingCache<String, Boolean> KA_MERCHANT_CACHE = CacheBuilder.newBuilder()
            .maximumSize(3000).expireAfterWrite(6, TimeUnit.HOURS)
            .build(new CacheLoader<String, Boolean>() {

                @Override
                public Boolean load(String key) {
                    return isBigCustomerOrIndustryCooperation(key);
                }
            });


    //判断组织路径是否属于大客户或行业合作下 ， 其中大客户以00069开头、行业合作以00052开头
    private boolean isBigCustomerOrIndustryCooperation(String merchantId) {
        boolean bigCustomerOrIndustryCooperation = false;

        Set<String> filterKAMerchantIds = ApolloUtil.getFilterKAMerchantIds();
        //若filterKAMerchantIds包含merchantId， 那么该商户应下载非KA对账单
        if (filterKAMerchantIds.contains(merchantId)) {
            return false;
        }

        try {
            CustomerRelationOriginReq req = new CustomerRelationOriginReq();
            req.setCustomerIds(Collections.singletonList(merchantId));
            PageListResult<CustomerRelationOriginResp> customerRelationOrigin = customerRelationFacade.findCustomerRelationOrigin(req);
            for (CustomerRelationOriginResp record : customerRelationOrigin.getRecords()) {
                if (Objects.nonNull(record.getOrganization()) && WosaiStringUtils.isNotEmpty(record.getOrganization().getPath())) {
                    String organizationPath = record.getOrganization().getPath();
                    bigCustomerOrIndustryCooperation = StringUtils.startsWithIgnoreCase(organizationPath, "00069") || StringUtils.startsWithIgnoreCase(organizationPath, "00052");
                    break;
                }
            }
        } catch (Exception e) {
            throw new ServiceException("判断商户{}是否是大客户或行业合作异常", e);
        }
        return bigCustomerOrIndustryCooperation;
    }

    public boolean isKAStatement(String merchantId)  {

        try {
            return KA_MERCHANT_CACHE.get(merchantId);
        } catch (Exception e) {
            log.error("判断商户{}是否是KA商户异常", merchantId, e);
        }
        return false;
    }

    public final Cache<String, Boolean> GROUP_HAS_KA_MERCHANT_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(6, TimeUnit.HOURS)
            .expireAfterWrite(6, TimeUnit.HOURS)
            .maximumSize(1000).recordStats().build();

    ExecutorService executorService = Executors.newFixedThreadPool(10);
    private static final String GROUP_PREFIX = "group:";


    private boolean isGroupHasBigCustomerOrIndustryCooperation(String groupSn, List<String> merchantIds) {

        Boolean value = GROUP_HAS_KA_MERCHANT_CACHE.getIfPresent(GROUP_PREFIX + groupSn);

        if (!Objects.isNull(value)){
            return value;
        }

        CountDownLatch latch = new CountDownLatch(merchantIds.size());

        // 使用共享变量来记录是否开通储值
        AtomicBoolean result = new AtomicBoolean(false);

        for (String merchantId : merchantIds) {
            // 提交商户储值判断任务给线程池执行
            executorService.submit(() -> {
                try {
                    if (result.get()) {
                        return;
                    }
                    boolean bigCustomerOrIndustryCooperation = isBigCustomerOrIndustryCooperation(merchantId);
                    if (bigCustomerOrIndustryCooperation) {
                        result.set(true);
                    }
                } catch (Exception exception) {
                    log.info("集团商户下商户{}, 判断是否是大客户或行业合作组织异常", merchantId, exception);
                } finally {
                    // 商户储值判断任务完成之后计数器减1
                    latch.countDown();
                }
            });
        }

        // 等待所有任务完成
        try {
            latch.await();
        } catch (InterruptedException e) {
            throw new ServiceException("CountDownLatch error", e);
        }
        return result.get();
    }

    public boolean isGroupKAStatement(String groupSn, List<String> merchantIds)  {
        try {
            return isGroupHasBigCustomerOrIndustryCooperation(groupSn, merchantIds);
        } catch (Exception e) {
            log.error("判断集团{}是否存在KA商户异常", groupSn, e);
        }
        return false;
    }

    /**
     * 校验流水查询用户数据权限
     * @param queryFilter
     */
    public void checkDataPermission(PageInfo pageInfo, Map<String, Object> queryFilter) {
        String merchantUserId = MapUtil.getString(queryFilter, "merchant_user_id");
        if (StringUtils.isEmpty(merchantUserId)) {
            return;
        }
        PlatformEnum platformEnum;
        String permissionCode ;
        int permissionType = MapUtil.getIntValue(queryFilter, "permissionType");
        if (PermissionType.APP_TRANS.getCode() == permissionType) {
            platformEnum = PlatformEnum.APP;
            permissionCode = CommonConstant.APP_TRANSACTION_PERMISSION_CODE;
        } else if (PermissionType.MSP_TRANS.getCode() == permissionType) {
            platformEnum = PlatformEnum.MSP;
            permissionCode = CommonConstant.MSP_TRANSACTION_PERMISSION_CODE;
        } else {
            return;
        }

        try {
            AnalyzeDataPermissionResponseDTO permissionResponseDTO = getDataPermissionOption(merchantUserId, platformEnum, permissionCode);
            if (CollectionUtils.isEmpty(permissionResponseDTO.getOptionsList())) {
                return;
            }
            Tuple2<Date, Date> date = null;
            for (AnalyzeDataPermissionResponseDTO.OptionsBean optionsBean : permissionResponseDTO.getOptionsList()) {
                Date startTime = Objects.nonNull(pageInfo.getDateStart()) ? new Date(pageInfo.getDateStart()) : null;
                Date endTime = Objects.nonNull(pageInfo.getDateEnd()) ? new Date(pageInfo.getDateEnd()) : null;
                date = processTimePermission(startTime, endTime, optionsBean);

                if (Objects.nonNull(date)) {
                    if (Objects.nonNull(date.r1)) {
                        pageInfo.setDateStart(BigDecimal.valueOf(date.r1.getTime()).longValue());
                    }
                    if (Objects.nonNull(date.r2)) {
                        pageInfo.setDateEnd(BigDecimal.valueOf(date.r2.getTime()).longValue());
                    }
                }
            }

        } catch (Exception e) {
            // 处理异常，例如记录日志或返回错误响应
            // 根据需要设置startDate和endDate的默认值或抛出异常
            log.error("账本流水数据权限获取异常异常", e);
        }
    }

    /**
     * 校验对账单用户数据权限
     * @param queryFilter
     */
    public void checkDataPermission(Map<String, Object> queryFilter, String userId) {

        if (StringUtils.isEmpty(userId)) {
            return;
        }

        PlatformEnum platformEnum;
        String permissionCode ;
        int permissionType = MapUtil.getIntValue(queryFilter, "permissionType");
        if (PermissionType.MSP_TRANS.getCode() == permissionType) {
            platformEnum = PlatformEnum.MSP;
            permissionCode = CommonConstant.MSP_TRANSACTION_PERMISSION_CODE;
        } else if (PermissionType.MSP_REPORT.getCode() == permissionType) {
            platformEnum = PlatformEnum.MSP;
            permissionCode = CommonConstant.MSP_REPORT_PERMISSION_CODE;
        } else {
            return;
        }

        try {
            AnalyzeDataPermissionResponseDTO permissionResponseDTO = getDataPermissionOption(userId, platformEnum, permissionCode);
            if (CollectionUtils.isEmpty(permissionResponseDTO.getOptionsList())) {
                return;
            }
            Tuple2<Date, Date> date = null;
            for (AnalyzeDataPermissionResponseDTO.OptionsBean optionsBean : permissionResponseDTO.getOptionsList()) {
                Long start = MapUtil.getLong(queryFilter, ConstantUtil.KEY_DATE_START);
                Long end = MapUtil.getLong(queryFilter, ConstantUtil.KEY_DATE_END);
                Date startTime = Objects.nonNull(start) ? new Date(start) : null;
                Date endTime = Objects.nonNull(end) ? new Date(end) : null;
                date = processTimePermission(startTime, endTime, optionsBean);

                if (Objects.nonNull(date)) {
                    if (Objects.nonNull(date.r1)) {
                        queryFilter.put(ConstantUtil.KEY_DATE_START, BigDecimal.valueOf(date.r1.getTime()).longValue());
                    }
                    if (Objects.nonNull(date.r2)) {
                        queryFilter.put(ConstantUtil.KEY_DATE_END, BigDecimal.valueOf(date.r2.getTime()).longValue());
                    }
                }
            }

        } catch (Exception e) {
            // 处理异常，例如记录日志或返回错误响应
            // 根据需要设置startDate和endDate的默认值或抛出异常
            log.error("对账单流水数据权限获取异常异常", e);
        }
    }

    /**
     * 校验账本用户数据权限
     * @param param
     */
    public void checkDataPermission(AccountRecordParam param) {

        String merchantUserId = param.getMerchant_user_id();
        if (StringUtils.isEmpty(merchantUserId)) {
            return;
        }

        PlatformEnum platformEnum;
        String permissionCode ;
        int permissionType = param.getPermissionType();
        if (PermissionType.APP_TRANS.getCode() == permissionType) {
            platformEnum = PlatformEnum.APP;
            permissionCode = CommonConstant.APP_TRANSACTION_PERMISSION_CODE;
        } else if (PermissionType.MSP_TRANS.getCode() == permissionType) {
            platformEnum = PlatformEnum.MSP;
            permissionCode = CommonConstant.MSP_TRANSACTION_PERMISSION_CODE;
        } else {
            return;
        }

        try {
            AnalyzeDataPermissionResponseDTO permissionResponseDTO = getDataPermissionOption(merchantUserId, platformEnum, permissionCode);
            if (CollectionUtils.isEmpty(permissionResponseDTO.getOptionsList())) {
                return;
            }
            Tuple2<Date, Date> date = null;
            for (AnalyzeDataPermissionResponseDTO.OptionsBean optionsBean : permissionResponseDTO.getOptionsList()) {
                Date startTime = param.getStartTime();
                Date endTime = param.getEndTime();
                date = processTimePermission(startTime, endTime, optionsBean);

                if (Objects.nonNull(date)) {
                    param.setStartTime(date.r1);
                    param.setEndTime(date.r2);
                }
            }

        } catch (Exception e) {
            // 处理异常，例如记录日志或返回错误响应
            // 根据需要设置startDate和endDate的默认值或抛出异常
            log.error("账本数据权限获取异常异常", e);
        }
    }

    //用户数据权限
    public static final Cache<String, AnalyzeDataPermissionResponseDTO> USER_DATA_PERMISSION_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(5000).recordStats().build();
    private static final String DATA_PERMISSION_PREFIX = "permission:";

    private AnalyzeDataPermissionResponseDTO getDataPermissionOption(String merchantUserId, PlatformEnum platformEnum, String permissionCode) {

        String key = String.format("%s-%s", merchantUserId, permissionCode);
        AnalyzeDataPermissionResponseDTO value = USER_DATA_PERMISSION_CACHE.getIfPresent(DATA_PERMISSION_PREFIX + key);

        if (!Objects.isNull(value)){
            return value;
        }
        AnalyzeDataPermissionResponseDTO permission;
        try {
            QueryAnalyzeDataPermissionRequestDTO requestDTO = new QueryAnalyzeDataPermissionRequestDTO();
            requestDTO.setMerchantUserId(merchantUserId);
            requestDTO.setPlatform(platformEnum);
            requestDTO.setCode(permissionCode);
            requestDTO.setMergeConfig("merchant_reopen");
            permission = merchantUserDataPermissionsService.getDataPermissionAnalyzeDataPermissionByCode(requestDTO);

        } catch (Exception exception) {
            throw new ServiceException("获取用户:"+ merchantUserId +"指定数据权限异常, 权限code为" + permissionCode, exception);
        }

        if (Objects.isNull(permission)) {
            permission = new AnalyzeDataPermissionResponseDTO();
        }
        USER_DATA_PERMISSION_CACHE.put(DATA_PERMISSION_PREFIX + key, permission);
        return permission;
    }

    private Tuple2<Date, Date> processTimePermission(Date startTime, Date endTime, AnalyzeDataPermissionResponseDTO.OptionsBean optionBean) {
        if(Objects.isNull(optionBean)) {
            return new Tuple2<>(startTime, endTime);
        }
        Date optionStartTime = optionBean.getStartTime();
        Date optionEndTime = optionBean.getEndTime();

        if (Objects.nonNull(startTime) && Objects.nonNull(optionStartTime)) {
            //两者不为null时，取大值
            startTime = startTime.compareTo(optionStartTime) < 0 ? optionStartTime : startTime;
        } else {
            startTime = Objects.nonNull(optionStartTime) ? optionStartTime : startTime;
        }

        if (Objects.nonNull(endTime) && Objects.nonNull(optionEndTime)) {
            //两者不为null时，取小值
            endTime = endTime.compareTo(optionEndTime) < 0 ? endTime : optionEndTime;
        } else {
            endTime = Objects.nonNull(optionEndTime) ? optionEndTime : endTime;
        }

        return new Tuple2<>(startTime, endTime);
    }

}
