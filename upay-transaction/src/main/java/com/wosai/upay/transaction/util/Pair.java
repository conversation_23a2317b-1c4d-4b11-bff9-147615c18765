package com.wosai.upay.transaction.util;

import java.util.Objects;

/**
 * <AUTHOR>
 * @param <A>
 * @param <B>
 */
public class Pair <A, B> {

    public final A fst;
    public final B snd;

    public Pair(A var1, B var2) {
        this.fst = var1;
        this.snd = var2;
    }

    public String toString() {
        return "Pair[" + this.fst + "," + this.snd + "]";
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        Pair<?, ?> pair = (Pair<?, ?>) o;
        return Objects.equals(fst, pair.fst) &&
                Objects.equals(snd, pair.snd);
    }

    @Override
    public int hashCode() {

        return Objects.hash(fst, snd);
    }

    public static <A, B> Pair<A, B> of(A var0, B var1) {
        return new Pair(var0, var1);
    }
}
