package com.wosai.upay.transaction.util;

import avro.shaded.com.google.common.collect.Lists;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Splitter;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.Transaction;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by kay on 2017/7/10.
 */
public class CommonUtil {
    public static final Logger logger = LoggerFactory.getLogger(CommonUtil.class);
    public static ObjectMapper objectMapper = new ObjectMapper();

    private static final Splitter SPLITTER = Splitter.on(',').trimResults().omitEmptyStrings();

    public static Long parseLong(Object number) {
        if (number instanceof String) {
            return Long.parseLong((String) number);
        } else if (number instanceof Number) {
            return ((Number) number).longValue();
        } else {
            return null;
        }
    }

    public static double getDoubleValue(Map map, Object key) {
        Double doubleObject = getDouble(map, key);
        return doubleObject == null ? 0.0D : doubleObject.doubleValue();
    }

    public static Double getDouble(final Map map, final Object key) {
        Number answer = getNumber(map, key);
        if (answer == null) {
            return null;
        } else if (answer instanceof Double) {
            return (Double) answer;
        }
        return new Double(answer.doubleValue());
    }

    public static Number getNumber(final Map map, final Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                if (answer instanceof Number) {
                    return (Number) answer;

                } else if (answer instanceof String) {
                    try {
                        String text = (String) answer;
                        return NumberFormat.getInstance().parse(text);

                    } catch (ParseException e) {
                        logger.error("ParseExceptiont: " + e);
                        return null;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 四舍五入保留多少位小数点
     *
     * @param money
     * @param scaleSize
     * @return
     */
    public static double formatMoney(Object money, int scaleSize) {
        if (money == null) {
            return 0.00;
        }
        return new BigDecimal(money.toString()).setScale(scaleSize, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static PageInfo setPageInfoDefaultValueIfNull(PageInfo pageInfo) {
        if (pageInfo == null) {
            pageInfo = new PageInfo(1, 10);
        }
        if (pageInfo.getPage() == null || pageInfo.getPage().intValue() < 0) {
            pageInfo.setPage(1);
        }
        if (pageInfo.getPageSize() == null || pageInfo.getPageSize().intValue() < 0) {
            pageInfo.setPageSize(10);
        }
        if (pageInfo.getOrderBy() == null || pageInfo.getOrderBy().size() == 0) {
            pageInfo.setOrderBy(Arrays.asList(
                    new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.DESC)
            ));
        }

        return pageInfo;
    }

    /**
     * 找出每一个list里面相同的元素
     *
     * @param allElementsIds
     * @return
     */
    public static Set<String> getAllSameElementInLists(List<List<String>> allElementsIds) {
        Map<String, Integer> elementCounts = new HashMap<>(16);
        for (List<String> elements : allElementsIds) {
            for (String ele : elements) {
                elementCounts.put(ele, elementCounts.get(ele) == null ? 1 : elementCounts.get(ele) + 1);
            }
        }
        int size = allElementsIds.size();
        Set<String> sameElements = new HashSet<>();
        for (String ele : elementCounts.keySet()) {
            int count = elementCounts.get(ele);
            if (count >= size) {
                sameElements.add(ele);
            }
        }
        return sameElements;
    }

    /**
     * 每一个元素通过flag拼接为一个字符串
     *
     * @param flag
     * @param eles
     * @return
     */
    public static String join(String flag, Collection<String> eles) {
        if (eles == null) {
            return null;
        }
        StringBuffer sb = null;
        if (eles.size() == 1) {
            return eles.iterator().next();
        }
        Iterator<String> iterator = eles.iterator();
        while (iterator.hasNext()) {
            if (sb == null) {
                sb = new StringBuffer(iterator.next());
            } else {
                sb.append(flag).append(iterator.next());
            }
        }
        for (int i = 0; i < eles.size(); i++) {

        }
        return sb.toString();
    }

    public static List getListFromObject(Object object) {
        try {
            if (object == null) {
                return null;
            } else if (object instanceof List) {
                return (List) object;
            } else if (object instanceof Map) {
                return Arrays.asList(object);
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("can not transfer to List: " + object);
            return null;
        }
    }

    public static Map getMapFromJsonObject(Object jsonObject) {
        try {
            if (jsonObject == null) {
                return null;
            } else if (jsonObject instanceof String) {
                return objectMapper.readValue(((String) jsonObject).getBytes("utf-8"), Map.class);
            } else if (jsonObject instanceof byte[]) {
                return objectMapper.readValue((byte[]) jsonObject, Map.class);
            } else if (jsonObject instanceof Map) {
                return (Map) jsonObject;
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("can not transfer to map: " + jsonObject);
            return null;
        }
    }

    public static void removeDuplicatesRecord(List<Map<String, Object>> part, List<String> edgeRecordIds, long endTime) {
        //过滤上一个 part 中的记录
        Iterator iterator = part.iterator();
        while (iterator.hasNext()) {
            Map record = (Map) iterator.next();
            String id = BeanUtil.getPropString(record, ConstantUtil.KEY_ID);
            long ctime = getTime(record);
            if (ctime != endTime) {
                break;
            }
            if (edgeRecordIds.contains(id)) {
                iterator.remove();
            }
        }
    }

    public static List<String> addDuplicatesRecord(List<Map<String, Object>> part, long endTime) {
        List<String> edgeRecordIds = new LinkedList();
        for (int i = part.size() - 1; i >= 0; i--) {
            Map record = part.get(i);
            String id = BeanUtil.getPropString(record, ConstantUtil.KEY_ID);
            long ctime = getTime(record);
            if (ctime != endTime) {
                break;
            }
            edgeRecordIds.add(id);
        }
        return edgeRecordIds;
    }


    public static long getTime(Map<String, Object> record) {
        return DBSelectContext.getContext().getFinishTimeQuery() ? BeanUtil.getPropLong(record, Transaction.CHANNEL_FINISH_TIME) : BeanUtil.getPropLong(record, ConstantUtil.KEY_CTIME);
    }

    public static List<OrderBy> getTimeOrderBy() {
        return DBSelectContext.getContext().getFinishTimeQuery() ? Arrays.asList(new OrderBy(Transaction.CHANNEL_FINISH_TIME, OrderBy.OrderType.DESC)) : Arrays.asList(new OrderBy(ConstantUtil.KEY_CTIME, OrderBy.OrderType.DESC));
    }

    public static List<String> listMapToListString(List<Map> listMap, String key) {
        if (listMap == null || listMap.size() == 0) {
            return new ArrayList<>();
        }
        List<String> result = new ArrayList<>();
        for (Map map : listMap) {
            result.add(BeanUtil.getPropString(map, key));
        }
        return result;
    }

    public static List<String> splitToListString(String value) {
        if (StringUtils.hasLength(value)) {
            return Arrays.stream(value.split(","))
                    .filter(v -> v.length() > 0)
                    .collect(Collectors.toList());
        }

        return Lists.newArrayList();
    }

    public static List<Integer> splitToListInteger(String value) {
        if (StringUtils.hasLength(value)) {
            return Arrays.stream(value.split(","))
                    .filter(v -> v.length() > 0)
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    public static List<Integer> stringToIntList(List<String> stringList) {
        if (CollectionUtils.isEmpty(stringList)) {
            return Lists.newArrayList();
        }
        return Lists.transform(stringList, Integer::valueOf);
    }

    @SafeVarargs
    public static <E> List<E> newArrayList(E... elements) {
        List<E> list;
        if (elements == null || elements.length == 0) {
            list = new ArrayList<>(0);
            return list;
        }
        list = new ArrayList<>(elements.length);
        Collections.addAll(list, elements);

        return list;
    }

    public static void putToMapIfValueNotEmpty(Map<String,Object> target, String key, Object value){
        if(value == null){
            return;
        }
        if(value instanceof String && !((String) value).isEmpty()){
            target.put(key, value);
        }else if(value instanceof Map && !((Map<?, ?>) value).isEmpty()){
            target.put(key, value);
        }else if(value instanceof Collection && !((Collection<?>) value).isEmpty()){
            target.put(key, value);
        }
    }

    public static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 11) {
            return phoneNumber;
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);
    }
}
