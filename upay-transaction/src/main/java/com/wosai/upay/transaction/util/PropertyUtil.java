package com.wosai.upay.transaction.util;

import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PropertyUtil extends PropertyPlaceholderConfigurer {

    private static Map<String, String> propertiesData = new HashMap<>();

    @Override
    protected String convertProperty(String propertyName, String propertyValue) {
        String value = super.convertProperty(propertyName, propertyValue);
        propertiesData.put(propertyName, value);

        return value;
    }

    /**
     * 获取Properties文件中的值,如果不存在则返回defaultValue
     *
     * @param key
     * @param defaultValue
     * @return
     */
    public static String getProperty(String key, String defaultValue) {
        String value = propertiesData.get(key);
        return value == null ? defaultValue : value;
    }

    /**
     * 获取Properties文件中的值,不存在返回null
     *
     * @param key
     * @return
     */
    public static String getProperty(String key) {
        return getProperty(key, null);
    }
}
