package com.wosai.upay.transaction.helper;

import com.wosai.upay.transaction.annotation.DBSelectField;
import com.wosai.upay.transaction.annotation.DBSelectMethod;
import com.wosai.upay.transaction.annotation.DBSelectParam;
import com.wosai.upay.transaction.enums.UpayQueryType;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.beans.factory.annotation.Value;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DBSelectServiceMethodInterceptor implements MethodInterceptor {

    @Value("${upay_swipe.prefix}")
    private String prefix;

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        if(DBSelectContext.getContext().getInit()!= null){
            return invocation.proceed();
        }
        Method method = invocation.getMethod();
        if (method.isAnnotationPresent(DBSelectMethod.class)) {
            Parameter[] parameters = method.getParameters();
            Object[] args = invocation.getArguments();
            int length = parameters.length;
            for (int i = 0; i < length; i++) {
                Parameter parameter = parameters[i];
                DBSelectParam dbSelectParam = parameter.getAnnotation(DBSelectParam.class);
                if (dbSelectParam != null) {
                    Class<?> paramClazz = parameter.getType();
                    Object arg = args[i];
                    if (arg != null) {
                        //基本类型
                        if (isPrimite(paramClazz)) {
                            if (String.valueOf(arg).startsWith(prefix)) {
                                DBSelectContext.getContext().initDB(UpayQueryType.UPAY_SWIPE.getCode());
                            } else {
                                DBSelectContext.getContext().initDB(UpayQueryType.UPAY.getCode());
                            }
                            break;
                        }

                        //map
                        if (paramClazz.isAssignableFrom(Map.class)) {
                            Map map = (Map) arg;
                            Object selectParam = map.get(dbSelectParam.value());
                            if(selectParam != null){
                                DBSelectContext.getContext().initDB((Integer) selectParam);
                            }
                            break;
                        }


                        //引用类型
                        Field[] declaredFields = paramClazz.getDeclaredFields();
                        for (Field field : declaredFields) {
                            field.setAccessible(true);
                            DBSelectField dbSelectField = field.getAnnotation(DBSelectField.class);
                            if (dbSelectField != null) {
                                Object fieldValue = field.get(arg);
                                if (fieldValue != null) {
                                    DBSelectContext.getContext().initDB((Integer) fieldValue);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }

        Object result;
        try {
            result = invocation.proceed();
        } finally {
            DBSelectContext.removeContext();
        }
        return result;
    }



    /**
     * 判断是否为基本类型：包括String
     * @param clazz clazz
     * @return true：是;   false：不是
     */
    private boolean isPrimite(Class<?> clazz){
        return clazz.isPrimitive() || clazz == String.class;
    }
}
