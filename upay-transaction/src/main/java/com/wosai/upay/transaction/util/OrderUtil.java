package com.wosai.upay.transaction.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.Receiver;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.Payment;
import com.wosai.upay.transaction.model.StatementConfig;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.service.StatementConfigService;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.transaction.util.LanguageUtil.*;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class OrderUtil implements InitializingBean {
    public static final Logger logger = LoggerFactory.getLogger(OrderUtil.class);

    @Autowired
    private StatementConfigService statementConfigService;
    @Resource
    private BusinessService businessService;

    public static ObjectMapper objectMapper = new ObjectMapper();
    public static LinkedHashMap<String, String> paywayMap = new LinkedHashMap<String, String>();
    public static LinkedHashMap<String, String> paywayLangugeMap = new LinkedHashMap<String, String>();

    public static List<Integer> paywayList = new ArrayList<>();
    public static Set<Integer> chargePaywayList = new HashSet<>();
    public static Map<Integer, String> currencyMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() {
        List<Map<String, Object>> paywayRecords = statementConfigService.getPaywayRecords();
        chargePaywayList =  businessService.getAllChargePayWay().keySet();
        if (paywayRecords != null) {
            for (Map paywayRecord : paywayRecords) {
                int payway = BeanUtil.getPropInt(paywayRecord, StatementConfig.KEY);
                String name = BeanUtil.getPropString(paywayRecord, StatementConfig.NAME);
                String currency = BeanUtil.getPropString(paywayRecord, StatementConfig.CURRENCY);

                paywayList.add(payway);
                paywayMap.put(String.valueOf(payway), name);
                currencyMap.put(payway, currency);

                Map<String, String> extraInfo = (Map) BeanUtil.getProperty(paywayRecord, StatementConfig.EXTRA_INFO);
                if (CollectionUtils.isEmpty(extraInfo)) {
                    continue;
                }
                for (String lan : extraInfo.keySet()) {
                    paywayLangugeMap.put(payway + lan, extraInfo.get(lan));
                }

            }
        }

    }

    /**
     * 扩展订单计算逻辑字段
     *
     * @param orderEx
     * @param orderTransactions
     * @return orderEx
     * 返回扩展了计算字段的订单，计算规则如下：
     * mch_favorable_amount(商户优惠) = net_hongbao_wosai_mch(喔噻商户红包) + net_discount_wosai_mch(喔噻商户折扣)
     * wosai_favorable_amount(收钱吧优惠) = net_original(原始金额净值) - net_effective(支付通道金额净值) - mch_favorable_amount(商户优惠)
     * channelAgentFavorableAmount(收款通道机构优惠) = net_hongbao_channel(收款通道红包净值) + net_discount_channel(收款通道折扣净值)
     * channel_mch_favorable_amount(收款通道商户免充值优惠) = net_discount_channel_mch(收款通道商户免充值折扣净值) + net_hongbao_channel_mch(收款通道商户免充值红包净值)
     * channel_mch_top_up_favorable_amount(收款通道商户充值优惠) = net_discount_channel_mch_top_up(收款通道商户充值折扣净值) + net_hongbao_channel_mch_top_up(收款通道商户充值红包净值)
     * channel_favorable_amount(收款通道优惠) = channelAgentFavorableAmount(收款通道机构优惠) + channel_mch_favorable_amount(收款通道商户免充值优惠) + channel_mch_top_up_favorable_amount(收款通道商户充值优惠)
     * actual_receive_amount(实收金额) = net_original(原始金额净值) - mch_favorable_amount(商户优惠) - channel_mch_favorable_amount(收款通道商户免充值优惠)
     */
    public static Map caculateExtendFields(Map orderEx, List<Map<String, Object>> orderTransactions) {
        int status = BeanUtil.getPropInt(orderEx, Order.STATUS, -1);
        Long clearingAmount = 0L;
        Long actualReceiveAmount = 0L;
        Long mchFavorableAmount = 0L;
        Long wosaiFavorableAmount = 0L;
        Long channelFavorableAmount = 0L;
        Long channelAgentFavorableAmount = 0L;
        Long channelMchFavorableAmount = 0L;
        Long channelMchTopUpFavorableAmount = 0L;
        Long fee = 0L;
        long sharingAmount = 0L;
        Long tradeServiceSharingAmount = 0L;
        long hbfqDiscountAmount = 0L;

        List statusList = Arrays.asList(
                Order.STATUS_CREATED, Order.STATUS_PAY_CANCELED, Order.STATUS_PAY_ERROR, Order.STATUS_REFUNDED, Order.STATUS_CANCELED, Order.STATUS_DEPOSIT_CONSUMED_CANCELED
                , Order.STATUS_DEPOSIT_FREEZE_INPROGRESS
                , Order.STATUS_DEPOSIT_FREEZED
                , Order.STATUS_DEPOSIT_FREEZE_CANCELED
                , Order.STATUS_DEPOSIT_FREEZE_ERROR
                , Order.STATUS_DEPOSIT_CANCEL_INPROGRESS
                , Order.STATUS_DEPOSIT_CANCELED
                , Order.STATUS_DEPOSIT_CANCEL_ERROR
                , Order.STATUS_DEPOSIT_CONSUME_ERROR);
        if (!statusList.contains(status)) {
            Long netHongbaoWosaiMch = BeanUtil.getPropLong(orderEx, Order.NET_HONGBAO_WOSAI_MCH);
            Long netDiscountWosaiMch = BeanUtil.getPropLong(orderEx, Order.NET_DISCOUNT_WOSAI_MCH);

            Long netHongbaoChannel = BeanUtil.getPropLong(orderEx, Order.NET_HONGBAO_CHANNEL);
            Long netDiscountChannel = BeanUtil.getPropLong(orderEx, Order.NET_DISCOUNT_CHANNEL);
            Long netDiscountChannelMch = BeanUtil.getPropLong(orderEx, Order.NET_DISCOUNT_CHANNEL_MCH);
            Long netDiscountChannelMchTopUp = BeanUtil.getPropLong(orderEx, Order.NET_DISCOUNT_CHANNEL_MCH_TOP_UP);
            Long netHongbaoChannelMch = BeanUtil.getPropLong(orderEx, Order.NET_HONGBAO_CHANNEL_MCH);
            Long netHongbaoChannelMchTopUp = BeanUtil.getPropLong(orderEx, Order.NET_HONGBAO_CHANNEL_MCH_TOP_UP);
            Long netOriginal = BeanUtil.getPropLong(orderEx, Order.NET_ORIGINAL);
            Long netEffective = BeanUtil.getPropLong(orderEx, Order.NET_EFFECTIVE);

            List<Map<String, Object>> successOrderTransactions = orderTransactions.stream().filter(transaction -> MapUtils.getInteger(transaction, Transaction.STATUS) == Transaction.STATUS_SUCCESS).collect(Collectors.toList());
            Map<String, Object> calculateFields = calculateFromTransactionList(successOrderTransactions);
            sharingAmount = MapUtils.getLongValue(calculateFields, Order.SHARING_AMOUNT);
            tradeServiceSharingAmount = MapUtils.getLongValue(calculateFields, Order.TRADE_SERVICE_SHARING_AMOUNT);
            fee = MapUtils.getLongValue(calculateFields, Order.FEE);
            hbfqDiscountAmount = MapUtils.getLongValue(calculateFields, TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE);

            //商户优惠
            mchFavorableAmount = OrderOrTransAmountUtil.getMerchantFavorableAmount(netHongbaoWosaiMch, netDiscountWosaiMch);
            //收钱吧优惠
            wosaiFavorableAmount = OrderOrTransAmountUtil.getWosaiFavorableAmount(netOriginal, netEffective, mchFavorableAmount);
            //收款通道机构优惠
            channelAgentFavorableAmount = OrderOrTransAmountUtil.getChannelAgentFavorableAmount(netHongbaoChannel, netDiscountChannel);
            //收款通道商户免充值优惠
            channelMchFavorableAmount = OrderOrTransAmountUtil.getChannelMchFavorableAmount(netDiscountChannelMch, netHongbaoChannelMch);
            //收款通道商户充值优惠
            channelMchTopUpFavorableAmount = OrderOrTransAmountUtil.getChannelMchTopUpFavorableAmount(netDiscountChannelMchTopUp, netHongbaoChannelMchTopUp);
            //收款通道优惠
            channelFavorableAmount = OrderOrTransAmountUtil.getChannelFavorableAmount(channelAgentFavorableAmount, channelMchFavorableAmount, channelMchTopUpFavorableAmount);
            //实收金额
            actualReceiveAmount = OrderOrTransAmountUtil.getActualReceiveAmount(netOriginal, mchFavorableAmount, channelMchFavorableAmount);

            //结算金额
            clearingAmount = OrderOrTransAmountUtil.getClearingAmount(actualReceiveAmount, fee);
            if (!StringUtil.empty(BeanUtil.getPropString(orderEx, TransactionParam.FEE_ORIGINAL))) {
                String originalFeeRate = BeanUtil.getPropString(orderEx, TransactionParam.FEE_RATE_ORIGINAL, "0.0");
                orderEx.put(TransactionParam.FEE_ORIGINAL, Math.round(actualReceiveAmount * CommonUtil.formatMoney(originalFeeRate, 3) * 0.01));
            }
        }

        orderEx.put(Order.MCH_FAVORABLE_AMOUNT, mchFavorableAmount);
        orderEx.put(Order.WOSAI_FAVORABLE_AMOUNT, wosaiFavorableAmount);
        orderEx.put(Order.CHANNEL_AGENT_FAVORABLE_AMOUNT, channelAgentFavorableAmount);
        orderEx.put(Order.CHANNEL_MCH_FAVORABLE_AMOUNT, channelMchFavorableAmount);
        orderEx.put(Order.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT, channelMchTopUpFavorableAmount);
        orderEx.put(Order.CHANNEL_FAVORABLE_AMOUNT, channelFavorableAmount);
        orderEx.put(Order.ACTUAL_RECEIVE_AMOUNT, actualReceiveAmount);
        orderEx.put(Order.FEE, fee);
        orderEx.put(Order.SHARING_AMOUNT, sharingAmount);
        orderEx.put(Order.TRADE_SERVICE_SHARING_AMOUNT, tradeServiceSharingAmount);
        orderEx.put(Order.CLEARING_AMOUNT, clearingAmount - sharingAmount - tradeServiceSharingAmount);
        orderEx.put(TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE, hbfqDiscountAmount);
        return orderEx;
    }

    /**
     * 从订单流水列表中计算一些金额
     */
    @NotNull
    private static Map<String, Object> calculateFromTransactionList(List<Map<String, Object>> transactionList) {
        if (CollectionUtils.isEmpty(transactionList)) {
            return Maps.newTreeMap();
        }

        long orderSharingAmount = 0L;
        long orderTradeServiceSharingAmount = 0L;
        long orderFee = 0L;
        long hbfqDiscountAmount = 0L;

        for (Map<String, Object> transaction : transactionList) {
            if (transaction == null) {
                continue;
            }

            TransactionUtil.expandTransactionInfo(transaction);
            TransactionUtil.expandTransactionItemsPayments(transaction, false);
            TransactionUtil.expandTransactionItemTradeInfo(transaction);
            TransactionUtil.calculateExtendFields(transaction);

            long txSharingAmount = MapUtils.getLongValue(transaction, Transaction.SHARING_AMOUNT);
            long txTradeServiceSharingAmount = MapUtils.getLongValue(transaction, Transaction.TRADE_SERVICE_SHARING_AMOUNT);
            long txFee = MapUtils.getLongValue(transaction, Transaction.FEE);
            long txHbfqDiscountAmount = MapUtils.getLongValue(transaction, TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE);

            int type = MapUtils.getIntValue(transaction, Transaction.TYPE);
            // 正向交易：预授权完成+收款+储值充值
            if (TransactionType.DEPOSIT_CONSUME.getCode() == type 
                    || TransactionType.PAYMENT.getCode() == type 
                    || TransactionType.STORE_IN.getCode() == type) {
                orderSharingAmount += txSharingAmount;
                orderTradeServiceSharingAmount += txTradeServiceSharingAmount;
                orderFee += txFee;
                hbfqDiscountAmount += txHbfqDiscountAmount;
            }
            // 逆向交易：撤单+退款+预授权完成撤销(刷卡交易特有)+储值充值退款
            if (TransactionType.CANCEL.getCode() == type 
                    || TransactionType.REFUND.getCode() == type
                    || TransactionType.DEPOSIT_CONSUME_CANCEL.getCode() == type
                    || TransactionType.IN_REFUND.getCode() == type) {
                orderSharingAmount -= txSharingAmount;
                orderTradeServiceSharingAmount -= txTradeServiceSharingAmount;
                orderFee -= txFee;
                hbfqDiscountAmount -= txHbfqDiscountAmount;
            }
        }

        Map<String, Object> result = Maps.newHashMap();
        result.put(Order.SHARING_AMOUNT, orderSharingAmount);
        result.put(Order.TRADE_SERVICE_SHARING_AMOUNT, orderTradeServiceSharingAmount);
        result.put(Order.FEE, orderFee);
        result.put(TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE, hbfqDiscountAmount);

        return result;
    }

    public static void transformReflect(Map<String, Object> order) {
        Object reflect = BeanUtil.getProperty(order, Order.REFLECT);
        if (reflect instanceof byte[]) {
            String reflectString = new String((byte[]) reflect);
            order.put(Order.REFLECT, reflectString);
        }
    }

    public static void jsonFormatOrder(Map<String, Object> order) {
        List<String> jsonBlobColumnList = Arrays.asList(
                Order.ITEMS,
                Order.NET_ITEMS
        );
        for (String column : order.keySet()) {
            if (jsonBlobColumnList.contains(column)) {
                Object value = order.get(column);
                if (!(value instanceof Map) && value != null) {
                    try {
                        Object data = objectMapper.readValue((byte[]) value, Object.class);
                        order.put(column, data);
                    } catch (JsonProcessingException e) {
                    } catch (IOException ioException) {

                    }
                }
            }
        }
    }


    /**
     * 由于折扣立减信息存放在order items 里面的payments里面，把这些字段拆开为
     * discount_wosai_total
     * net_discount_wosai
     * discount_wosai_mch_total
     * net_discount_wosai_mch
     * hongbao_wosai_total
     * net_hongbao_wosai
     * hongbao_wosai_mch_total
     * net_hongbao_wosai_mch
     * discountChannel
     * discountChannelMch
     * discountChannelMchTopUp
     * hongbaoChannel
     * hongbaoChannelMch
     * hongbaoChannelMchTopUp
     * 如果这些字段有值，才添加相应字段， 早期收钱吧活动立减也需要把对应的金额转换为对应的字段
     */
    public static void expandOrderItemsPayments(Map<String, Object> orderEx) {
        Long hongbaoWosaiTotal = null;
        Long hongbaoWosaiMchTotal = null;
        Long discountWosaiTotal = null;
        Long discountWosaiMchTotal = null;
        Long netHongbaoWosai = null;
        Long netHongbaoWosaiMch = null;
        Long netDiscountWosai = null;
        Long netDiscountWosaiMch = null;

        Long netDiscountChannel = null;
        Long netDiscountChannelMch = null;
        Long netDiscountChannelMchTopUp = null;
        Long netHongbaoChannel = null;
        Long netHongbaoChannelMch = null;
        Long netHongbaoChannelMchTopUp = null;
        Long discountChannelTotal = null;
        Long discountChannelMchTotal = null;
        Long discountChannelMchTopUpTotal = null;
        Long hongbaoChannelTotal = null;
        Long hongbaoChannelMchTotal = null;
        Long hongbaoChannelMchTopUpTotal = null;
        String originType = null;
        List<Map<String, Object>> payments = CommonUtil.getListFromObject(BeanUtil.getNestedProperty(orderEx, Order.ITEMS + "." + Order.PAYMENTS));

        if (payments != null && payments.size() > 0) {
            for (Map payment : payments) {
                String type = BeanUtil.getPropString(payment, Payment.TYPE, "");
                long amountTotal = BeanUtil.getPropLong(payment, Payment.AMOUNT_TOTAL);
                long netAmount = BeanUtil.getPropLong(payment, Payment.NET_AMOUNT);
                switch (type) {
                    case Payment.TYPE_HONGBAO_WOSAI:
                        hongbaoWosaiTotal = (hongbaoWosaiTotal == null ? 0 : hongbaoWosaiTotal) + amountTotal;
                        netHongbaoWosai = (netHongbaoWosai == null ? 0 : netHongbaoWosai) + netAmount;
                        break;
                    case Payment.TYPE_HONGBAO_WOSAI_MCH:
                        hongbaoWosaiMchTotal = (hongbaoWosaiMchTotal == null ? 0 : hongbaoWosaiMchTotal) + amountTotal;
                        netHongbaoWosaiMch = (netHongbaoWosaiMch == null ? 0 : netHongbaoWosaiMch) + netAmount;
                        originType = BeanUtil.getPropString(payment, Payment.ORIGIN_TYPE);//woSai商户红包类型
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI:
                        discountWosaiTotal = (discountWosaiTotal == null ? 0 : discountWosaiTotal) + amountTotal;
                        netDiscountWosai = (netDiscountWosai == null ? 0 : netDiscountWosai) + netAmount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                        discountWosaiMchTotal = (discountWosaiMchTotal == null ? 0 : discountWosaiMchTotal) + amountTotal;
                        netDiscountWosaiMch = (netDiscountWosaiMch == null ? 0 : netDiscountWosaiMch) + netAmount;
                        originType = BeanUtil.getPropString(payment, Payment.ORIGIN_TYPE);//woSai商户折扣类型
                        break;
                    default:
                        break;
                }
            }
        } else {
            //处理早期收钱吧立减红包订单 在这里区分不了是红包还是立减了，全部当做是立减
            long originalTotal = BeanUtil.getPropLong(orderEx, Order.ORIGINAL_TOTAL);
            long netOriginal = BeanUtil.getPropLong(orderEx, Order.NET_ORIGINAL);
            long effectiveTotal = BeanUtil.getPropLong(orderEx, Order.EFFECTIVE_TOTAL);
            long netEffective = BeanUtil.getPropLong(orderEx, Order.NET_EFFECTIVE);

            if (originalTotal - effectiveTotal > 0) {
                discountWosaiTotal = 0 + (originalTotal - effectiveTotal);
                netDiscountWosai = 0 + (netOriginal - netEffective);
            }
        }

        // item.channel_payments 只会在微信和支付宝直连时才会设置，没有设置时，取payment(transaction.extra_out_fields.payment)信息
        List<Map<String, Object>> extraPayments = CommonUtil.getListFromObject(BeanUtil.getNestedProperty(orderEx, Order.ITEMS + "." + Order.CHANNEL_PAYMENTS));
        if (extraPayments == null || extraPayments.size() == 0) {
            extraPayments = CommonUtil.getListFromObject(BeanUtil.getNestedProperty(orderEx, Order.PAYMENTS));
            if (null != extraPayments && extraPayments.size() > 0) {
                for (Map<String, Object> payment : extraPayments) {
                    payment.put(Payment.NET_AMOUNT, payment.get(Payment.AMOUNT));
                }
            }
        }
        if (extraPayments != null && extraPayments.size() > 0) {
            for (Map payment : extraPayments) {
                String type = BeanUtil.getPropString(payment, Payment.TYPE, "");
                long amountTotal = BeanUtil.getPropLong(payment, Payment.AMOUNT_TOTAL);
                long netAmount = BeanUtil.getPropLong(payment, Payment.NET_AMOUNT);
                switch (type) {
                    case Payment.TYPE_DISCOUNT_CHANNEL:
                        netDiscountChannel = (netDiscountChannel == null ? 0 : netDiscountChannel) + netAmount;
                        discountChannelTotal = (discountChannelTotal == null ? 0 : discountChannelTotal) + amountTotal;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH:
                        netDiscountChannelMch = (netDiscountChannelMch == null ? 0 : netDiscountChannelMch) + netAmount;
                        discountChannelMchTotal = (discountChannelMchTotal == null ? 0 : discountChannelMchTotal) + amountTotal;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP:
                        netDiscountChannelMchTopUp = (netDiscountChannelMchTopUp == null ? 0 : netDiscountChannelMchTopUp) + netAmount;
                        discountChannelMchTopUpTotal = (discountChannelMchTopUpTotal == null ? 0 : discountChannelMchTopUpTotal) + amountTotal;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL:
                        netHongbaoChannel = (netHongbaoChannel == null ? 0 : netHongbaoChannel) + netAmount;
                        hongbaoChannelTotal = (hongbaoChannelTotal == null ? 0 : hongbaoChannelTotal) + amountTotal;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH:
                        netHongbaoChannelMch = (netHongbaoChannelMch == null ? 0 : netHongbaoChannelMch) + netAmount;
                        hongbaoChannelMchTotal = (hongbaoChannelMchTotal == null ? 0 : hongbaoChannelMchTotal) + amountTotal;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH_TOP_UP:
                        netHongbaoChannelMchTopUp = (netHongbaoChannelMchTopUp == null ? 0 : netHongbaoChannelMchTopUp) + netAmount;
                        hongbaoChannelMchTopUpTotal = (hongbaoChannelMchTopUpTotal == null ? 0 : hongbaoChannelMchTopUpTotal) + amountTotal;
                        break;
                    default:
                        break;
                }
            }
        }

        if (hongbaoWosaiTotal != null) {
            orderEx.put(Order.HONGBAO_WOSAI_TOTAL, hongbaoWosaiTotal);
        }
        if (netHongbaoWosai != null) {
            orderEx.put(Order.NET_HONGBAO_WOSAI, netHongbaoWosai);
        }
        if (hongbaoWosaiMchTotal != null) {
            orderEx.put(Order.HONGBAO_WOSAI_MCH_TOTAL, hongbaoWosaiMchTotal);
            if (!StringUtils.isEmpty(originType)) {
                orderEx.put(Transaction.DISCOUNT_WOSAI_MCH_TYPE, originType);
            }
        }
        if (netHongbaoWosaiMch != null) {
            orderEx.put(Order.NET_HONGBAO_WOSAI_MCH, netHongbaoWosaiMch);
        }
        if (discountWosaiTotal != null) {
            orderEx.put(Order.DISCOUNT_WOSAI_TOTAL, discountWosaiTotal);
        }
        if (netDiscountWosai != null) {
            orderEx.put(Order.NET_DISCOUNT_WOSAI, netDiscountWosai);
        }
        if (discountWosaiMchTotal != null) {
            orderEx.put(Order.DISCOUNT_WOSAI_MCH_TOTAL, discountWosaiMchTotal);
            if (!StringUtils.isEmpty(originType)) {
                orderEx.put(Transaction.DISCOUNT_WOSAI_MCH_TYPE, originType);
            }
        }
        if (netDiscountWosaiMch != null) {
            orderEx.put(Order.NET_DISCOUNT_WOSAI_MCH, netDiscountWosaiMch);
        }
        if (netDiscountChannel != null) {
            orderEx.put(Order.NET_DISCOUNT_CHANNEL, netDiscountChannel);
        }
        if (netDiscountChannelMch != null) {
            orderEx.put(Order.NET_DISCOUNT_CHANNEL_MCH, netDiscountChannelMch);
        }
        if (netDiscountChannelMchTopUp != null) {
            orderEx.put(Order.NET_DISCOUNT_CHANNEL_MCH_TOP_UP, netDiscountChannelMchTopUp);
        }
        if (netHongbaoChannel != null) {
            orderEx.put(Order.NET_HONGBAO_CHANNEL, netHongbaoChannel);
        }
        if (netHongbaoChannelMch != null) {
            orderEx.put(Order.NET_HONGBAO_CHANNEL_MCH, netHongbaoChannelMch);
        }
        if (netHongbaoChannelMchTopUp != null) {
            orderEx.put(Order.NET_DISCOUNT_CHANNEL_MCH_TOP_UP, netHongbaoChannelMchTopUp);
        }
        if (discountChannelTotal != null) {
            orderEx.put(Order.DISCOUNT_CHANNEL_TOTAL, discountChannelTotal);
        }
        if (discountChannelMchTotal != null) {
            orderEx.put(Order.DISCOUNT_CHANNEL_MCH_TOTAL, discountChannelMchTotal);
        }
        if (discountChannelMchTopUpTotal != null) {
            orderEx.put(Order.DISCOUNT_CHANNEL_MCH_TOP_UP_TOTAL, discountChannelMchTopUpTotal);
        }
        if (hongbaoChannelTotal != null) {
            orderEx.put(Order.HONGBAO_CHANNEL_TOTAL, hongbaoChannelTotal);
        }
        if (hongbaoChannelMchTotal != null) {
            orderEx.put(Order.HONGBAO_CHANNEL_MCH_TOTAL, hongbaoChannelMchTotal);
        }
        if (hongbaoChannelMchTopUpTotal != null) {
            orderEx.put(Order.HONGBAO_CHANNEL_MCH_TOP_UP_TOTAL, hongbaoChannelMchTopUpTotal);
        }

    }

    public static LinkedHashMap<String, String> providerMap;
    public static LinkedHashMap<String, String> subPaywayMap;
    public static LinkedHashMap<String, String> orderStatusMap;
    public static LinkedHashMap<String, String> transactionStatusMap;
    public static LinkedHashMap<String, String> transactionTypeMap;
    public static LinkedHashMap<String, String> orderExportColumnMap;
    public static LinkedHashMap<String, String> orderExportColumnMap2;
    public static LinkedHashMap<String, String> terminalTypeMap;


    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(3);
        map.put("1001", "兴业银行");
        map.put("1002", "拉卡拉");
        map.put("1013", "拉卡拉");
        map.put("1014", "拉卡拉");
        map.put("1015", "上海兴业");
        map.put("1016", "拉卡拉");
        map.put("1017", "拉卡拉");
        map.put("1018", "银联商务");
        map.put("1019", "银联");
        map.put("1020", "通联");
        map.put("1021", "澳门极易付");
        map.put("1022", "招商银行");
        map.put("1023", "邮储银行");
        map.put("1024", "广发银行");
        map.put("1026", "建设银行");
        map.put("1028", "华夏银行");
        map.put("1029", "杭州兴业");
        map.put("1030", "工商银行");
        map.put("1033", "拉卡拉银联");
        providerMap = map;
    }


    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(4);
        map.put("1", "B扫C");
        map.put("2", "C扫B");
        map.put("3", "WAP支付");
        map.put("4", "小程序支付");
        subPaywayMap = map;
    }


    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(32);
        map.put("0", "待支付");
        map.put("1200", "已支付");
        map.put("1300", "已关闭");
        map.put("1501", "支付失败");
        map.put("2201", "已全额退款");
        map.put("2100", "退款中");
        map.put("2210", "已部分退款");
        map.put("2501", "退款失败");
        map.put("3100", "撤单中");
        map.put("3201", "已撤单");
        map.put("3501", "撤单失败");
        map.put("1", "预授权-进行中");
        map.put("4200", "预授权-成功");
        map.put("4300", "预授权-失败后撤销(结果为失败)");
        map.put("4501", "预授权-失败(结果未知，可能成功，也有可能失败)");
        map.put("5100", "预授权撤销-进行中");
        map.put("5201", "预授权撤销-成功");
        map.put("5501", "预授权撤销-失败");
        map.put("6100", "预授权完成-进行中");
        map.put("6201", "预授权完成-成功");
        map.put("6501", "预授权完成-失败");
        map.put("7100", "预授权完成撤销-进行中");
        map.put("7201", "预授权完成撤销-成功");
        map.put("7501", "预授权完成撤销-失败");

        orderStatusMap = map;
    }


    /**
     * 新支付网关交易状态表
     */
    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(14);
        map.put("0", "处理中");
        map.put("2000", "成功");
        map.put("2001", "失败");
        map.put("2110", "失败");
        map.put("2101", "失败");
        map.put("2102", "失败");
        map.put("2103", "失败");
        map.put("2104", "失败");
        map.put("2105", "失败");
        map.put("2106", "失败");
        map.put("2107", "失败");
        map.put("2108", "失败");
        map.put("2109", "失败");
        map.put("1001", "处理中");
        map.put("1002", "失败");
        transactionStatusMap = map;
    }

    
    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(6);
        map.put("10", "Android应用");
        map.put("11", "iOS应用");
        map.put("20", "Windows桌面应用");
        map.put("30", "专用设备");
        map.put("40", "门店码");
        map.put("50", "服务");
        terminalTypeMap = map;
    }

    /**
     * 导出订单字段
     */
    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
        map.put("day", "日期");
        map.put("time", "时间");
        map.put("sn", "商户订单号");
        map.put("trade_no", "收款通道订单号");
        map.put("client_sn", "商户内部订单号");
        map.put("status", "订单状态");
        map.put("original_total", "订单金额");
        map.put("discount", "优惠减免");
        map.put("net_original", "实收金额");
        map.put("terminal_name", "终端名称");
        map.put("terminal_sn", "终端编号");
        map.put("store_name", "门店名称");
        map.put("store_sn", "门店编号");
        map.put("merchant_name", "商户名称");
        map.put("merchant_sn", "商户编号");
        map.put("payway", "收款通道");
        map.put("sub_payway", "交易模式");
        map.put("buyer_login", "付款账户");
        map.put("liquidation_next_day", "是否试用");
        map.put("fee_rate", "扣率");
        map.put("fee", "手续费");
        map.put("operator", "操作员");
        map.put("provider", "支付通道");

        orderExportColumnMap = map;
    }

    /**
     * 导出订单字段
     */
    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>();
        map.put("day", "日期");
        map.put("time", "时间");
        map.put("sn", "商户订单号");
        map.put("trade_no", "收款通道订单号");
        map.put("client_sn", "商户内部订单号");
        map.put("status", "订单状态");
        map.put("original_total", "订单金额");
        map.put("discount", "优惠减免");
        map.put("net_original", "实收金额");
        map.put("terminal_name", "终端名称");
        map.put("terminal_sn", "终端号");
        map.put("terminal_type", "终端类型");
        map.put("terminal_device_fingerprint", "设备号");
        map.put("store_name", "门店名称");
        map.put("store_sn", "门店号");
        map.put("merchant_name", "商户名称");
        map.put("merchant_sn", "商户号");
        map.put("payway", "收款通道");
        map.put("sub_payway", "交易模式");
        map.put("buyer_login", "付款账户");
        map.put("liquidation_next_day", "是否试用");
        map.put("fee_rate", "扣率");
        map.put("fee", "手续费");
        map.put("operator", "操作员");
        map.put("operator_name", "收银员");

        orderExportColumnMap2 = map;
    }

    public static String getProviderDesc(Object provider) {
        if (provider == null) {
            return null;
        } else {
            return providerMap.get(provider.toString());
        }
    }

    public static String getPaywayDesc(Object payway) {
        if (payway == null) {
            return null;
        } else {
            return paywayMap.get(payway.toString());
        }
    }

    public static String getPaywayDesc(Object payway, String language) {
        return getPaywayDesc(payway, null, language);
    }


    public static String getPaywayDesc(Object payway, String currency, String language) {
        if (payway == null) {
            return null;
        } else {
            /**TransactionUtil.filterOverseasPayway()中已经完成对支付方式的转换**/
//            if("HKD".equals(currency)&&Integer.parseInt(payway.toString())==1){
//                payway=99;
//            }
            return paywayLangugeMap.get(payway + language);
        }
    }

    public static String getSubPaywayDesc(Object subPayway) {
        if (subPayway == null) {
            return null;
        } else {
            return subPaywayMap.get(subPayway.toString());
        }
    }

    public static String getOrderStatusDesc(Object orderStatus) {
        if (orderStatus == null) {
            return null;
        } else {
            return orderStatusMap.get(orderStatus.toString());
        }
    }

    public static String getTransactionStatusDesc(Object transactionStatus) {
        if (transactionStatus == null) {
            return null;
        } else {
            return transactionStatusMap.get(transactionStatus.toString());
        }
    }

    public static String getTransactionStatusLanguageKey(Object transactionStatus) {
        Map<String, String> map = CollectionUtil.hashMap(
                "成功", TRANSACTION_STATUS_SUCCESS,
                "失败", TRANSACTION_STATUS_FAILURE,
                "处理中", TRANSACTION_STATUS_IN_PROCESS
        );
        String cnName = getTransactionStatusDesc(transactionStatus);
        return map.get(cnName);
    }

    public static String getTerminallTypeDesc(Object type) {
        if (type == null) {
            return null;
        } else {
            return terminalTypeMap.get(type.toString());
        }
    }

    public static String getTerminalTypeLanguageKey(Object type) {
        Map<String, String> map = CollectionUtil.hashMap(
                "Android应用", ANDROID,
                "iOS应用", IOS,
                "Windows桌面应用", PC_SOFTWARE,
                "专用设备", PAYMENT_DEVICE,
                "门店码", STORE_QR_CODE,
                "服务", SERVICE
        );
        String cnName = getTerminallTypeDesc(type);
        return map.get(cnName);
    }

    public static LinkedHashMap<String, String> getPaywayMap() {
        return paywayMap;
    }

    public static void setPaywayMap(LinkedHashMap<String, String> paywayMap) {
        OrderUtil.paywayMap = paywayMap;
    }

    public static void expandSharingBooks(Map<String, Object> order, List<Map<String, Object>> transactionList, BusinessService businessService) {
        if(transactionList == null || transactionList.isEmpty()) {
            return;
        }
        List<Map<String, Object>> sumarrySharingBooks = new ArrayList<>();
        for (Map<String, Object> transaction : transactionList) {
            if (transaction == null) {
                continue;
            }
            int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
            transaction.put(LanguageUtil.PAY_FUND_CHANNEL, TransactionUtil.getPayChannel(transaction));
            TransactionUtil.expandTransactionItemsPayments(transaction, false);
            TransactionUtil.expandTransactionInfo(transaction);
            if (TransactionType.PAYMENT.getCode() == type ||
                    TransactionType.DEPOSIT_FREEZE.getCode() == type ||
                    TransactionType.DEPOSIT_CONSUME.getCode() == type ||
                    TransactionType.CHARGE.getCode() == type ||
                    TransactionType.ORDER_TAKE.getCode() == type ||
                    TransactionType.STORE_PAY.getCode() == type ||
                    TransactionType.STORE_IN.getCode() == type) {
                // 将订单中对应需要用到的字段存入order中，方便service层调用
                String tradeApp = MapUtils.getString(transaction, Transaction.TRADE_APP);
                String bizModel = MapUtils.getString(transaction, Transaction.SQB_BIZ_MODEL);
                String paySource = MapUtils.getString(transaction, Transaction.SQB_PAY_SOURCE);
                String payPath = MapUtils.getString(transaction, Transaction.SQB_PAY_PATH);
                order.put(Order.TRADE_APP, tradeApp);
                order.put(Order.SQB_BIZ_MODEL, bizModel);
                order.put(Order.SQB_PAY_SOURCE, paySource);
                order.put(Order.SQB_PAY_PATH, payPath);
            }
            TransactionUtils.expandMchDiscountOriginType(transaction);
            TransactionUtil.calculateExtendFields(transaction);
            TransactionUtil.expandSharingBooks(transaction, businessService);
            List<Map<String, Object>> sharingBookList = (List<Map<String, Object>>) MapUtil.getObject(transaction, com.wosai.upay.transaction.constant.CommonConstant.SHARING_BOOKS);
            if(sharingBookList == null || sharingBookList.isEmpty()) {
                continue;
            }
            if(TransactionType.DEPOSIT_CONSUME.getCode() != type 
                    && TransactionType.PAYMENT.getCode() != type 
                    && TransactionType.STORE_IN.getCode() != type
                    && TransactionType.STORE_PAY.getCode() != type) {
                for (Map<String, Object> sharingBook : sharingBookList) {
                    sharingBook.put(Payment.AMOUNT, -1 * MapUtil.getLongValue(sharingBook, Payment.AMOUNT));
                }
            }
            sumarrySharingBooks.addAll(sharingBookList);
        }

        // 按照分账业务名称进行分组
      //分账回退需要根据业务名称进行合并
        Map<String, Map<String, Object>> sharingBooks = new HashMap<String, Map<String, Object>>();
        sumarrySharingBooks.forEach(sbr ->{
            String alias = MapUtil.getString(sbr, Receiver.ALIAS);
            Map<String, Object> sharingBook = sharingBooks.get(alias);
            if(sharingBook == null) {
                sharingBooks.put(alias, sbr);
                return;
            }
            sharingBook.put(Payment.AMOUNT, MapUtil.getLongValue(sharingBook, Payment.AMOUNT) + MapUtil.getLongValue(sbr, Payment.AMOUNT) );
        });
        order.put(com.wosai.upay.transaction.constant.CommonConstant.SHARING_BOOKS, new ArrayList(sharingBooks.values()));
    }
}

