package com.wosai.upay.transaction.helper;


import com.ctrip.framework.apollo.Apollo;
import com.wosai.upay.common.log.LogstashMarkerAppendFileds;
import com.wosai.upay.common.log.MethodEndLog;
import com.wosai.upay.common.log.MethodStartLog;
import com.wosai.upay.common.util.SpringWebUtil;
import com.wosai.upay.core.exception.*;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.util.ApolloUtil;
import com.wosai.upay.transaction.util.JsonRpcCallUtilWrapper;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.jdbc.CannotGetJdbcConnectionException;
import org.springframework.jdbc.UncategorizedSQLException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class UpayServiceMethodInterceptor implements MethodInterceptor {

    public UpayServiceMethodInterceptor() {

    }

    private static final Logger logger = LoggerFactory.getLogger(UpayServiceMethodInterceptor.class);

    private static final String WALLET_CHANGELOG_HBASE_SERVICE_NAME = "com.wosai.upay.transaction.service.WalletChangeLogHBaseService";

    private static final String TRANSACTION_HBASE_SERVICE_NAME = "com.wosai.upay.transaction.service.TransactionHBaseService";

    private static final String CANNOT_SET_INCORRECT_VALUE_MATCHES = ".+Incorrect (.+) value: '(.+)' for column '(.+)'.+";
    private static final String CANNOT_SET_INCORRECT_VALUE = "$3属性不能被赋值为'$2'";

    private static final String DATA_OBJECT_NOT_EXISTS = "object id not exists.";

    private static final String UNKNOWN_COLUMN_MATCHES = ".+Unknown column '(.+)' in 'field list'.*";
    private static final String UNKNOWN_COLUMN = "未知属性'$1'";

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        //long before = 0;
        //if (logger.isTraceEnabled()) {
        long before = System.currentTimeMillis();
        MethodStartLog startLog = new MethodStartLog(invocation.getMethod(), invocation.getArguments(), JsonRpcCallUtilWrapper.getCallHeaderMapFromRequest(SpringWebUtil.getCurrentRequest()));
        if (ApolloUtil.getStartLogEnable()) {
            //apollo 控制打印开始日志
            logger.trace(LogstashMarkerAppendFileds.append(startLog), "invoking method start");
        }
        // logger.trace("invoking {}", new MethodInvocationFormatter(invocation));
        //}
        boolean success = false;
        Throwable tex = null;
        Object result = null;
        try {
            result = invocation.proceed();
            success = true;

            return result;
        } catch (ConstraintViolationException ex) {
            StringBuilder sb = new StringBuilder();
            for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append(violation.getMessage());
            }
            tex = new BizException(BizException.CODE_INVALID_PARAMETER, sb.toString(), ex);
            throw tex;
        } catch (CannotGetJdbcConnectionException ex) {
            tex = new CoreCannotGetJdbcConnectionException(CoreException.getCodeDesc(CoreException.CODE_CANNOT_GET_JDBC_CONNECTION), ex);
            throw tex;
        } catch (DuplicateKeyException ex) {
            tex = new CoreDatabaseDuplicateKeyException(CoreException.getCodeDesc(CoreException.CODE_DATABASE_DUPLICATE_KEY), ex);
            throw tex;
        } catch (BadSqlGrammarException ex) {
            if (ex.getMessage().matches(UNKNOWN_COLUMN_MATCHES)) {
                tex = new CoreDatabaseUnknownColumnException(ex.getMessage().replaceAll(UNKNOWN_COLUMN_MATCHES, UNKNOWN_COLUMN), ex);
            } else {
                tex = new CoreDataAccessException(CoreException.getCodeDesc(CoreException.CODE_DATA_ACCESS_EXCEPTION), ex);
            }
            throw tex;
        } catch (UncategorizedSQLException ex) {
            if (ex.getMessage().matches(CANNOT_SET_INCORRECT_VALUE_MATCHES)) {
                tex = new CoreDatabaseCannotSetIncorrectValueException(ex.getMessage().replaceAll(CANNOT_SET_INCORRECT_VALUE_MATCHES, CANNOT_SET_INCORRECT_VALUE), ex);
            } else {
                tex = new CoreDataAccessException(CoreException.getCodeDesc(CoreException.CODE_DATA_ACCESS_EXCEPTION), ex);
            }
            throw tex;
        } catch (DataAccessException ex) {
            tex = new CoreDataAccessException(CoreException.getCodeDesc(CoreException.CODE_DATA_ACCESS_EXCEPTION), ex);
            throw tex;
        } catch (Throwable ex) {
            tex = new BizException(BizException.CODE_UNKNOWN_ERROR, ex.getMessage(), ex);
            throw tex;
        } finally {
            //if (logger.isTraceEnabled()) {
            long duration = System.currentTimeMillis() - before;
            if (Objects.nonNull(tex)) {
                logger.error(LogstashMarkerAppendFileds.append(buildMethodEndLog(startLog, duration, success, result)), "invoking method end", tex);
            } else {
                String name = invocation.getMethod().getDeclaringClass().getName();
                if (StringUtils.isNotBlank(name) && (name.equals(WALLET_CHANGELOG_HBASE_SERVICE_NAME) || name.equals(TRANSACTION_HBASE_SERVICE_NAME))) {
                    logger.trace(LogstashMarkerAppendFileds.append(buildMethodEndLog(startLog, duration, success, null)), "invoking method end");
                } else {
                    logger.trace(LogstashMarkerAppendFileds.append(buildMethodEndLog(startLog, duration, success, result)), "invoking method end");
                }
            }
            //logger.trace("use {}ms and {} in {}", duration, success, new MethodInvocationFormatter(invocation));
            //}
        }
    }

    private MethodEndLog buildMethodEndLog(MethodStartLog startLog, long duration, boolean success, Object result) {
        MethodEndLog endLog = new MethodEndLog(startLog);
        endLog.setDuration(duration);
        endLog.setSuccess(success);
        endLog.setReturnType(null);
        endLog.setResult(result);
        endLog.setException(null);
        return endLog;
    }


}
