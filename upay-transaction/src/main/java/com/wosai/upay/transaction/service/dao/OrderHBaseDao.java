package com.wosai.upay.transaction.service.dao;

import com.google.common.collect.Lists;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.cashdesk.api.request.CashDeskTradeQueryListRequest;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.IHbaseConstant;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.service.dao.base.HBaseDao;
import com.wosai.upay.transaction.service.model.query.OrderHBaseQuery;
import com.wosai.upay.transaction.util.*;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.common.SolrInputDocument;
import org.apache.solr.common.params.ShardParams;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderHBaseDao extends HBaseDao<OrderHBaseQuery> {

    @Value("${solr.timeout:3000}")
    private Integer solrTimeout;

    public static final String TABLE_PREFIX = "upay:o_";

    private static final String COLLECTION_PREFIX = "upay_order_";

    @SuppressWarnings("unchecked")
    public OrderHBaseDao() {
        // 扫码交易配置
        if (!StringUtils.isEmpty(PropertyUtil.getProperty("hbase.o.tableName"))) {
            tableName = TableName.valueOf(PropertyUtil.getProperty("hbase.o.tableName"));
        }

        // 刷卡交易配置
        swipeTableName = TableName.valueOf(PropertyUtil.getProperty("hbase.swipe.o.tableName"));
        swipeCollections = Arrays.asList(PropertyUtil.getProperty("solr.swipe.o.collection"));
    }

    @SneakyThrows
    public void putRow(@NotNull String merchantId, @NotNull Long cTime, @NotNull String id, List<Three<String, Object, Class<?>>> columnValueTypeList) {
        if (!CollectionUtils.isEmpty(columnValueTypeList)) {
            Put put = new Put(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(cTime), Bytes.toBytes(id)));
            Map<String, Object> map = new HashedMap(columnValueTypeList.size());
            columnValueTypeList.stream().filter(value -> (value.fst != null && value.snd != null && value.third != null)).forEach(columnValueType -> {
                        map.put(columnValueType.fst, columnValueType.snd);
                        put.addColumn(FAMILY, columnValueType.fst.getBytes(), SolrUtils.convert2ByteArray(columnValueType.snd, columnValueType.third));
                    }
            );
            put.setTimestamp(MapUtils.getLongValue(map, DaoConstants.MTIME, System.currentTimeMillis()));
            SolrHBaseUtils.put(getTableName(new DateTime(cTime).toString(CommonConstant.DAY_SDF_PATTERN_YYYYMM)), put);

            SolrInputDocument solrInputFields = buildSolrInputDocument(merchantId, cTime, id, map);
            SolrHBaseUtils.putSolr(COLLECTION_PREFIX + new DateTime(cTime).toString(CommonConstant.DAY_SDF_PATTERN_YYYYMM), solrInputFields);
        }
    }

    private SolrInputDocument buildSolrInputDocument(@NotNull String merchantId, @NotNull Long cTime, @NotNull String id, Map<String, Object> map) {
        SolrInputDocument solrInputFields = new SolrInputDocument();
        solrInputFields.addField(DaoConstants.ID, String.valueOf(Hex.encodeHex(Bytes.add(Bytes.toBytes(merchantId), Bytes.toBytes(cTime), Bytes.toBytes(id)))));
        solrInputFields.addField(Order.SN, map.get(Order.SN));
        solrInputFields.addField(Order.MERCHANT_ID, map.get(Order.MERCHANT_ID));
        solrInputFields.addField(Order.STORE_ID, map.get(Order.STORE_ID));
        solrInputFields.addField(Order.TERMINAL_ID, map.get(Order.TERMINAL_ID));
        solrInputFields.addField(Order.CLIENT_SN, map.get(Order.CLIENT_SN));
        solrInputFields.addField(Order.ORIGINAL_TOTAL, map.get(Order.ORIGINAL_TOTAL));
        solrInputFields.addField(Order.TRADE_NO, map.get(Order.TRADE_NO));
        solrInputFields.addField(Order.PAYWAY, map.get(Order.PAYWAY));
        solrInputFields.addField(Order.SUB_PAYWAY, map.get(Order.SUB_PAYWAY));
        solrInputFields.addField(Order.PROVIDER, map.get(Order.PROVIDER));
        solrInputFields.addField(DaoConstants.CTIME, map.get(DaoConstants.CTIME));
        solrInputFields.addField(Order.STATUS, map.get(Order.STATUS));
        solrInputFields.addField(Order.BUYER_UID, map.get(Order.BUYER_UID));
        solrInputFields.addField(Order.BUYER_LOGIN, map.get(Order.BUYER_LOGIN));
        solrInputFields.addField(CommonConstant.UPDATE_VERSION_L, MapUtils.getLongValue(map, DaoConstants.MTIME, System.currentTimeMillis()) + 1);
        return solrInputFields;
    }


    @Override
    protected int getSolrTimeout(Integer solrTimeout) {
        return Optional.ofNullable(solrTimeout).orElse(this.solrTimeout);
    }

    @Override
    protected void where(SolrQuery solrQuery, OrderHBaseQuery query) {
        List<String> fq = Lists.newArrayList();
        List<String> q = Lists.newArrayList();
        if (StringUtils.hasLength(query.getOrderSn())) {
            q.add(SolrUtils.formatEqualFq(Order.SN, SolrUtils.escapeQueryChars(query.getOrderSn())));
        } else if (!CollectionUtils.isEmpty(query.getOrderSns())) {
            q.add(SolrUtils.formatInFq(Order.SN, SolrUtils.escapeQueryChars(query.getOrderSns())));
        }

        if (StringUtils.hasLength(query.getTradeNo())) {
            q.add(SolrUtils.formatEqualFq(Order.TRADE_NO, SolrUtils.escapeQueryChars(query.getTradeNo())));
        }

        // 门店id
        if (!CollectionUtils.isEmpty(query.getStoreIds())) {
            q.add(SolrUtils.formatInFq(Order.STORE_ID, SolrUtils.escapeQueryChars(query.getStoreIds())));
        }
        //商户id
        if (!CollectionUtils.isEmpty(query.getMerchantIds())) {
            q.add(SolrUtils.formatInFq(Order.MERCHANT_ID, SolrUtils.escapeQueryChars(query.getMerchantIds())));
            if (ApolloUtil.useSolrRoute(query.getMerchantIds().get(0), query.getStartTime()) 
                    && DBSelectContext.getContext().getSelectDb() != UpayQueryType.UPAY_SWIPE) {
                solrQuery.setParam(ShardParams._ROUTE_, query.getMerchantIds().stream().collect(Collectors.joining(",")));
            }
        }

        //终端id
        if (!CollectionUtils.isEmpty(query.getTerminalIds())) {
            q.add(SolrUtils.formatInFq(Order.TERMINAL_ID, SolrUtils.escapeQueryChars(query.getTerminalIds())));
        }


        if (StringUtils.hasLength(query.getClientSn())) {
            q.add(SolrUtils.formatEqualFq(Order.CLIENT_SN, SolrUtils.escapeQueryChars(query.getClientSn())));
        }

        if (!CollectionUtils.isEmpty(query.getProviders())) {
            q.add(SolrUtils.formatInFq(Order.PROVIDER, query.getProviders()));
        }

        if (!CollectionUtils.isEmpty(query.getStatusList())) {
            q.add(SolrUtils.formatInFq(Order.STATUS, query.getStatusList()));
        }

        if (!CollectionUtils.isEmpty(query.getPayWays())) {
            q.add(SolrUtils.formatInFq(Order.PAYWAY, query.getPayWays()));
        }

        if (!CollectionUtils.isEmpty(query.getSubPayWays())) {
            q.add(SolrUtils.formatInFq(Order.SUB_PAYWAY, query.getSubPayWays()));
        }

        if (!Objects.isNull(query.getStartTime()) || !Objects.isNull(query.getEndTime())) {
            q.add(SolrUtils.formatRangeFq(DaoConstants.CTIME, query.getStartTime(), query.getEndTime(), false));
        }

        if (!Objects.isNull(query.getMinTotalAmount()) || !Objects.isNull(query.getMaxTotalAmount())) {
            q.add(SolrUtils.formatRangeFq(Order.ORIGINAL_TOTAL, query.getMinTotalAmount(), query.getMaxTotalAmount(), true));
        }

        if (!CollectionUtils.isEmpty(query.getBuyerLogins())) {
            q.add(SolrUtils.formatInFq(Order.BUYER_LOGIN, SolrUtils.escapeQueryChars(query.getBuyerLogins())));
        }
        if (!CollectionUtils.isEmpty(query.getBuyerUids())) {
            q.add(SolrUtils.formatInFq(Order.BUYER_UID, SolrUtils.escapeQueryChars(query.getBuyerUids())));
        }
        if (query.getProviderIsNull()) {
            q.add(SolrUtils.formatNullFq(Order.PROVIDER));
        }
        queryStrategy(solrQuery, fq, q, query.getStartTime());

    }

    @Override
    protected String getTableNamePrefix() {
        return TABLE_PREFIX;
    }

    @Override
    protected String getSolrCollectionPrefix() {
        return COLLECTION_PREFIX;
    }

    @Override
    public Map<String, Object> buildEntryMap(Result r, Map<String, Object> initRow) {
        Map<String, Object> row = new HashMap<>(initRow);
        if (!CollectionUtils.isEmpty(row)) {
            row.replace(DaoConstants.ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ID)));
            row.replace(Order.SN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.SN)));
            row.replace(Order.CLIENT_SN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.CLIENT_SN)));
            row.replace(Order.SUBJECT, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.SUBJECT)));
            row.replace(Order.BODY, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BODY)));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.STATUS)).ifPresent(m ->
                    row.replace(Order.STATUS, null, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.ORIGINAL_TOTAL)).ifPresent(m ->
                    row.replace(Order.ORIGINAL_TOTAL, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.NET_ORIGINAL)).ifPresent(m ->
                    row.replace(Order.NET_ORIGINAL, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.EFFECTIVE_TOTAL)).ifPresent(m ->
                    row.replace(Order.EFFECTIVE_TOTAL, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.NET_EFFECTIVE)).ifPresent(m ->
                    row.replace(Order.NET_EFFECTIVE, null, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.TOTAL_DISCOUNT)).ifPresent(m ->
                    row.replace(Order.TOTAL_DISCOUNT, null, Bytes.toLong(m))
            );

            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.NET_DISCOUNT)).ifPresent(m ->
                    row.replace(Order.NET_DISCOUNT, null, Bytes.toLong(m))
            );

            row.replace(Order.BUYER_UID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_UID)));
            row.replace(Order.BUYER_LOGIN, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_LOGIN)));
            row.replace(Order.MERCHANT_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.MERCHANT_ID)));
            row.replace(Order.TERMINAL_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TERMINAL_ID)));
            row.replace(Order.STORE_ID, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.STORE_ID)));
            row.replace(Order.OPERATOR, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.OPERATOR)));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PROVIDER)).ifPresent(m ->
                    row.replace(Order.PROVIDER, null, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAY_WAY)).ifPresent(m ->
                    row.replace(Order.PAYWAY, null, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.SUB_PAYWAY)).ifPresent(m ->
                    row.replace(Order.SUB_PAYWAY, null, Bytes.toInt(m))
            );
            row.replace(Order.TRADE_NO, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TRADE_NO)));
            row.replace(Order.REFLECT, null, reflectService.convertReflect(Bytes.toString(r.getValue(FAMILY, IHbaseConstant.REFLECT))));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CTIME)).ifPresent(m ->
                    row.replace(DaoConstants.CTIME, null, Bytes.toLong(m))
            );

            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.MTIME)).ifPresent(m ->
                    row.replace(DaoConstants.MTIME, null, Bytes.toLong(m))
            );
            row.replace(Order.NFC_CARD, null, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.NFC_CARD)));
            row.replace(Order.ITEMS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.ITEMS)));
            row.replace(Order.NET_ITEMS, null, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.NET_ITEMS)));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.TCP_MODIFIED)).ifPresent(m ->
                    row.replace(Order.TCP_MODIFIED, null, Bytes.toInt(m))
            );


        } else {
            row.put(DaoConstants.ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.ID)));
            row.put(Order.SN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.SN)));
            row.put(Order.CLIENT_SN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.CLIENT_SN)));
            row.put(Order.SUBJECT, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.SUBJECT)));
            row.put(Order.BODY, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BODY)));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.STATUS)).ifPresent(m ->
                    row.put(Order.STATUS, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.ORIGINAL_TOTAL)).ifPresent(m ->
                    row.put(Order.ORIGINAL_TOTAL, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.NET_ORIGINAL)).ifPresent(m ->
                    row.put(Order.NET_ORIGINAL, Bytes.toLong(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.EFFECTIVE_TOTAL)).ifPresent(m ->
                    row.put(Order.EFFECTIVE_TOTAL, Bytes.toLong(m))
            );

            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.NET_EFFECTIVE)).ifPresent(m ->
                    row.put(Order.NET_EFFECTIVE, Bytes.toLong(m))
            );
            row.put(Order.TOTAL_DISCOUNT, null);
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.TOTAL_DISCOUNT)).ifPresent(m ->
                    row.put(Order.TOTAL_DISCOUNT, Bytes.toLong(m))
            );

            row.put(Order.NET_DISCOUNT, null);
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.NET_DISCOUNT)).ifPresent(m ->
                    row.put(Order.NET_DISCOUNT, Bytes.toLong(m))
            );

            row.put(Order.BUYER_UID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_UID)));
            row.put(Order.BUYER_LOGIN, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.BUYER_LOGIN)));
            row.put(Order.MERCHANT_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.MERCHANT_ID)));
            row.put(Order.TERMINAL_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TERMINAL_ID)));
            row.put(Order.STORE_ID, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.STORE_ID)));
            row.put(Order.OPERATOR, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.OPERATOR)));
            row.put(Order.PROVIDER, null);
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PROVIDER)).ifPresent(m ->
                    row.put(Order.PROVIDER, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.PAY_WAY)).ifPresent(m ->
                    row.put(Order.PAYWAY, Bytes.toInt(m))
            );
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.SUB_PAYWAY)).ifPresent(m ->
                    row.put(Order.SUB_PAYWAY, Bytes.toInt(m))
            );
            row.put(Order.TRADE_NO, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.TRADE_NO)));
            row.put(Order.REFLECT, reflectService.convertReflect(Bytes.toString(r.getValue(FAMILY, IHbaseConstant.REFLECT))));
            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.CTIME)).ifPresent(m ->
                    row.put(DaoConstants.CTIME, Bytes.toLong(m))
            );

            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.MTIME)).ifPresent(m ->
                    row.put(DaoConstants.MTIME, Bytes.toLong(m))
            );
            row.put(Order.NFC_CARD, Bytes.toString(r.getValue(FAMILY, IHbaseConstant.NFC_CARD)));
            row.put(Order.ITEMS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.ITEMS)));
            row.put(Order.NET_ITEMS, SolrUtils.bytes2Object(r.getValue(FAMILY, IHbaseConstant.NET_ITEMS)));

            if (Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.TCP_MODIFIED)).isPresent()) {
                row.put(Order.TCP_MODIFIED, Bytes.toInt(r.getValue(FAMILY, IHbaseConstant.TCP_MODIFIED)) == 1);
            } else {
                row.put(Order.TCP_MODIFIED, false);
            }


            if (Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.DELETED)).isPresent()) {
                row.put(DaoConstants.DELETED, Bytes.toInt(r.getValue(FAMILY, IHbaseConstant.DELETED)) == 1);
            } else {
                row.put(DaoConstants.DELETED, false);
            }

            Optional.ofNullable(r.getValue(FAMILY, IHbaseConstant.VERSION)).ifPresent(m ->
                    row.put(DaoConstants.VERSION, Bytes.toLong(m))
            );

        }

        return row;
    }

    @Override
    protected void esWhere(BoolQueryBuilder esQuery, OrderHBaseQuery query) {
        if (StringUtils.hasLength(query.getOrderSn())) {
            esQuery.must(QueryBuilders.matchQuery(Order.SN, query.getOrderSn()));
        } else if (!CollectionUtils.isEmpty(query.getOrderSns())) {
            esQuery.must(buildESInQuery(Order.SN, query.getOrderSns()));
        }

        if (StringUtils.hasLength(query.getTradeNo())) {
            esQuery.must(QueryBuilders.matchQuery(Order.TRADE_NO, query.getTradeNo()));
        }

        // 门店id
        if (!CollectionUtils.isEmpty(query.getStoreIds())) {
            esQuery.must(buildESInQuery(Order.STORE_ID, query.getStoreIds()));
        }
        //商户id
        if (!CollectionUtils.isEmpty(query.getMerchantIds())) {
            esQuery.must(buildESInQuery(Order.MERCHANT_ID, query.getMerchantIds()));
        }

        //终端id
        if (!CollectionUtils.isEmpty(query.getTerminalIds())) {
            esQuery.must(buildESInQuery(Order.TERMINAL_ID, query.getTerminalIds()));
        }


        if (StringUtils.hasLength(query.getClientSn())) {
            esQuery.must(QueryBuilders.matchQuery(Order.CLIENT_SN, query.getClientSn()));
        }

        if (!CollectionUtils.isEmpty(query.getProviders())) {
            esQuery.must(buildESInQuery(Order.PROVIDER, query.getProviders()));
        }

        if (!CollectionUtils.isEmpty(query.getStatusList())) {
            esQuery.must(buildESInQuery(Order.STATUS, query.getStatusList()));
        }

        if (!CollectionUtils.isEmpty(query.getPayWays())) {
            esQuery.must(buildESInQuery(Order.PAYWAY, query.getPayWays()));
        }

        if (!CollectionUtils.isEmpty(query.getSubPayWays())) {
            esQuery.must(buildESInQuery(Order.SUB_PAYWAY, query.getSubPayWays()));
        }

        if (!Objects.isNull(query.getStartTime()) || !Objects.isNull(query.getEndTime())) {
            buildESRangeQuery(DaoConstants.CTIME, query.getStartTime(), query.getEndTime(), false);
        }

        if (!Objects.isNull(query.getMinTotalAmount()) || !Objects.isNull(query.getMaxTotalAmount())) {
            buildESRangeQuery(Order.ORIGINAL_TOTAL, query.getMinTotalAmount(), query.getMaxTotalAmount(), true);
        }

        if (!CollectionUtils.isEmpty(query.getBuyerLogins())) {
            esQuery.must(buildESInQuery(Order.BUYER_LOGIN, query.getBuyerLogins()));
        }
        if (!CollectionUtils.isEmpty(query.getBuyerUids())) {
            esQuery.must(buildESInQuery(Order.BUYER_UID, query.getBuyerUids()));
        }
        if (query.getProviderIsNull()) {
            esQuery.mustNot(QueryBuilders.existsQuery(Order.PROVIDER));
        }
    }

    @Override
    protected void buildCashDeskRequest(CashDeskTradeQueryListRequest request, OrderHBaseQuery query, BusinessService businessService) {
        throw new BizException(BizException.CODE_BIZ_EXCEPTION, "不支持收银台查询");
    }
}
