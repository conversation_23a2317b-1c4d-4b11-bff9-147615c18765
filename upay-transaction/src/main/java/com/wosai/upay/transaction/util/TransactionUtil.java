package com.wosai.upay.transaction.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.wosai.common.utils.transaction.TransactionEnhanceFields;
import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.Receiver;
import com.wosai.profit.sharing.model.ReceiverType;
import com.wosai.profit.sharing.model.upay.ProfitSharing;
import com.wosai.profit.sharing.util.UpayProfitSharingUtil;
import com.wosai.upay.core.meta.Provider;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.TransactionConstant;
import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.model.*;
import com.wosai.upay.transaction.service.BusinessService;
import org.apache.commons.collections.MapUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.transaction.enums.SubPayWayType.SUB_PAYWAY_BARCODE;
import static com.wosai.upay.transaction.model.Transaction.*;

/**
 * Created by jianfree on 30/12/16.
 */
public class TransactionUtil {

    /**
     * 支付宝所有的支付源
     */
    private static final Set<Integer> ALIPAY_ALL_PAYWAY = Sets.newHashSet(1, 2, 20, 98, 99);
    public static final long DEFAULT_ENTPAY_QUERY_EXPIRE_TIME = 24 * 60 * 60 * 1000; // 微企付超时时间为24小时

    public static final Logger logger = LoggerFactory.getLogger(TransactionUtil.class);
    public static ObjectMapper objectMapper = new ObjectMapper();

    public static void expandTransactionItemTradeInfo(Map<String, Object> transaction) {
        Object extraOutField = MapUtil.getObject(transaction, Transaction.EXTRA_OUT_FIELDS);
        Map extraOut = CommonUtil.getMapFromJsonObject(extraOutField);
        Map overseas = MapUtil.getMap(extraOut, Transaction.OVERSEAS);
        String bankTradeNo = MapUtil.getString(extraOut, Transaction.TRADE_NO, "");
        transaction.put(Transaction.BANK_TRADE_NO, bankTradeNo);
        String channelTradeNo = getChannelTradeNo(transaction);
        transaction.put(Transaction.EXTRA_CHANNEL_TRADE_NO, channelTradeNo);
        Map configSnapshot = null;
        try {
            configSnapshot = (Map) MapUtil.getObject(transaction, Transaction.CONFIG_SNAPSHOT);
        } catch (Exception e) {
            logger.error("transfer transaction config_snapshot to map exception: [{}]", e);
        }
        Map<String, Object> tradeInfo = TransactionUtil.getTradeConfigInfoByConfigSnapshot(configSnapshot);
        transaction.put(TransactionParam.APP_AUTH_SHOP_ID, MapUtil.getString(tradeInfo, TransactionParam.APP_AUTH_SHOP_ID));
        transaction.put(TransactionParam.FEE_RATE, MapUtil.getString(tradeInfo, TransactionParam.FEE_RATE));
        transaction.put(TransactionParam.LIQUIDATION_NEXT_DAY, MapUtil.getBooleanValue(tradeInfo, TransactionParam.LIQUIDATION_NEXT_DAY));
        String merchantDefaultCurrency = MapUtil.getString(configSnapshot, TransactionParam.CURRENCY, "CNY");
        transaction.put(Transaction.MERCHANT_DEFAULT_CURRENCY, merchantDefaultCurrency); //商户基础信息中设置的交易币种
        transaction.put(Transaction.CURRENCY, merchantDefaultCurrency);
        transaction.put(Transaction.PAYMENT_INST, MapUtil.getString(overseas, Transaction.PAYMENT_INST, "CN"));
        transaction.put(Transaction.PAYWAY, rewriteOverseasPayway(transaction));
        if (!StringUtil.empty(MapUtil.getString(tradeInfo, TransactionParam.FEE))) {
            transaction.put(TransactionParam.FEE, MapUtil.getLongValue(tradeInfo, TransactionParam.FEE));
        }
        if (!StringUtil.empty(MapUtil.getString(tradeInfo, TransactionParam.FEE_RATE_ORIGINAL))) {
            transaction.put(TransactionParam.FEE_RATE_ORIGINAL, MapUtil.getString(tradeInfo, TransactionParam.FEE_RATE_ORIGINAL));
        }
        transaction.put(Transaction.PROVIDER_MCH_ID, MapUtil.getString(tradeInfo, TransactionParam.PROVIDER_MCH_ID));
        transaction.remove(Transaction.CONFIG_SNAPSHOT);

    }

    public static void calculateExtendFields(Map orderEx) {
        int status = MapUtil.getIntValue(orderEx, Transaction.STATUS);
        int type = MapUtil.getIntValue(orderEx, Transaction.TYPE);
        Long actualReceiveAmount = 0L;
        Long clearingAmount = 0L;
        Long mchFavorableAmount = 0L;
        Long wosaiFavorableAmount = 0L;
        Long channelFavorableAmount = 0L;
        Long channelAgentFavorableAmount = 0L;
        Long channelMchFavorableAmount = 0L;
        Long channelMchTopUpFavorableAmount = 0L;
        Long sharingAmount = 0L;
        Long tradeServiceSharingAmount = 0L;
        Long hbfqMchDiscountAmount = MapUtil.getLongValue(orderEx, Transaction.FQ_MCH_DISCOUNT_AMOUNT, 0);
        Long notRestituteSharingAmount = 0L;
        //预授权 预授权撤销 不计算
        if (status == Transaction.STATUS_SUCCESS && (Transaction.TYPE_DEPOSIT_FREEZE != type && Transaction.TYPE_DEPOSIT_CANCEL != type)) {
            Long originalAmount = MapUtil.getLongValue(orderEx, Transaction.ORIGINAL_AMOUNT);
            Long effectiveAmount = MapUtil.getLongValue(orderEx, Transaction.EFFECTIVE_AMOUNT);
            Long paidAmount = MapUtil.getLongValue(orderEx, Transaction.PAID_AMOUNT);

            Long hongbaoWosaiMchAmount = MapUtil.getLongValue(orderEx, Transaction.HONGBAO_WOSAI_MCH_AMOUNT);
            Long discountWosaiMchAmount = MapUtil.getLongValue(orderEx, Transaction.DISCOUNT_WOSAI_MCH_AMOUNT);

            Long discountChannelAmount = MapUtil.getLongValue(orderEx, Transaction.DISCOUNT_CHANNEL_AMOUNT);
            Long discountChannelMchAmount = MapUtil.getLongValue(orderEx, Transaction.DISCOUNT_CHANNEL_MCH_AMOUNT);
            Long discountChannelMchTopUpAmount = MapUtil.getLongValue(orderEx, Transaction.DISCOUNT_CHANNEL_MCH_TOP_UP_AMOUNT);
            Long hongbaoChannelAmount = MapUtil.getLongValue(orderEx, Transaction.HONGBAO_CHANNEL_AMOUNT);
            Long hongbaoChannelMchAmount = MapUtil.getLongValue(orderEx, Transaction.HONGBAO_CHANNEL_MCH_AMOUNT);
            Long hongbaoChannelMchTopUpAmount = MapUtil.getLongValue(orderEx, Transaction.HONGBAO_CHANNEL_MCH_TOP_UP_AMOUNT);

            //商户优惠
            mchFavorableAmount = OrderOrTransAmountUtil.getMerchantFavorableAmount(hongbaoWosaiMchAmount, discountWosaiMchAmount);
            //收钱吧优惠
            wosaiFavorableAmount = OrderOrTransAmountUtil.getWosaiFavorableAmount(originalAmount, effectiveAmount, mchFavorableAmount);

            //收款通道机构优惠
            channelAgentFavorableAmount = OrderOrTransAmountUtil.getChannelAgentFavorableAmount(hongbaoChannelAmount, discountChannelAmount);
            //收款通道商户免充值优惠
            channelMchFavorableAmount = OrderOrTransAmountUtil.getChannelMchFavorableAmount(discountChannelMchAmount, hongbaoChannelMchAmount);
            //收款通道商户充值优惠
            channelMchTopUpFavorableAmount = OrderOrTransAmountUtil.getChannelMchTopUpFavorableAmount(discountChannelMchTopUpAmount, hongbaoChannelMchTopUpAmount);
            //收款通道优惠
            channelFavorableAmount = OrderOrTransAmountUtil.getChannelFavorableAmount(channelAgentFavorableAmount, channelMchFavorableAmount, channelMchTopUpFavorableAmount);
            //实收金额
            actualReceiveAmount = OrderOrTransAmountUtil.getActualReceiveAmount(originalAmount, mchFavorableAmount, channelMchFavorableAmount);

            Long fee = MapUtil.getLongValue(orderEx, Transaction.FEE);

            //兼容支付网关费率计算的问题，original_amount 和 effective_amount 为0时，手续费为0
            if (originalAmount == 0 && effectiveAmount == 0) {
                fee = 0l;
                orderEx.put(Transaction.FEE, fee);
            }

            //如果支付网关trade_config 没有记录手续费，由(实收金额 * 费率)计算得出
            if (StringUtil.empty(MapUtil.getString(orderEx, Transaction.FEE))) {
                double feeRate = 0.0;
                String feeRateString = MapUtil.getString(orderEx, TransactionParam.FEE_RATE, "0.0");
                if (feeRateString.trim() != "") {
                    feeRate = CommonUtil.formatMoney(feeRateString, 3);
                }
                fee = Math.round(actualReceiveAmount * feeRate * 0.01);
                orderEx.put(Transaction.FEE, fee);
            }
            if (!StringUtil.empty(MapUtil.getString(orderEx, TransactionParam.FEE_ORIGINAL))) {
                String originalFeeRate = MapUtil.getString(orderEx, TransactionParam.FEE_RATE_ORIGINAL, "0.0");
                orderEx.put(TransactionParam.FEE_ORIGINAL, Math.round(actualReceiveAmount * CommonUtil.formatMoney(originalFeeRate, 3) * 0.01));
            }
            //结算金额
            clearingAmount = OrderOrTransAmountUtil.getClearingAmount(actualReceiveAmount, fee);
            //分账标识
            Integer sharingFlag = MapUtil.getIntValue(orderEx, TransactionEnhanceFields.SHARING_FLAG.getField(), 0);
            Map profitSharing = MapUtil.getMap(MapUtil.getMap(orderEx, "extra_params", Maps.newTreeMap()), "profit_sharing");
            // 花呗商户贴息
            boolean isHbfqMchDiscount = false;
            String productFlag = MapUtil.getString(orderEx, Transaction.PRODUCT_FLAG);
            if(!StringUtil.empty(productFlag) && (productFlag.contains(TransactionConstant.PRODUCT_FLAG_HBFQ_DISCOUNT))) {
                isHbfqMchDiscount = true;
                orderEx.put(Transaction.FQ_TYPE, Transaction.FQ_TYPE_SELLER);
            }
            List<Map<String, Object>> sharingBookMapList = new ArrayList<>();
            if (Objects.equals(sharingFlag, 1) && MapUtil.isNotEmpty(profitSharing)) {
                boolean useRound = UpayProfitSharingUtil.useRound(MapUtil.getIntValue(orderEx, Order.PAYWAY), MapUtil.getInteger(orderEx, Order.PROVIDER));
                if (TransactionType.PAYMENT_TYPES.contains(type)) {
                    sharingAmount = UpayProfitSharingUtil.getSharingPayAmountByProfitSharing(profitSharing, clearingAmount, fee, useRound);
                    if(isHbfqMchDiscount) {
                        hbfqMchDiscountAmount = getProductSharingPayAmountByProfitSharing(profitSharing, clearingAmount, fee, useRound, TransactionConstant.PRODUCT_FLAG_HBFQ_DISCOUNT);
                        orderEx.put(TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE, hbfqMchDiscountAmount);
                    }
                    sharingBookMapList = UpayProfitSharingUtil.calculateSharingPayAmount(profitSharing, clearingAmount, fee, useRound);
                } else if (TransactionType.REFUND_TYPES.contains(type)) {
                    Long payClearingAmount = MapUtil.getLong(profitSharing, ProfitSharing.SHARING_PAY_CLEARING_AMOUNT);
                    if (payClearingAmount != null) {
                        sharingAmount = UpayProfitSharingUtil.getSharingRestituteAmountByProfitSharing(profitSharing, clearingAmount, fee, payClearingAmount, useRound);
                        if(isHbfqMchDiscount) {
                            hbfqMchDiscountAmount = getProductSharingRestituteAmountByProfitSharing(profitSharing, clearingAmount, fee, payClearingAmount, useRound, TransactionConstant.PRODUCT_FLAG_HBFQ_DISCOUNT);
                            orderEx.put(TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE, hbfqMchDiscountAmount);
                            notRestituteSharingAmount = getNotRestituteAmountByProfitSharing(profitSharing, clearingAmount, fee, payClearingAmount, useRound);
                        }
                        sharingBookMapList = UpayProfitSharingUtil.calculateSharingRestituteAmount(profitSharing, clearingAmount, fee, payClearingAmount, useRound);
                    } else { // 兼容历史数据
                        sharingAmount = UpayProfitSharingUtil.getSharingPayAmountByProfitSharing(profitSharing, clearingAmount, fee, useRound);
                        sharingBookMapList = UpayProfitSharingUtil.calculateSharingPayAmount(profitSharing, clearingAmount, fee, useRound);
                    }
                }
            }
            //根据sharingBookMapList计算技术服务费
            Set<String> tradeServiceReceiverIds = ApolloUtil.getSharingProfitMapping().keySet();
            for (Map<String, Object> item : sharingBookMapList) {
                String receiverId = MapUtil.getString(item, ProfitSharing.RECEIVER_ID);
                if (tradeServiceReceiverIds.contains(receiverId)) {
                    long amount = MapUtil.getLongValue(item, ProfitSharing.RECEIVER_AMOUNT);
                    tradeServiceSharingAmount += amount;
                }
            }
        }

        orderEx.put(Transaction.MCH_FAVORABLE_AMOUNT, mchFavorableAmount);
        orderEx.put(Transaction.WOSAI_FAVORABLE_AMOUNT, wosaiFavorableAmount);
        orderEx.put(Transaction.CHANNEL_FAVORABLE_AMOUNT, channelFavorableAmount);
        orderEx.put(Transaction.CHANNEL_AGENT_FAVORABLE_AMOUNT, channelAgentFavorableAmount);
        orderEx.put(Transaction.CHANNEL_MCH_FAVORABLE_AMOUNT, channelMchFavorableAmount);
        orderEx.put(Transaction.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT, channelMchTopUpFavorableAmount);
        orderEx.put(Transaction.ACTUAL_RECEIVE_AMOUNT, actualReceiveAmount);
        orderEx.put(Transaction.SHARING_AMOUNT, sharingAmount - tradeServiceSharingAmount);
        orderEx.put(Transaction.TRADE_SERVICE_SHARING_AMOUNT, tradeServiceSharingAmount);
        orderEx.put(Transaction.FQ_MCH_DISCOUNT_AMOUNT, hbfqMchDiscountAmount);
        orderEx.put(Transaction.NOT_RESTITUTE_SHARING_AMOUNT, notRestituteSharingAmount);
        orderEx.put(Transaction.CLEARING_AMOUNT, clearingAmount - sharingAmount - notRestituteSharingAmount);


    }

    public static void transformReflect(Map<String, Object> transaction) {
        Object reflect = MapUtil.getObject(transaction, Transaction.REFLECT);
        if (reflect instanceof byte[]) {
            String reflectString = new String((byte[]) reflect);
            if ("null".equals(reflectString) || "\"\"".equals(reflectString)) {
                reflectString = null;
            }
            transaction.put(Transaction.REFLECT, reflectString);
        }
    }

    public static void jsonFormatOrder(Map<String, Object> order) {
        List<String> jsonBlobColumnList = Lists.newArrayList(
                Transaction.ITEMS,
                Transaction.EXTRA_PARAMS,
                Transaction.EXTRA_OUT_FIELDS,
                Transaction.EXTENDED_PARAMS,
                Transaction.CONFIG_SNAPSHOT,
                Transaction.REFLECT,
                Transaction.BIZ_ERROR_CODE,
                Transaction.PROVIDER_ERROR_INFO
        );
        //预授权不做反json
        Object reflect = order.get(Transaction.REFLECT);
        if ((reflect instanceof String) && org.apache.commons.lang3.StringUtils.contains((String) reflect, Transaction.REFLECT_DEPOSIT_TYPE)) {
            jsonBlobColumnList.remove(Transaction.REFLECT);
        }
        jsonBlobColumnList.parallelStream().forEach(column -> {
            Object value = order.get(column);
            if (!(value instanceof Map) && value != null) {
                Object data = null;
                try {
                    if (value instanceof byte[]) {
                        data = objectMapper.readValue((byte[]) value, Object.class);
                    } else if (value instanceof String) {
                        try {
                            data = objectMapper.readValue((String) value, Object.class);
                            // ObjectMapper会将"4.9 ***** *****" 转换为 4.9，转换后部分数据会缺失，需要做特殊校验
                            if (data instanceof Number && !((String) value).equals(data.toString())) {
                                data = value;
                            }
                        } catch (Exception ex) {
                            data = value;
                        }
                    } else {
                        data = objectMapper.readValue(value.toString(), Object.class);
                    }
                } catch (JsonProcessingException e) {
                } catch (IOException ioException) {
                } catch (ClassCastException classCastException) {
                    logger.error("catch exception json format order: id = " + MapUtil.getString(order, DaoConstants.ID) + " column = " + column, classCastException);
                }

                order.put(column, data);
            }
        });
    }

    /**
     * 由于折扣立减信息存放在transaction items extra_out_fields 里面的payments里面，把这些字段拆开为
     * discount_wosai_amount
     * discount_wosai_mch_amount
     * hongbao_wosai_amount
     * hongbao_wosai_mch_amount
     * discount_channel_amount
     * discount_channel_mch_amount
     * discount_channel_mch_top_up_amount
     * hongbao_channel_amount
     * hongbao_channel_mch_amount
     * hongbao_channel_mch_top_up_amount
     * payments 支付明细原样返回
     * 如果这些字段有值，才添加相应字段， 早期收钱吧活动立减也需要把对应的金额转换为对应的字段
     */
    public static void expandTransactionItemsPayments(Map tran, boolean removeExtraOutFields) {
        Long hongbaoWosaiAmount = null;
        Long hongbaoWosaiMchAmount = null;
        Long discountWosaiAmount = null;
        Long discountWosaiMchAmount = null;

        Long discountChannelAmount = null;
        Long discountChannelMchAmount = null;
        Long discountChannelMchTopUpAmount = null;
        Long hongbaoChannelAmount = null;
        Long hongbaoChannelMchAmount = null;
        Long hongbaoChannelMchTopUpAmount = null;
        String originType = null;
        List<Map<String, Object>> allPayments = null;

        List<Map<String, Object>> payments = CommonUtil.getListFromObject(BeanUtil.getNestedProperty(tran, Transaction.ITEMS + "." + Transaction.PAYMENTS));

        if (payments != null && payments.size() > 0) {
            if(allPayments == null){
                allPayments = new ArrayList<>(payments);
            }else{
                allPayments.addAll(payments);
            }
            for (Map payment : payments) {
                String type = MapUtil.getString(payment, Payment.TYPE, "");
                long amount = MapUtil.getLongValue(payment, Payment.AMOUNT);
                switch (type) {
                    case Payment.TYPE_HONGBAO_WOSAI:
                        hongbaoWosaiAmount = hongbaoWosaiAmount == null ? 0 : hongbaoWosaiAmount;
                        hongbaoWosaiAmount += amount;
                        break;
                    case Payment.TYPE_HONGBAO_WOSAI_MCH:
                        hongbaoWosaiMchAmount = hongbaoWosaiMchAmount == null ? 0 : hongbaoWosaiMchAmount;
                        hongbaoWosaiMchAmount += amount;
                        originType = MapUtil.getString(payment, Payment.ORIGIN_TYPE);//woSai商户红包类型
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI:
                        discountWosaiAmount = discountWosaiAmount == null ? 0 : discountWosaiAmount;
                        discountWosaiAmount += amount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                        discountWosaiMchAmount = discountWosaiMchAmount == null ? 0 : discountWosaiMchAmount;
                        discountWosaiMchAmount += amount;
                        originType = MapUtil.getString(payment, Payment.ORIGIN_TYPE);//woSai商户折扣类型
                        break;
                    default:
                        break;
                }
            }
        } else {
            //处理早期收钱吧立减红包订单 在这里区分不了是红包还是立减了，全部当做是立减
            long originalAmount = MapUtil.getLongValue(tran, Transaction.ORIGINAL_AMOUNT);
            long effectiveAmount = MapUtil.getLongValue(tran, Transaction.EFFECTIVE_AMOUNT);

            if (originalAmount - effectiveAmount > 0) {
                discountWosaiAmount = 0 + (originalAmount - effectiveAmount);
            }
        }

        List<Map<String,Object>> extraPayments = CommonUtil.getListFromObject(BeanUtil.getNestedProperty(tran, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.PAYMENTS));
        if (removeExtraOutFields) {
            tran.remove(Transaction.EXTRA_OUT_FIELDS);
        }
        if (extraPayments != null && extraPayments.size() > 0) {
            if(allPayments == null){
                allPayments = new ArrayList<>(extraPayments);
            }else{
                allPayments.addAll(extraPayments);
            }
            for (Map payment : extraPayments) {
                String type = MapUtil.getString(payment, Payment.TYPE, "");
                long amount = MapUtil.getLongValue(payment, Payment.AMOUNT);
                switch (type) {
                    case Payment.TYPE_DISCOUNT_CHANNEL:
                        discountChannelAmount = discountChannelAmount == null ? 0 : discountChannelAmount;
                        discountChannelAmount += amount;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH:
                        discountChannelMchAmount = discountChannelMchAmount == null ? 0 : discountChannelMchAmount;
                        discountChannelMchAmount += amount;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP:
                        discountChannelMchTopUpAmount = discountChannelMchTopUpAmount == null ? 0 : discountChannelMchTopUpAmount;
                        discountChannelMchTopUpAmount += amount;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL:
                        hongbaoChannelAmount = hongbaoChannelAmount == null ? 0 : hongbaoChannelAmount;
                        hongbaoChannelAmount += amount;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH:
                        hongbaoChannelMchAmount = hongbaoChannelMchAmount == null ? 0 : hongbaoChannelMchAmount;
                        hongbaoChannelMchAmount += amount;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH_TOP_UP:
                        hongbaoChannelMchTopUpAmount = hongbaoChannelMchTopUpAmount == null ? 0 : hongbaoChannelMchTopUpAmount;
                        hongbaoChannelMchTopUpAmount += amount;
                        break;
                    default:
                        break;
                }
            }
        }
        if(allPayments != null){
            tran.put(Transaction.PAYMENTS, allPayments);
        }

        if (hongbaoWosaiAmount != null) {
            tran.put(Transaction.HONGBAO_WOSAI_AMOUNT, hongbaoWosaiAmount);
        }
        if (hongbaoWosaiMchAmount != null) {
            tran.put(Transaction.HONGBAO_WOSAI_MCH_AMOUNT, hongbaoWosaiMchAmount);
            if (!StringUtils.isEmpty(originType)) {
                tran.put(Transaction.DISCOUNT_WOSAI_MCH_TYPE, originType);
            }
        }
        if (discountWosaiAmount != null) {
            tran.put(Transaction.DISCOUNT_WOSAI_AMOUNT, discountWosaiAmount);
        }
        if (discountWosaiMchAmount != null) {
            tran.put(Transaction.DISCOUNT_WOSAI_MCH_AMOUNT, discountWosaiMchAmount);
            if (!StringUtils.isEmpty(originType)) {
                tran.put(Transaction.DISCOUNT_WOSAI_MCH_TYPE, originType);
            }
        }
        if (discountChannelAmount != null) {
            tran.put(Transaction.DISCOUNT_CHANNEL_AMOUNT, discountChannelAmount);
        }
        if (discountChannelMchAmount != null) {
            tran.put(Transaction.DISCOUNT_CHANNEL_MCH_AMOUNT, discountChannelMchAmount);
        }
        if (discountChannelMchTopUpAmount != null) {
            tran.put(Transaction.DISCOUNT_CHANNEL_MCH_TOP_UP_AMOUNT, discountChannelMchTopUpAmount);
        }
        if (hongbaoChannelAmount != null) {
            tran.put(Transaction.HONGBAO_CHANNEL_AMOUNT, hongbaoChannelAmount);
        }
        if (hongbaoChannelMchAmount != null) {
            tran.put(Transaction.HONGBAO_CHANNEL_MCH_AMOUNT, hongbaoChannelMchAmount);
        }
        if (hongbaoChannelMchTopUpAmount != null) {
            tran.put(Transaction.HONGBAO_CHANNEL_MCH_TOP_UP_AMOUNT, hongbaoChannelMchTopUpAmount);
        }

    }

    /**
     * 获取费率、微信sub_appid以及是否待结算信息
     *
     * @param configSnapshot
     * @return
     */
    public static Map<String, Object> getTradeConfigInfoByConfigSnapshot(Object configSnapshot) {
        if (configSnapshot == null) {
            return null;
        } else {
            Map snapshot = CommonUtil.getMapFromJsonObject(configSnapshot);
            Map tradeParams = getTradeConfigFromConfigSnapshot(snapshot);
            boolean liquidationNextDay = MapUtil.getBooleanValue(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, false);
            String currency = MapUtil.getString(snapshot, TransactionParam.CURRENCY, "CNY");

            String feeRateString = MapUtil.getString(tradeParams, TransactionParam.FEE_RATE, "0.0");
            if (feeRateString.trim().equals("")) {
                feeRateString = "0.0";
            }
            String wexinSubAppid = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_APP_ID, Strings.EMPTY);
            String wexinMiniSubAppid = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_MINI_SUB_APP_ID, Strings.EMPTY);
            Map tradeConfig = CollectionUtil.hashMap(
                    TransactionParam.FEE_RATE, feeRateString,
                    TransactionParam.LIQUIDATION_NEXT_DAY, liquidationNextDay,
                    TransactionParam.CURRENCY, currency,
                    TransactionParam.WEIXIN_SUB_APP_ID, wexinSubAppid,
                    TransactionParam.WEIXIN_MINI_SUB_APP_ID, wexinMiniSubAppid
            );
            String providerMchId = MapUtil.getString(tradeParams, TransactionParam.PROVIDER_MCH_ID);
            if (providerMchId != null){
                tradeConfig.put(TransactionParam.PROVIDER_MCH_ID, providerMchId);
            }
            if (!StringUtil.empty(MapUtil.getString(tradeParams, TransactionParam.FEE))) {
                tradeConfig.put(TransactionParam.FEE, MapUtil.getLongValue(tradeParams, TransactionParam.FEE));
            }
            if (!StringUtil.empty(MapUtil.getString(tradeParams, TransactionParam.FEE_RATE_ORIGINAL))) {
                tradeConfig.put(TransactionParam.FEE_RATE_ORIGINAL, MapUtil.getString(tradeParams, TransactionParam.FEE_RATE_ORIGINAL));
            }
            return tradeConfig;
        }
    }

    /**
     * 取得交易参数
     *
     * @param configSnapshot
     * @return
     */
    private static Map getTradeConfigFromConfigSnapshot(Map<String, Object> configSnapshot) {
        if (configSnapshot == null) {
            return null;
        }
        for (String key : configSnapshot.keySet()) {
            Object value = configSnapshot.get(key);
            if (value instanceof Map) {
                if (MapUtil.getBooleanValue((Map)value, TransactionParam.ACTIVE, false)) {
                    return (Map) value;
                }
            }
        }
        return null;
    }

    /**
     * 去除交易参数里面的敏感信息
     *
     * @param configSnapshot
     */
    public static void removeConfigSnapshotSensetiveInfo(Map<String, Object> configSnapshot) {
        for (String key : configSnapshot.keySet()) {
            Object value = configSnapshot.get(key);
            if (value instanceof String && key.contains("key")) {
                configSnapshot.put(key, "*");
            } else if (value instanceof Map) {
                removeConfigSnapshotSensetiveInfo((Map<String, Object>) value);
            }
        }
    }

    /**
     * 获取错误信息
     *
     * @param errorInfo
     * @return
     */
    public static String getErrorMessage(Map<String, Object> errorInfo) {
        if (!CollectionUtils.isEmpty(errorInfo)) {
            for (String key : errorInfo.keySet()) {
                Object value = errorInfo.get(key);
                if (value instanceof Map) {
                    String msg = MapUtil.getString((Map) value, "message", "");
                    if (StringUtils.isEmpty(msg)) {
                        continue;
                    }
                    String standardName = MapUtil.getString((Map) value, "standardName", "");
                    if (!StringUtils.isEmpty(standardName)) {
                        return String.format("%s[%s]", msg, standardName);
                    } else {
                        return msg;
                    }

                }
            }
        }

        return "";
    }


    /**
     * 根据流水相关信息重写支付方式
     *
     * @param transaction
     * @return 98	支付宝香港--本地	overseas.currency == "HKD"
     * 19   微信香港--本地   overseas.currency == "HKD"
     */
    private static int rewriteOverseasPayway(Map transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        Map<String, Object> extraOutField = (Map<String, Object>) MapUtil.getObject(transaction, Transaction.EXTRA_OUT_FIELDS);
        Map overseas = MapUtil.getMap(extraOutField, Transaction.OVERSEAS);
        if (MapUtil.isNotEmpty(overseas)) {
            String currency = MapUtil.getString(transaction, Transaction.CURRENCY);
            String actualPayCurrency = MapUtil.getString(overseas, TransactionParam.CURRENCY);
            if (StatementObjectConfig.CURRENCY_HKD.equals(currency) && StatementObjectConfig.CURRENCY_HKD.equals(actualPayCurrency)) {
                if (payway == StatementObjectConfig.ALIPAY_OLD) {
                    payway = StatementObjectConfig.ALIPAY_HK_LOCAL;
                } else if (payway == StatementObjectConfig.WEIXIN) {
                    payway = StatementObjectConfig.WEIXIN_HK;
                }
            }
        }
        return payway;
    }


    /**
     * 由于折扣立减信息存放在order items 里面的payments里面，把这些字段拆开为
     * discount_wosai_total
     * net_discount_wosai
     * discount_wosai_mch_total
     * net_discount_wosai_mch
     * hongbao_wosai_total
     * net_hongbao_wosai
     * hongbao_wosai_mch_total
     * net_hongbao_wosai_mch
     * discountChannel
     * discountChannelMch
     * discountChannelMchTopUp
     * hongbaoChannel
     * hongbaoChannelMch
     * hongbaoChannelMchTopUp
     * 如果这些字段有值，才添加相应字段， 早期收钱吧活动立减也需要把对应的金额转换为对应的字段
     */
    public static void expandTransactionItemsPayments(Map<String, Object> orderEx) {
        Long hongbaoWosaiTotal = null;
        Long hongbaoWosaiMchTotal = null;
        Long discountWosaiTotal = null;
        Long discountWosaiMchTotal = null;
        Long netHongbaoWosai = null;
        Long netHongbaoWosaiMch = null;
        Long netDiscountWosai = null;
        Long netDiscountWosaiMch = null;

        Long netDiscountChannel = null;
        Long netDiscountChannelMch = null;
        Long netDiscountChannelMchTopUp = null;
        Long netHongbaoChannel = null;
        Long netHongbaoChannelMch = null;
        Long netHongbaoChannelMchTopUp = null;
        Long discountChannelTotal = null;
        Long discountChannelMchTotal = null;
        Long discountChannelMchTopUpTotal = null;
        Long hongbaoChannelTotal = null;
        Long hongbaoChannelMchTotal = null;
        Long hongbaoChannelMchTopUpTotal = null;

        List<Map<String, Object>> payments = CommonUtil.getListFromObject(BeanUtil.getNestedProperty(orderEx, Order.ITEMS + "." + Order.PAYMENTS));

        if (payments != null && payments.size() > 0) {
            for (Map payment : payments) {
                String type = MapUtil.getString(payment, Payment.TYPE, "");
                long amountTotal = MapUtil.getLongValue(payment, Payment.AMOUNT_TOTAL);
                long netAmount = MapUtil.getLongValue(payment, Payment.NET_AMOUNT);
                switch (type) {
                    case Payment.TYPE_HONGBAO_WOSAI:
                        hongbaoWosaiTotal = 0 + amountTotal;
                        netHongbaoWosai = 0 + netAmount;
                        break;
                    case Payment.TYPE_HONGBAO_WOSAI_MCH:
                        hongbaoWosaiMchTotal = 0 + amountTotal;
                        netHongbaoWosaiMch = 0 + netAmount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI:
                        discountWosaiTotal = 0 + amountTotal;
                        netDiscountWosai = 0 + netAmount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                        discountWosaiMchTotal = 0 + amountTotal;
                        netDiscountWosaiMch = 0 + netAmount;
                        break;
                    default:
                        break;
                }
            }
        } else {
            //处理早期收钱吧立减红包订单 在这里区分不了是红包还是立减了，全部当做是立减
            long originalTotal = MapUtil.getLongValue(orderEx, Order.ORIGINAL_TOTAL);
            long netOriginal = MapUtil.getLongValue(orderEx, Order.NET_ORIGINAL);
            long effectiveTotal = MapUtil.getLongValue(orderEx, Order.EFFECTIVE_TOTAL);
            long netEffective = MapUtil.getLongValue(orderEx, Order.NET_EFFECTIVE);

            if (originalTotal - effectiveTotal > 0) {
                discountWosaiTotal = 0 + (originalTotal - effectiveTotal);
                netDiscountWosai = 0 + (netOriginal - netEffective);
            }
        }

        // item.channel_payments 只会在微信和支付宝直连时才会设置，没有设置时，取payment(transaction.extra_out_fields.payment)信息
        List<Map<String, Object>> extraPayments = CommonUtil.getListFromObject(BeanUtil.getNestedProperty(orderEx, Order.ITEMS + "." + Order.CHANNEL_PAYMENTS));
        if (extraPayments == null || extraPayments.size() == 0) {
            extraPayments = CommonUtil.getListFromObject(MapUtil.getObject(orderEx, Order.PAYMENTS));
            if (null != extraPayments && extraPayments.size() > 0) {
                for (Map<String, Object> payment : extraPayments) {
                    payment.put(Payment.NET_AMOUNT, payment.get(Payment.AMOUNT));
                }
            }
        }
        if (extraPayments != null && extraPayments.size() > 0) {
            for (Map payment : extraPayments) {
                String type = MapUtil.getString(payment, Payment.TYPE, "");
                long amountTotal = MapUtil.getLongValue(payment, Payment.AMOUNT_TOTAL);
                long netAmount = MapUtil.getLongValue(payment, Payment.NET_AMOUNT);
                switch (type) {
                    case Payment.TYPE_DISCOUNT_CHANNEL:
                        netDiscountChannel = 0 + netAmount;
                        discountChannelTotal = 0 + amountTotal;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH:
                        netDiscountChannelMch = 0 + netAmount;
                        discountChannelMchTotal = 0 + amountTotal;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP:
                        netDiscountChannelMchTopUp = 0 + netAmount;
                        discountChannelMchTopUpTotal = 0 + amountTotal;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL:
                        netHongbaoChannel = 0 + netAmount;
                        hongbaoChannelTotal = 0 + amountTotal;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH:
                        netHongbaoChannelMch = 0 + netAmount;
                        hongbaoChannelMchTotal = 0 + amountTotal;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH_TOP_UP:
                        netHongbaoChannelMchTopUp = 0 + netAmount;
                        hongbaoChannelMchTopUpTotal = 0 + amountTotal;
                        break;
                    default:
                        break;
                }
            }
        }

        if (hongbaoWosaiTotal != null) {
            orderEx.put(Order.HONGBAO_WOSAI_TOTAL, hongbaoWosaiTotal);
        }
        if (netHongbaoWosai != null) {
            orderEx.put(Order.NET_HONGBAO_WOSAI, netHongbaoWosai);
        }
        if (hongbaoWosaiMchTotal != null) {
            orderEx.put(Order.HONGBAO_WOSAI_MCH_TOTAL, hongbaoWosaiMchTotal);
        }
        if (netHongbaoWosaiMch != null) {
            orderEx.put(Order.NET_HONGBAO_WOSAI_MCH, netHongbaoWosaiMch);
        }
        if (discountWosaiTotal != null) {
            orderEx.put(Order.DISCOUNT_WOSAI_TOTAL, discountWosaiTotal);
        }
        if (netDiscountWosai != null) {
            orderEx.put(Order.NET_DISCOUNT_WOSAI, netDiscountWosai);
        }
        if (discountWosaiMchTotal != null) {
            orderEx.put(Order.DISCOUNT_WOSAI_MCH_TOTAL, discountWosaiMchTotal);
        }
        if (netDiscountWosaiMch != null) {
            orderEx.put(Order.NET_DISCOUNT_WOSAI_MCH, netDiscountWosaiMch);
        }
        if (netDiscountChannel != null) {
            orderEx.put(Order.NET_DISCOUNT_CHANNEL, netDiscountChannel);
        }
        if (netDiscountChannelMch != null) {
            orderEx.put(Order.NET_DISCOUNT_CHANNEL_MCH, netDiscountChannelMch);
        }
        if (netDiscountChannelMchTopUp != null) {
            orderEx.put(Order.NET_DISCOUNT_CHANNEL_MCH_TOP_UP, netDiscountChannelMchTopUp);
        }
        if (netHongbaoChannel != null) {
            orderEx.put(Order.NET_HONGBAO_CHANNEL, netHongbaoChannel);
        }
        if (netHongbaoChannelMch != null) {
            orderEx.put(Order.NET_HONGBAO_CHANNEL_MCH, netHongbaoChannelMch);
        }
        if (netHongbaoChannelMchTopUp != null) {
            orderEx.put(Order.NET_DISCOUNT_CHANNEL_MCH_TOP_UP, netHongbaoChannelMchTopUp);
        }
        if (discountChannelTotal != null) {
            orderEx.put(Order.DISCOUNT_CHANNEL_TOTAL, discountChannelTotal);
        }
        if (discountChannelMchTotal != null) {
            orderEx.put(Order.DISCOUNT_CHANNEL_MCH_TOTAL, discountChannelMchTotal);
        }
        if (discountChannelMchTopUpTotal != null) {
            orderEx.put(Order.DISCOUNT_CHANNEL_MCH_TOP_UP_TOTAL, discountChannelMchTopUpTotal);
        }
        if (hongbaoChannelTotal != null) {
            orderEx.put(Order.HONGBAO_CHANNEL_TOTAL, hongbaoChannelTotal);
        }
        if (hongbaoChannelMchTotal != null) {
            orderEx.put(Order.HONGBAO_CHANNEL_MCH_TOTAL, hongbaoChannelMchTotal);
        }
        if (hongbaoChannelMchTopUpTotal != null) {
            orderEx.put(Order.HONGBAO_CHANNEL_MCH_TOP_UP_TOTAL, hongbaoChannelMchTopUpTotal);
        }

    }

    /**
     * 流水添加费率，是否待结算等信息
     *
     * @param transaction
     */
    public static void expandTransactionInfo(Map<String, Object> transaction) {

        Map<String, Object> extraOutField = (Map<String, Object>) MapUtil.getObject(transaction, Transaction.EXTRA_OUT_FIELDS);
        String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG, "");
        List payments = (List) MapUtil.getObject(extraOutField, Transaction.PAYMENTS);
        Map overseas = MapUtil.getMap(extraOutField, Transaction.OVERSEAS);
        String payType = getUnionPayType(payments);
        transaction.put(Transaction.PAY_TYPE, payType);
        transaction.put(Transaction.EXTRA_CHANNEL_TRADE_NO, getChannelTradeNo(transaction));
        transaction.put(Transaction.BATCH_BILL_NO, MapUtil.getString(extraOutField, Transaction.BATCH_BILL_NO));
        transaction.put(Transaction.SYS_TRACE_NO, MapUtil.getString(extraOutField, Transaction.SYS_TRACE_NO));
        transaction.put(Transaction.BANK_TYPE, MapUtil.getString(extraOutField, Transaction.BANK_TYPE));
        transaction.put(Transaction.IS_FIX, MapUtil.getBoolean(extraOutField, Transaction.IS_FIX, false));
        //先从overseas 取，没有的话，从extraOut取。。。。。
        String actualPayCurrency = MapUtil.getString(overseas, TransactionParam.CURRENCY);
        if (StringUtils.isEmpty(actualPayCurrency)) {
            actualPayCurrency = MapUtil.getString(extraOutField, TransactionParam.CURRENCY);
        }
        if (StringUtils.isEmpty(actualPayCurrency)) {
            actualPayCurrency = StatementObjectConfig.CURRENCY_CNY;
        }
        CommonUtil.putToMapIfValueNotEmpty(transaction, Transaction.COMBO_ID, MapUtil.getString(extraOutField, Transaction.COMBO_ID));
        Map<String, Object> tradeInfo = TransactionUtil.getTradeConfigInfoByConfigSnapshot(transaction.get(Transaction.CONFIG_SNAPSHOT));
        transaction.put(TransactionParam.FEE_RATE, MapUtil.getString(tradeInfo, TransactionParam.FEE_RATE));
        transaction.put(TransactionParam.CURRENCY, MapUtil.getString(tradeInfo, TransactionParam.CURRENCY, "CNY"));
        transaction.put(Transaction.PAYWAY, rewriteOverseasPayway(transaction));
        transaction.put("actual_pay_currency", actualPayCurrency);
        transaction.put(TransactionParam.LIQUIDATION_NEXT_DAY, MapUtil.getBooleanValue(tradeInfo, TransactionParam.LIQUIDATION_NEXT_DAY));
        if (!StringUtil.empty(MapUtil.getString(tradeInfo, TransactionParam.FEE))) {
            transaction.put(TransactionParam.FEE, MapUtil.getLongValue(tradeInfo, TransactionParam.FEE));
        }
        if (!StringUtil.empty(MapUtil.getString(tradeInfo, TransactionParam.FEE_RATE_ORIGINAL))) {
            transaction.put(TransactionParam.FEE_RATE_ORIGINAL, MapUtil.getString(tradeInfo, TransactionParam.FEE_RATE_ORIGINAL));
        }
        transaction.put(Transaction.PAYMENTS, payments);
        Map<String, Object> extendedParams = (Map<String, Object>) transaction.get(Transaction.EXTENDED_PARAMS);
        if (extendedParams != null) {
            Object extendParamsObj = extendedParams.get(BusinessV2Fields.EXTEND_PARAMS);
            if (Order.PAYWAY_JDWALLET == BeanUtil.getPropInt(transaction, Order.PAYWAY)) {
                if (extendParamsObj != null && extendParamsObj instanceof Map) {
                    Map extendParams = (Map) extendParamsObj;
                    transaction.put(TransactionParam.FQ_NUM, MapUtil.getIntValue(extendParams, TransactionParam.BAITIAO_FQ_NUM));
                }
            }
        }
        if (MapUtils.isNotEmpty(extraOutField)) {
            if (Order.PAYWAY_WEIXIN == BeanUtil.getPropInt(transaction, Transaction.PAYWAY)) {
                Map installInfo = MapUtils.getMap(extraOutField, WX_INSTALLMENT_INFO);
                if (MapUtils.isNotEmpty(installInfo)) {
                    transaction.put(TransactionParam.FQ_NUM, MapUtil.getIntValue(installInfo, WX_INSTALLMENT_INFO_NUM));
                    transaction.put(Transaction.FQ_TYPE, Transaction.FQ_TYPE_SELLER);
                    transaction.put(TransactionParam.FQ_SELLER_PERCENT, 100);
                }
            }
        }
        if (MapUtil.getBooleanValue(extraOutField, TransactionParam.HB_FQ, false)) {
            Map<String, Object> extraParams = (Map<String, Object>) transaction.get(Transaction.EXTRA_PARAMS);
            if (extendedParams != null) {
                Map<String, Object> extendParams = (Map<String, Object>) extendedParams.get(BusinessV2Fields.EXTEND_PARAMS);
                if (extendParams != null) {
                    int fqNum = MapUtil.getIntValue(extendParams, TransactionParam.FQ_NUM, 0);
                    long fqAmount = MapUtil.getLongValue(extraParams, HbfqTransactionUtils.FQ_AMOUNT, -1);
                    transaction.put(HbfqTransactionUtils.COMBINATION_PAY, MapUtil.getBooleanValue(extraParams, HbfqTransactionUtils.COMBINATION_PAY, false));
                    if (fqAmount != -1) {
                        transaction.put(HbfqTransactionUtils.FQ_AMOUNT, fqAmount);
                    }
                    //以前的数据没有product_flag的概念,只有hb_fq_num字段，这里useHbfq为true一定是花呗分期，
                    //useHbfq如果为false不一定就是不是花呗分期，如果fq_num是空，hb_fq_num不是空 (信用卡分期一定会上送fq_num,而老版本的没有fq_num字段)
                    boolean useHbfq = productFlag.contains(HbfqTransactionUtils.HBFQ_PRODUCT_FLAG);
                    if (useHbfq || fqNum == 0) {
                        //兼容之前的花呗分期接口
                        if (fqNum == 0) {
                            fqNum = MapUtil.getIntValue(extendParams, TransactionParam.HB_FQ_NUM, 0);
                            useHbfq = true;
                        }
                        transaction.put(TransactionParam.HB_FQ_NUM, fqNum);
                    }
                    transaction.put(TransactionParam.FQ_NUM, fqNum);
                    int sellerPercent = MapUtil.getIntValue(extendParams, TransactionParam.FQ_SELLER_PERCENT, 0);
                    if(useHbfq){
                        sellerPercent = MapUtil.getIntValue(extendParams, TransactionParam.HB_FQ_SELLER_PERCENT, 0);
                        transaction.put(TransactionParam.HB_FQ_SELLER_PERCENT, sellerPercent);
                    }
                    transaction.put(TransactionParam.FQ_SELLER_PERCENT, sellerPercent);
                    int fqType = sellerPercent > 0 ? Transaction.FQ_TYPE_SELLER : Transaction.FQ_TYPE_BUYER;
                    if(useHbfq){
                        transaction.put(Transaction.HB_FQ_TYPE, fqType);
                    }
                    transaction.put(Transaction.FQ_TYPE, fqType);
                    if(fqType == Transaction.FQ_TYPE_SELLER && extraParams != null){
                        if (useHbfq) {
                            transaction.put(TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE, extraParams.get(TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE));
                            if (MapUtil.getInteger(transaction, Transaction.PROVIDER) == null) {
                                //如果是直连的,需要自己计算
                                transaction.put(Transaction.FQ_MCH_DISCOUNT_AMOUNT, calculateSellerAmount(fqAmount, fqNum, useHbfq));
                            }
                        }
                        transaction.put(TransactionConstant.APPEND_SQB_FQ_SELLER_SERVICE_CHARGE, extraParams.get(TransactionConstant.APPEND_SQB_FQ_SELLER_SERVICE_CHARGE));
                    }
                }
            }
        }

        Map<String, Object> configSnapshot = (Map<String, Object>) MapUtil.getObject(transaction, Transaction.CONFIG_SNAPSHOT);
        Long tradeAPP = MapUtil.getLong(configSnapshot, Transaction.TRADE_APP, null);
        if(Objects.nonNull(tradeAPP)){
            transaction.put(Transaction.TRADE_APP, tradeAPP);
        }


        Map lakalaTradeParams = MapUtil.getMap(configSnapshot, Transaction.LAKALA_TRADE_PARAMS);
        transaction.put(Transaction.LAKALA_MERC_ID, MapUtil.getString(lakalaTradeParams, Transaction.LAKALA_MERC_ID));
        transaction.put(Transaction.LAKALA_TERM_ID, MapUtil.getString(lakalaTradeParams, Transaction.LAKALA_TERM_ID));
        Map<String, Object> extraParams = (Map<String, Object>) MapUtil.getObject(transaction, Transaction.EXTRA_PARAMS);
        // sql_wallet_name
        transaction.put(Transaction.SQB_WALLET_NAME, MapUtil.getString(extraParams, Transaction.SQB_WALLET_NAME));
        //sqb_biz_model
        transaction.put(Transaction.SQB_BIZ_MODEL, MapUtil.getString(extraParams, Transaction.SQB_BIZ_MODEL));
        //sqb_pay_path
        transaction.put(Transaction.SQB_PAY_PATH, MapUtil.getString(extraParams, Transaction.SQB_PAY_PATH));
        // 消费者 mark_up （加价） 金额
        Long sqbMarkUpAmount = MapUtil.getLong(extraParams, Transaction.SQB_MARK_UP_AMOUNT, null);
        if(Objects.nonNull(sqbMarkUpAmount)){
            transaction.put(Transaction.SQB_MARK_UP_AMOUNT, sqbMarkUpAmount);
        }
        if (extraOutField != null && extraOutField.containsKey(Transaction.IS_DEFAULT_POI)) {
            boolean isDefaultPoi = MapUtil.getBoolean(extraOutField, Transaction.IS_DEFAULT_POI, false);
            //location 表示定位信息,如果经纬度true是取configsnapshot中的数据,为false取extraParam中的数据
            if (isDefaultPoi) {
                Map<String, String> map = new HashMap<>();
                map.put(Transaction.LONGITUDE, MapUtil.getString(configSnapshot, Transaction.LONGITUDE));
                map.put(Transaction.LATITUDE, MapUtil.getString(configSnapshot, Transaction.LATITUDE));
                transaction.put(Transaction.LOCATION, map);
            } else {
                transaction.put(Transaction.LOCATION, MapUtil.getMap(extraParams, Transaction.POI));
            }
        }
        // providerTerminalId 先从configSnapShot中拿,再从交易参数中拿
        String providerTerminalId = MapUtil.getString(configSnapshot, Transaction.TERM_ID);
        if (providerTerminalId == null) {
            for (String key : TERMINAL_MAP.keySet()) {
                // 广发通道交易参数,主扫和被扫特殊判断
                if (key.equals(CGBBANK_TRADE_PARAMS)) {
                    Map tradeParams = MapUtil.getMap(configSnapshot, CGBBANK_TRADE_PARAMS);
                    String subPayway = BeanUtil.getPropString(transaction, Transaction.SUB_PAYWAY, "");
                    if (subPayway.equals(String.valueOf(SUB_PAYWAY_BARCODE.getCode()))) {
                        providerTerminalId = MapUtil.getString(tradeParams, CGBBANK_TERMINAL_ID, "");
                    } else {
                        providerTerminalId = MapUtil.getString(tradeParams, CGBBANK_PRE_TERMINAL_ID, "");
                    }
                    break;
                }
                if (configSnapshot != null && configSnapshot.containsKey(key)) {
                    Map tradeParams = MapUtil.getMap(configSnapshot, key);
                    providerTerminalId = MapUtil.getString(tradeParams, TERMINAL_MAP.get(key), "");
                    break;
                }
            }
        }
        transaction.put(Transaction.PROVIDER_TERMINAL_ID, providerTerminalId);

        // sqb_pay_source
        CommonUtil.putToMapIfValueNotEmpty(transaction, Transaction.SQB_PAY_SOURCE, MapUtil.getString(extraParams, Transaction.SQB_PAY_SOURCE));
        Map<String, Object> enhanceExtraParams = TransactionUtils.enhanceExtraParams(extraParams);
        Integer sharingFlag = MapUtil.getIntValue(enhanceExtraParams, TransactionEnhanceFields.SHARING_FLAG.getField(), 0);
        Double ratioTotal = MapUtil.getDoubleValue(enhanceExtraParams, TransactionEnhanceFields.RATIO_TOTAL.getField(), 0.0);
        //分账标识
        transaction.put(TransactionEnhanceFields.SHARING_FLAG.getField(), sharingFlag);
        //分账比例
        transaction.put(TransactionEnhanceFields.RATIO_TOTAL.getField(), ratioTotal);

        Map<String, Object> tradeParams = getTradeParams(configSnapshot);

        Integer payWay = MapUtil.getInteger(transaction, Transaction.PAYWAY);
        Integer provider = MapUtil.getInteger(transaction, Transaction.PROVIDER);
        // 支付宝直连：展示支付宝收款账号（seller_id）、支付宝门店号（alipay_store_id)
        if (ALIPAY_ALL_PAYWAY.contains(payWay) && provider == null) {
            // seller_id 支付宝直连收款支付宝pid
            transaction.put(Transaction.SELLER_ID, MapUtil.getString(tradeParams, Transaction.PID));
            // alipay_store_id 支付宝门店号（直接配置 优先取） app_auth_shop_id  支付宝门店号 （授权里面获取到的 非优先取）
            String alipayStoreId = MapUtil.getString(tradeParams, Transaction.ALIPAY_STORE_ID);
            if (StringUtils.isEmpty(alipayStoreId)) {
                alipayStoreId = MapUtil.getString(tradeParams, Transaction.APP_AUTH_SHOP_ID);
            }
            transaction.put(Transaction.ALIPAY_STORE_ID, alipayStoreId);
        }

        // payway_merchant_id 间连支付源商户号
        if (provider != null || Objects.equals(payWay, Order.PAYWAY_WEIXIN)) {
            transaction.put(Transaction.PAYWAY_MERCHANT_ID, getPaywayMerchantId(tradeParams));
        }
        //添加ip
        transaction.put(Transaction.CLIENT_IP, MapUtil.getString(extraParams, Transaction.CLIENT_IP));
        //添加sqb_csb_to_wap_sn
        transaction.put(Transaction.SQB_CSB_TO_WAP_SN, MapUtil.getString(extraParams, Transaction.SQB_CSB_TO_WAP_SN));
        //添加terminal_vendor_app_appid
        transaction.put(Transaction.VENDOR_APP_APPID, MapUtil.getString(configSnapshot, "terminal_vendor_app_appid"));
        //添加poi，如果存在
        if (!MapUtil.isEmpty(extraParams) && extraParams.containsKey(Transaction.POI)) {
            transaction.put(Transaction.POI, MapUtil.getMap(extraParams, Transaction.POI));
        }
        //添加sqb_user_id,交易流水顾客id关联的用户id
        if (!MapUtil.isEmpty(extraParams)) {
            transaction.put(Transaction.SQB_USER_ID, MapUtil.getString(extraParams, Transaction.SQB_USER_ID));
        }
    }

    /**
     * 计算商家分期手续费
     * @param fqAmount
     * @param fqNum
     * @return
     */
    public static long calculateSellerAmount(long fqAmount, int fqNum, boolean userHb) {
        String fqTypeKey = userHb ? ApolloUtil.FQ_PARAMS_HUABEI : ApolloUtil.FQ_PARAMS_CREDIT;
        double fqFeeRate = ApolloUtil.getFqFeeRate(fqTypeKey, String.valueOf(fqNum), true);
        if(fqFeeRate == 0){
            return 0;
        }
        BigDecimal totalFeeInDecimal = BigDecimal.valueOf(fqAmount).multiply(new BigDecimal(fqFeeRate));
        long totalFeeInLong = totalFeeInDecimal.setScale(0,
                BigDecimal.ROUND_HALF_EVEN).longValue();
        return totalFeeInLong;
    }

    private static Map<String, Object> getTradeParams(Map<String, Object> configSnapshot) {
        if (MapUtil.isEmpty(configSnapshot)) {
            return Maps.newTreeMap();
        }

        for (Map.Entry<String, Object> entry : configSnapshot.entrySet()) {
            if (entry != null && entry.getKey() != null && entry.getKey().endsWith("trade_params")) {
                return (Map<String, Object>) Optional.ofNullable(entry.getValue()).orElse(Maps.newTreeMap());
            }
        }
        return Maps.newTreeMap();
    }

    /**
     * 获取支付源商户号
     */
    private static String getPaywayMerchantId(Map<String, Object> tradeParams) {
        if (MapUtil.isEmpty(tradeParams)) {
            return null;
        }

        String paywayMerchantId;

        // 微信（直连、间连：结算通道：内蒙银联、通联）
        if (!StringUtils.isEmpty(paywayMerchantId = MapUtil.getString(tradeParams, TransactionParam.WEIXIN_SUB_MCH_ID))) {
            return paywayMerchantId;
        }
        if (!StringUtils.isEmpty(paywayMerchantId = MapUtil.getString(tradeParams, TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID))) {
            return paywayMerchantId;
        }
        if (!StringUtils.isEmpty(paywayMerchantId = MapUtil.getString(tradeParams, TransactionParam.UNION_PAY_DIRECT_WEIXIN_SUB_MCH_ID))) {
            return paywayMerchantId;
        }
        if (!StringUtils.isEmpty(paywayMerchantId = MapUtil.getString(tradeParams, TransactionParam.UNION_PAY_TL_WEIXIN_SUB_MCH_ID))) {
            return paywayMerchantId;
        }

        // 支付宝（间连：结算通道：内蒙银联、通联）
        if (!StringUtils.isEmpty(paywayMerchantId = MapUtil.getString(tradeParams, TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID))) {
            return paywayMerchantId;
        }
        if (!StringUtils.isEmpty(paywayMerchantId = MapUtil.getString(tradeParams, TransactionParam.UNION_PAY_TL_ALIPAY_SUB_MCH_ID))) {
            return paywayMerchantId;
        }

        // 云闪付（间连：银联开放平台、通联）
        if (!StringUtils.isEmpty(paywayMerchantId = MapUtil.getString(tradeParams, TransactionParam.UNION_PAY_OPEN_MCH_ID))) {
            return paywayMerchantId;
        }
        if (!StringUtils.isEmpty(paywayMerchantId = MapUtil.getString(tradeParams, TransactionParam.UNION_PAY_TL_UNION_MCH_ID))) {
            return paywayMerchantId;
        }

        return paywayMerchantId;
    }

    public static void removeBlobFields(Map<String, Object> transaction) {
        List<String> jsonBlobColumnList = Arrays.asList(
                Transaction.EXTRA_PARAMS,
                Transaction.EXTENDED_PARAMS,
                Transaction.EXTRA_OUT_FIELDS,
                Transaction.CONFIG_SNAPSHOT
        );
        for (String column : jsonBlobColumnList) {
            transaction.remove(column);
        }
    }

    public static void revertEntPayStatus(Map<String, Object> transaction) {
        int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
        int status = MapUtil.getIntValue(transaction, Transaction.STATUS);
        long ctime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
        int payWay = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        transaction.put(Transaction.STATUS, getEntPayStatus(type, payWay, status, ctime));
    }

    public static int getEntPayStatus(int type, int payway, int originalStatus, long ctime) {
        if (Transaction.TYPE_PAYMENT == type && Transaction.STATUS_ERROR_RECOVERY == originalStatus) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - ctime < DEFAULT_ENTPAY_QUERY_EXPIRE_TIME && Order.PAYWAY_BANKACCOUNT == payway) {
                //如果是微企付转账超过轮训时间，那么在24小时支付超时时间之内，返回的订单状态由PAY_ERROR修改为CREATED
                return Transaction.STATUS_IN_PROG;
            }
        }
        return originalStatus;
    }

    public static boolean isCrossMerchantRefund(Map transaction) {
        Map object = (Map) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS_CROSS_MCH_REFUND);
        return object != null && object.containsKey("merchant_id");
    }

    public static boolean isForeignCardTrade(Map transaction) {
        int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
        String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG, "");
        return  Order.PAYWAY_BANKCARD == payway && productFlag.contains("ar");
    }

    public static String getPayChannel(Map transaction) {
        List<Map<String, Object>> channelPayments = (List<Map<String, Object>>) BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.PAYMENTS);
        //type和origin_type映射的值相同时，去重
        LinkedHashSet<String> channelPaymentSets = new LinkedHashSet<>();
        if (channelPayments != null) {
            String wildCardType = null;
            boolean needAddWildCardType = false;
            if (MapUtil.getIntValue(transaction, Transaction.PAYWAY) == Order.PAYWAY_BANKCARD) {
                wildCardType = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS), Transaction.WILD_CARD_TYPE);
                if (!StringUtil.empty(wildCardType)) {
                    wildCardType = wildCardType.toUpperCase();
                    needAddWildCardType =  true;
                }
            }
            for (Map changnnelPayment : channelPayments) {
                String typeChangnnelPayment = MapUtil.getString(ApolloUtil.getPaymentMap(), (MapUtil.getString(changnnelPayment, Payment.TYPE)), "");
                String originTypeChangnnelPayment = MapUtil.getString(ApolloUtil.getPaymentMap(), (MapUtil.getString(changnnelPayment, Payment.ORIGIN_TYPE)), "");

                // 银行卡外卡特殊处理
                if (needAddWildCardType) {
                    channelPaymentSets.add(typeChangnnelPayment + "（" + wildCardType + "）");
                    continue;
                }

                if (!StringUtils.isEmpty(typeChangnnelPayment) && !StringUtils.isEmpty(originTypeChangnnelPayment)) {
                    //如果type和origin_type都存在，origin_type就加上()
                    if (!typeChangnnelPayment.equals(originTypeChangnnelPayment)) {
                        channelPaymentSets.add(typeChangnnelPayment + "（" + originTypeChangnnelPayment + "）");
                    } else {
                        channelPaymentSets.add(typeChangnnelPayment);
                    }
                } else {
                    channelPaymentSets.add(typeChangnnelPayment);
                    channelPaymentSets.add(originTypeChangnnelPayment);
                }
            }
            boolean isHbfq = isHbfqInFlag(MapUtil.getString(transaction, Transaction.PRODUCT_FLAG));
            if (isHbfq) {
                //部分花呗交易流水,支付宝查单返回ALIPAYACCOUNT。
                String alipayHuabei = MapUtil.getString(ApolloUtil.getPaymentMap(), "PCREDIT");
                if (!StringUtils.isEmpty(alipayHuabei)) {
                    if(channelPaymentSets.add(alipayHuabei)){
                        String payment = MapUtil.getString(ApolloUtil.getPaymentMap(), "ALIPAYACCOUNT");
                        if (channelPaymentSets.contains(payment)) {
                            //有可能是组合支付,一部分使用余额,另一部分使用花呗。这种情况不需要移除 "支付宝钱包余额" 的资金明细
                            boolean balancePaymentCount = channelPayments.stream().filter(o -> MapUtil.getString(o, Payment.TYPE, "").equals(payment)).count() <= 1;
                            if (balancePaymentCount) {
                                channelPaymentSets.remove(payment);
                            }
                        }
                    }
                }
            }
            channelPaymentSets.remove("");
        }
        return channelPaymentSets.stream().collect(Collectors.joining(" "));
    }



    public static long applyRate(long amount, String rate) {
        if (rate == null) {
            return 0;
        }
        long cents = StringUtils.yuan2cents(rate);

        return (amount * cents + 5000) / 10000;
    }


    public static boolean isStoreInFlag(String productFlag){
        return !StringUtil.empty(productFlag) && productFlag.contains("aa");
    }

    public static boolean isHbfqInFlag(String productFlag){
        return !StringUtil.empty(productFlag) && productFlag.contains("a3");
    }

    /**
     *
     * 计算指定场景下的分账金额
     *
     * @param profitSharing
     * @param clearingAmount
     * @param fee
     * @param useRound
     * @param productFlag
     * @return
     */
    public static long getProductSharingPayAmountByProfitSharing(Map<String,Object> profitSharing, long clearingAmount, long fee, boolean useRound, String productFlag){
        long sumSharingAmount = 0L;
        List<Map<String, Object>> receivers = UpayProfitSharingUtil.calculateSharingPayAmount(profitSharing, clearingAmount, fee, useRound);
        for (Map<String, Object> receiver : receivers) {
            if(!StringUtil.empty(productFlag) && !Objects.equals(productFlag, MapUtil.getString(receiver, ProfitSharing.RECEIVER_PRODUCT_FLAG))) {
                continue;
            }
            sumSharingAmount+= MapUtil.getLongValue(receiver, ProfitSharing.RECEIVER_AMOUNT);
        }
        return sumSharingAmount;
    }

    /**
     *
     *  计算指定场景下的分账回退金额
     *
     * @param profitSharing
     * @param clearingAmount
     * @param fee
     * @param payClearingAmount
     * @param useRound
     * @param productFlag
     * @return
     */
    public static long getProductSharingRestituteAmountByProfitSharing(Map<String, Object> profitSharing, long clearingAmount, long fee, long payClearingAmount, boolean useRound, String productFlag) {
        long sumSharingAmount = 0L;
        List<Map<String, Object>> receivers = UpayProfitSharingUtil.calculateSharingRestituteAmount(profitSharing, clearingAmount, fee, payClearingAmount, useRound);
        for (Map<String, Object> receiver : receivers) {
            if(!StringUtil.empty(productFlag) && !Objects.equals(productFlag, MapUtil.getString(receiver, ProfitSharing.RECEIVER_PRODUCT_FLAG))) {
                continue;
            }
            if(Objects.equals(ProfitSharing.SHARING_FLAG_DISABLE, MapUtil.getString(receiver, ProfitSharing.SHARING_FLAG, ProfitSharing.SHARING_FLAG_ENABLE))) {
                continue;
            }
            sumSharingAmount+= MapUtil.getLongValue(receiver, ProfitSharing.RECEIVER_AMOUNT);
        }
        return sumSharingAmount;
    }

    /**
     *
     *  返回未回退的分账金额
     *
     * @param profitSharing
     * @param clearingAmount
     * @param fee
     * @param payClearingAmount
     * @param useRound
     * @param productFlag
     * @return
     */
    public static long getNotRestituteAmountByProfitSharing(Map<String, Object> profitSharing, long clearingAmount, long fee, long payClearingAmount, boolean useRound) {
        long sumSharingAmount = 0L;
        List<Map<String, Object>> receivers = UpayProfitSharingUtil.calculateSharingRestituteAmount(profitSharing, clearingAmount, fee, payClearingAmount, useRound);
        for (Map<String, Object> receiver : receivers) {
            if(Objects.equals(ProfitSharing.SHARING_FLAG_DISABLE, MapUtil.getString(receiver, ProfitSharing.SHARING_FLAG, ProfitSharing.SHARING_FLAG_ENABLE))) {
                sumSharingAmount+= MapUtil.getLongValue(receiver, ProfitSharing.RECEIVER_AMOUNT);
            }
        }
        return sumSharingAmount;
    }

    /**
     *  添加分账明细
     * @param transaction
     * @param businessService
     */
    public static void expandSharingBooks(Map<String, Object> transaction, BusinessService businessService) {
        expandSharingBooks(transaction, businessService, null);
     }

    /**
     *  添加分账明细
     * @param transaction
     * @param businessService
     */
    public static void expandSharingBooks(Map<String, Object> transaction, BusinessService businessService, Map<String, Object> localReceiverCache) {
        Map extraParams = (Map) transaction.get(Transaction.EXTRA_PARAMS);
        if (MapUtil.isEmpty(extraParams) || extraParams.get(Transaction.PROFIT_SHARING) == null) {
            return;
        }

        Map profitSharing = (Map) extraParams.get(Transaction.PROFIT_SHARING);
        if (MapUtil.isEmpty(profitSharing)) {
            return;
        }

        long sharingAmount = MapUtil.getLongValue(transaction, Transaction.SHARING_AMOUNT);
        long tradeServiceSharingAmount = MapUtil.getLongValue(transaction, Transaction.TRADE_SERVICE_SHARING_AMOUNT);
        List<Map<String, Object>> sharingBookMapList = null;
        if(tradeServiceSharingAmount > 0) {
            Integer type = MapUtil.getIntValue(transaction, Transaction.TYPE);
            long fee = MapUtil.getIntValue(transaction, Transaction.FEE);
            // 当前流水计算出来的结算金额已经减去了分账金额
            long clearingAmount = MapUtil.getIntValue(transaction, Transaction.CLEARING_AMOUNT) + sharingAmount + tradeServiceSharingAmount;
            int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
            int provider = MapUtil.getIntValue(transaction, Transaction.PROVIDER);
            boolean useRound = UpayProfitSharingUtil.useRound(payway, provider);
            // 正向支付
            if (TransactionType.PAYMENT_TYPES.contains(type)) {
                sharingBookMapList = UpayProfitSharingUtil.calculateSharingPayAmount(profitSharing, clearingAmount, fee, useRound);
            } else if (TransactionType.REFUND_TYPES.contains(type)) { // 反向退款/取消
                Long payClearingAmount = MapUtil.getLong(profitSharing, ProfitSharing.SHARING_PAY_CLEARING_AMOUNT);
                // payClearingAmount 不为空按新的方式计算
                if (payClearingAmount != null) {
                    sharingBookMapList = UpayProfitSharingUtil.calculateSharingRestituteAmount(profitSharing, clearingAmount, fee, payClearingAmount, useRound);
                } else { // payClearingAmount 为空，兼容历史数据
                    sharingBookMapList = UpayProfitSharingUtil.calculateSharingPayAmount(profitSharing, clearingAmount, fee, useRound);
                }
            }
        } else {
            // 分账金额为0时，也需要展示
            List<Map<String, Object>> receivers = (List<Map<String, Object>>) profitSharing.get(ProfitSharing.RECEIVERS);
            if(CollectionUtils.isEmpty(receivers)) {
                return;
            }
            sharingBookMapList = new ArrayList<Map<String,Object>>();
            for (Map<String, Object> receiver : receivers) {
                receiver.put(ProfitSharing.RECEIVER_AMOUNT, 0L);
                sharingBookMapList.add(receiver);
            }
        }
        if (CollectionUtils.isEmpty(sharingBookMapList)) {
            return;
        }
        Map<String, Long> sharingReceivers = new HashMap<>();
        Map<String, Map<String, Object>> sharingReceiverDetails = new HashMap<>();
        for (Map<String, Object> sharingBookMap : sharingBookMapList) {
            if(MapUtil.isEmpty(sharingBookMap)) {
                continue;
            }
            if(!ProfitSharing.SHARING_FLAG_ENABLE.equals(MapUtil.getString(sharingBookMap, ProfitSharing.SHARING_FLAG, ProfitSharing.SHARING_FLAG_ENABLE))) {
                continue;
            }
            String receiverId = MapUtil.getString(sharingBookMap, ProfitSharing.RECEIVER_ID);
            long amount = MapUtil.getLongValue(sharingBookMap, ProfitSharing.RECEIVER_AMOUNT);
            long totalAmount = MapUtil.getLongValue(sharingBookMap, receiverId) + amount;
            sharingReceivers.put(receiverId, totalAmount);
            sharingReceiverDetails.put(receiverId, sharingBookMap);
        }
        Map<String, Map<String, Object>> recieveInfos = businessService.getReceiverInfo(new ArrayList(sharingReceivers.keySet()), localReceiverCache);
        // 按照分账业务名称进行分组
        Map<String, TAccountRecordDetail.SharingBook> sharingBooks = new HashMap<String, TAccountRecordDetail.SharingBook>();
        sharingReceivers.forEach((k, v) ->{
            Map<String, Object> receiveInfo = recieveInfos.get(k);
            Integer rType = MapUtil.getInteger(receiveInfo, Receiver.TYPE, ReceiverType.SHARING.getVal());
            String alias = MapUtil.getString(receiveInfo, Receiver.ALIAS, "收款业务分账");
            if (!Objects.equals(ReceiverType.SERVICE_CHARGE.getVal(), rType)) {
                //收款业务分账 不处理
                return;
            }
            TAccountRecordDetail.SharingBook sharingBook = sharingBooks.get(alias);
            if(sharingBook == null) {
                sharingBook = new TAccountRecordDetail.SharingBook();
                Map<String, Object> receiver = sharingReceiverDetails.get(k);
                String ratio = MapUtil.getString(receiver, "ratio");
                if(!StringUtils.isEmpty(ratio) && ConfigServiceUtils.chargeNameSet.contains(alias)){
                    alias += "（" + ratio + "）";
                }
                sharingBooks.put(alias, sharingBook);
                sharingBook.setType(rType);
                sharingBook.setAlias(alias);
                sharingBook.setAmount(0L);
            }
            sharingBook.setAmount(sharingBook.getAmount() + v);
        });
        List<Map> sharingBooksMap = sharingBooks
                .values()
                .stream()
                .map(book -> MapUtil.hashMap(Receiver.TYPE, book.getType(), Receiver.ALIAS, book.getAlias(), Payment.AMOUNT, book.getAmount()))
                .collect(Collectors.toList());
        transaction.put(CommonConstant.SHARING_BOOKS, sharingBooksMap);
    }

    public static String getUnionPayType(List<Map> payments) {
        //判空
        if(payments == null || payments.size() == 0){
            return null;
        }
        for(Map payment: payments){
           String originType =  MapUtil.getString(payment,Payment.ORIGIN_TYPE);
            if(!StringUtils.isEmpty(originType) && (originType.equals("UPQUICKPASS_DEBIT") || originType.equals("UPQUICKPASS_CREDIT"))){
               return "银联云闪付";
            }
        }
        return null;
    }

    public static String getChannelTradeNo(Map<String, Object> transaction) {
        String tradeNo = MapUtil.getString(transaction, Transaction.TRADE_NO);
        Map<String, Object> extraOutField = (Map<String, Object>) MapUtil.getObject(transaction, Transaction.EXTRA_OUT_FIELDS);
        String channelTradeNo = MapUtil.getString(extraOutField, Transaction.CHANNEL_TRADE_NO);

        Integer provider = MapUtil.getInteger(transaction, Transaction.PROVIDER);
        // 直连通道和拉卡拉、海科等走银联接口的通道 收单机构和支付源订单号都使用支付宝微信的订单号
        if (provider == null
                || Objects.equals(Provider.UNION_PAY_DIRECT.getCode(), provider)
                || Objects.equals(Provider.HAIKE_UNION_PAY.getCode(), provider)
                || Objects.equals(Provider.LAKALA_UNION_PAY.getCode(), provider)
                || Objects.equals(Provider.ENTPAY.getCode(), provider)) {
            channelTradeNo = StringUtils.isEmpty(channelTradeNo) ? tradeNo : channelTradeNo;
        }

        return channelTradeNo;
    }

    /**
     * 是否使用额度包
     *
     * @param transaction
     * @return
     */
    public static boolean useQuota(Map transaction) {
        String quotaTag = MapUtil.getString(MapUtil.getMap(transaction, Transaction.EXTRA_OUT_FIELDS), Transaction.QUOTA_FEE_RATE_TAG);
        return !StringUtil.empty(quotaTag);
    }

    private static final char COMMON_SWTICH_OPEN = Character.forDigit(TransactionParam.STATUS_OPENED, 10);

    public static boolean isCommonSwitchOpen(String commonSwitch, int type) {
        if (StringUtils.isEmpty(commonSwitch) || commonSwitch.length() <= type) {
            return false;
        }
        char currentStatus = commonSwitch.charAt(type);
        if (currentStatus == COMMON_SWTICH_OPEN) {
            return true;
        }
        return false;
    }

    public static boolean summaryDeposit(String summaryExt) {
        return summaryDeposit(CommonUtil.splitToListString(summaryExt));
    }

    public static boolean summaryDeposit(List<String> summaryExts) {
        if (summaryExts != null && summaryExts.contains("DEPOSIT")) {
            return true;
        }
        return false;
    }

    public static String convertReflectStr(Object reflect) {
        if (Objects.isNull(reflect)) {
            return Strings.EMPTY;
        }

        if (reflect instanceof String) {
            return String.valueOf(reflect);
        } else if (reflect instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) reflect;
            return map.values().stream().map(Object::toString).collect(Collectors.joining(" "));
        }

        return JsonUtil.toJsonStr(reflect);
    }


}
