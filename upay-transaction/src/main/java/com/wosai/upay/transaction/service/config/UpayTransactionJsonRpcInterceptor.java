package com.wosai.upay.transaction.service.config;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphO;
import com.alibaba.csp.sentinel.context.ContextUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.JsonRpcInterceptor;
import com.wosai.sentinel.sdk.init.SentinelStarter;
import com.wosai.upay.transaction.enums.ErrorMessageEnum;

import java.lang.reflect.Method;
import java.util.List;

public class UpayTransactionJsonRpcInterceptor implements JsonRpcInterceptor {
    public UpayTransactionJsonRpcInterceptor() {
    }

    public static final ThreadLocal<String> INTO_METHOD = ThreadLocal.withInitial(() -> "");

    public void preHandleJson(JsonNode json) {
    }

    public void preCallMethod(Object target, Method method, Object... argument) {
        if (!SphO.entry(method, EntryType.IN, 1, argument)) {
            throw ErrorMessageEnum.REQUEST_TOO_FREQUENTLY.getBizException();
        }
        INTO_METHOD.set(method.getName());
    }

    public void afterCallMethod(Object target, Method method, Object result, Object... argument) {
        if (ContextUtil.getContext() != null) {
            SphO.exit(1, argument);
        }
        INTO_METHOD.remove();
    }

    public void preHandle(Object target, Method method, List<JsonNode> params) {
    }

    public void postHandle(Object target, Method method, List<JsonNode> params, JsonNode result) {
    }

    public void postHandleJson(JsonNode json) {
    }

    static {
        SentinelStarter.doStart();
    }
}
