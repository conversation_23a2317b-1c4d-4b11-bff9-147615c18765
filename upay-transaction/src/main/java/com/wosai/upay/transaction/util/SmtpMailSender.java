package com.wosai.upay.transaction.util;

import lombok.Data;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;

/**
 * Smtp 协议邮件发送类型
 *
 **/
@Component
public class SmtpMailSender {

    private JavaMailSender javaMailSender;

    public void setJavaMailSender(JavaMailSender javaMailSender) {
        this.javaMailSender = javaMailSender;
    }

    public JavaMailSender getJavaMailSender() {
        return javaMailSender;
    }

    public void send(Mail mail) throws UnsupportedEncodingException, MessagingException {
        Assert.hasLength(mail.getToAddresses(), "邮件接收者不能为空");

        MimeMessage mimeMailMessage = javaMailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMailMessage, true);
        mimeMessageHelper.setFrom(mail.getAccountName(), mail.getAccountAlias());
        mimeMessageHelper.setTo(Arrays.stream(mail.getToAddresses().split(",")).filter(StringUtils::isNotEmpty).distinct().toArray(String[]::new));
        mimeMessageHelper.setSubject(mail.getSubject());
        mimeMessageHelper.setText(mail.getHtmlBody(), true);

        //多附件
        if (CollectionUtils.isNotEmpty(mail.getAttachments())) {
            for (Mail.Attachment attachment : mail.getAttachments()) {
                mimeMessageHelper.addAttachment(attachment.getName(), new File(attachment.getPath()));
            }
        }
        try {
            javaMailSender.send(mimeMailMessage);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Accessors(chain = true)
    @Data
    public static class Mail {

        /**
         * 邮件账户名称，示例：
         */
        private String accountName;

        /**
         * 邮件账户别名，示例：
         */
        private String accountAlias;

        /**
         * 邮件收件邮箱列表，多个逗号分割
         */
        private String toAddresses;

        /**
         * 邮件主题
         */
        private String subject;

        /**
         * 邮件 HTML 格式内存
         */
        private String htmlBody;

        /**
         * 邮件内容 链接
         */
        private List<Link> hrefLinks;

        @Accessors(chain = true)
        @Data
        public static class Link {

            /**
             * 链接地址
             */
            private String ossUrl;

            /**
             * 本地资源（附件）绝对路径
             */
            private String path;
        }
        /**
         * 邮件附件
         */
        private List<Attachment> attachments;

        @Accessors(chain = true)
        @Data
        public static class Attachment {

            /**
             * 附件名称
             */
            private String name;

            /**
             * 本地资源（附件）绝对路径
             */
            private String path;
        }

    }

}
