package com.wosai.upay.transaction.constant;

/**
 * <AUTHOR>
 */
public class DatabaseQueryConstant {

    /**
     * 分页查询每页最大的记录数
     */
    public static final int MAX_PAGE_SIZE_LIMIT = 5000;

    /**
     * 集团分页查询每页最大的记录数
     */
    public static final int GROUP_EXPORT_PAGE_SIZE_LIMIT = 2000;

    /**
     * mysql最大分页最大记录数
     */
    public static final int MAX_PAGE_SIZE_MYSQL_LIMIT = 1000;

    public static final String TERMINAL_INDEX_NAME = "idx_terminal_id_ctime_status";
    public static final String STORE_INDEX_NAME = "idx_store_id_ctime_status";
    public static final String MERCHANT_INDEX_NAME = "idx_merchant_id_ctime_status";
    public static final String TERMINAL_FINISH_INDEX_NAME = "idx_terminal_id_channel_finish_time_status";
    public static final String STORE_FINISH_INDEX_NAME = "idx_store_id_channel_finish_time_status";
    public static final String MERCHANT_FINISH_INDEX_NAME = "idx_merchant_id_channel_finish_time_status";

    public static final String CLIENT_SN_INDEX_NAME = "idx_client_sn_store_id";
    
    public static final String TRANSACTION_CLIENT_TSN = "idx_client_tsn";

    public static final String TDDL_HINT_FIVE_MINITES_TIMEOUT = "/*+TDDL:SOCKET_TIMEOUT(300000)*/";
    public static final String TDDL_HINT_TEN_SECONDS_TIMEOUT = "/*+TDDL:SOCKET_TIMEOUT(10000)*/";
    public static final String TDDL_HINT_THIRTEEN_SECONDS_TIMEOUT = "/*+TDDL:SOCKET_TIMEOUT(20000)*/";
    public static final String TDDL_HINT_ONE_MINUTES_TIMEOUT = "/*+TDDL:SOCKET_TIMEOUT(61000)*/";
    public static final String TDDL_HINT_THREE_MINUTES_TIMEOUT = "/*+TDDL:SOCKET_TIMEOUT(181000)*/";

    public static final String TDDL_HINT_MAX_TIMEOUT = "/*+TDDL:SOCKET_TIMEOUT(21000)*/";


    public static final Integer SQL_QUERY_FIVE_MINITES_TIMEOUT_SECONDS = 300;
    public static final Integer SQL_QUERY_TEN_SECONDS_TIMEOUT_SECONDS = 10;
    public static final Integer SQL_QUERY_THIRTEEN_SECONDS_TIMEOUT_SECONDS = 20;
    public static final Integer SQL_QUERY_ONE_MINUTES_TIMEOUT_SECONDS = 60;
    public static final Integer SQL_QUERY_THREE_SECONDS_TIMEOUT_SECONDS = 3;
    public static final Integer SQL_QUERY_THREE_MINUTES_TIMEOUT_SECONDS = 180;


    // 注意：如果在指定超时时间内未执行完成，会返回数据，导致对账单导出为空
    public static final Integer QUERY_THIRTEEN_SECONDS_TIMEOUT_SECONDS = 20000;
    public static final Integer GROUP_EXPORT_SECONDS_TIMEOUT_SECONDS = 60000;

    public static final Integer TIMEOUT_5000 = 5000;


    public static final String UPAY_BACK_DATA_SOURCE_NAME = "upayBackDatasource";
    public static final String UPAY_DATA_SOURCE_NAME = "upayDatasource";
    public static final String UPAY_WIPE_DATASOURCE_NAME = "upaySwipeDatasource";



}
