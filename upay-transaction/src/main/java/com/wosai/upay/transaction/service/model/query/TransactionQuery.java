package com.wosai.upay.transaction.service.model.query;

import com.google.common.collect.Lists;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.*;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.param.AccountRecordParam;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.util.AccountBookUtils;
import com.wosai.upay.transaction.util.CommonUtil;
import com.wosai.upay.transaction.util.EnumUtils;
import com.wosai.upay.transaction.util.Pair;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.joda.time.DateTime;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class TransactionQuery extends BaseQuery {

    private List<String> storeIdList;

    private List<String> merchantIdList;

    private List<String> operatorIds;

    private String merchantId;

    private List<String> cashDeskList;

    private List<String> terminalList;

    private Set<String> groupByKeys;

    private List<String> notContainTerminalList;

    private List<Integer> payways;

    private List<String> orderSns;

    private String transactionId;

    private String transactionSn;

    private String tradeNo;

    private List<String> productFlags;

    private Set<LoadType> loadTypes = new HashSet<>();

    /**
     * 是否简易账本  简易账本不包括错误状态
     */
    private Boolean simple = false;

    private int offsetHour;

    private List<String> buyerUids;

    private Map<CommonStatus, StatusTypeSubPayWayQuery> statusTypeSubPayWayQueries = new HashMap<CommonStatus, StatusTypeSubPayWayQuery>();

    private List<String> summaryExts;

    private Long minOriginalAmount;

    private Long maxOriginalAmount;

    public Long getMinOriginalAmount() {
        return minOriginalAmount;
    }

    public void setMinOriginalAmount(Long minOriginalAmount) {
        this.minOriginalAmount = minOriginalAmount;
    }

    public Long getMaxOriginalAmount() {
        return maxOriginalAmount;
    }

    public void setMaxOriginalAmount(Long maxOriginalAmount) {
        this.maxOriginalAmount = maxOriginalAmount;
    }

    public List<String> getStoreIdList() {
        return storeIdList;
    }

    public void setStoreIdList(List<String> storeIdList) {
        this.storeIdList = storeIdList;
    }

    public List<String> getCashDeskList() {
        return cashDeskList;
    }

    public void setCashDeskList(List<String> cashDeskList) {
        this.cashDeskList = cashDeskList;
    }

    public List<String> getTerminalList() {
        return terminalList;
    }

    public void setTerminalList(List<String> terminalList) {
        this.terminalList = terminalList;
    }


    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }


    public Map<CommonStatus, StatusTypeSubPayWayQuery> getStatusTypeSubPayWayQueries() {
        return statusTypeSubPayWayQueries;
    }

    public List<String> getOperatorIds() {
        return operatorIds;
    }

    public void setOperatorIds(List<String> operatorIds) {
        this.operatorIds = operatorIds;
    }

    public void setTransactionSn(String transactionSn) {
        this.transactionSn = transactionSn;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public List<String> getOrderSns() {
        return orderSns;
    }

    public void setOrderSns(List<String> orderSns) {
        this.orderSns = orderSns;
    }

    public String getTransactionSn() {
        return transactionSn;
    }

    public Set<LoadType> getLoadTypes() {
        return loadTypes;
    }

    public void setLoadTypes(Set<LoadType> loadTypes) {
        this.loadTypes = loadTypes;
    }

    public List<Integer> getPayways() {
        return payways;
    }

    public void setPayways(List<Integer> payways) {
        this.payways = payways;
    }

    public Boolean getSimple() {
        return simple;
    }

    public void setSimple(Boolean simple) {
        this.simple = simple;
    }

    public List<String> getNotContainTerminalList() {
        return notContainTerminalList;
    }

    public void setNotContainTerminalList(List<String> notContainTerminalList) {
        this.notContainTerminalList = notContainTerminalList;
    }

    public Set<String> getGroupByKeys() {
        return groupByKeys;
    }

    public void setGroupByKeys(Set<String> groupByKeys) {
        this.groupByKeys = groupByKeys;
    }


    public List<String> getProductFlags() {
        return productFlags;
    }

    public void setProductFlags(List<String> productFlags) {
        this.productFlags = productFlags;
    }

    public int getOffsetHour() {
        return offsetHour;
    }

    public void setOffsetHour(int offsetHour) {
        this.offsetHour = offsetHour;
    }

    public List<String> getMerchantIdList() {
        return merchantIdList;
    }

    public void setMerchantIdList(List<String> merchantIdList) {
        this.merchantIdList = merchantIdList;
    }

    public List<String> getBuyerUids() {
        return buyerUids;
    }

    public void setBuyerUids(List<String> buyerUids) {
        this.buyerUids = buyerUids;
    }

    public TransactionQuery() {

    }

    public TransactionQuery clone() {
        TransactionQuery transactionQuery = new TransactionQuery();
        transactionQuery.setQueryEs(isQueryEs());
        transactionQuery.setQueryCashDesk(isQueryCashDesk());
        transactionQuery.setLimit(getLimit());
        transactionQuery.setStartTime(getStartTime());
        transactionQuery.setEndTime(getEndTime());
        transactionQuery.setLimit(getLimit());
        transactionQuery.setOrderBy(getOrderBy());
        transactionQuery.setMerchantId(getMerchantId());
        if (!CollectionUtils.isEmpty(getCashDeskList())) {
            transactionQuery.setCashDeskList(new ArrayList<>(getCashDeskList()));
        }
        if (!CollectionUtils.isEmpty(getStoreIdList())) {
            transactionQuery.setStoreIdList(new ArrayList<>(getStoreIdList()));
        }
        if (!CollectionUtils.isEmpty(getMerchantIdList())) {
            transactionQuery.setMerchantIdList(new ArrayList<>(getMerchantIdList()));
        }

        if (!CollectionUtils.isEmpty(getProductFlags())) {
            transactionQuery.setProductFlags(new ArrayList<>(getProductFlags()));
        }

        if (!CollectionUtils.isEmpty(getOperatorIds())) {
            transactionQuery.setOperatorIds(new ArrayList<>(getOperatorIds()));
        }

        if (!CollectionUtils.isEmpty(getTerminalList())) {
            transactionQuery.setTerminalList(getTerminalList());
        }

        if (!CollectionUtils.isEmpty(getOrderSns())) {
            transactionQuery.setOrderSns(new ArrayList<>(getOrderSns()));
        }

        if (!CollectionUtils.isEmpty(getLoadTypes())) {
            transactionQuery.setLoadTypes(new HashSet<>(getLoadTypes()));
        }

        if (!CollectionUtils.isEmpty(getPayways())) {
            transactionQuery.setPayways(new ArrayList<>(getPayways()));
        }

        if (!CollectionUtils.isEmpty(getNotContainTerminalList())) {
            transactionQuery.setNotContainTerminalList(new ArrayList<>(getNotContainTerminalList()));
        }

        if (!CollectionUtils.isEmpty(getStatusTypeSubPayWayQueries())) {
            for (Map.Entry<CommonStatus, StatusTypeSubPayWayQuery> statusTypeSubPayWayQueryEntry : statusTypeSubPayWayQueries.entrySet()) {
                transactionQuery.getStatusTypeSubPayWayQueries().put(statusTypeSubPayWayQueryEntry.getKey(), statusTypeSubPayWayQueryEntry.getValue().clone());
            }
        }
        transactionQuery.setTransactionSn(getTransactionSn());

        transactionQuery.setOrderBy(getOrderBy());

        transactionQuery.setSimple(getSimple());

        transactionQuery.setBackUpTime(getBackUpTime());

        transactionQuery.setGroupByKeys(groupByKeys);

        transactionQuery.setOffsetHour(offsetHour);

        if (!CollectionUtils.isEmpty(getBuyerUids())) {
            transactionQuery.setBuyerUids(new ArrayList<>(getBuyerUids()));
        }
        transactionQuery.setMinOriginalAmount(getMinOriginalAmount());
        transactionQuery.setMaxOriginalAmount(getMaxOriginalAmount());
        return transactionQuery;

    }


    public static TransactionQuery buildTransactionQuery(AccountRecordParam param, ThreadLocal<Map<String, Object>> detailUrlParam, BusinessService businessService) {
        TransactionQuery transactionQuery = new TransactionQuery();
        Map<String, Object> detailUrl = detailUrlParam.get();
        detailUrl.put(CommonConstant.TOKEN, param.getToken());
        detailUrl.put(CommonConstant.UPAY_QUERY_TYPE, String.valueOf(param.getUpayQueryType()));
        //是否门店账本
        detailUrl.put(CommonConstant.STORE_ACCOUNT_FLAG, String.valueOf(param.getIsStoreAccount()));

        transactionQuery.setOrderSns(CommonUtil.splitToListString(param.getOrderSns()));

        transactionQuery.setStoreIdList(CommonUtil.splitToListString(param.getStoreIds()));

        transactionQuery.setMerchantIdList(CommonUtil.splitToListString(param.getMerchantIds()));

        transactionQuery.setCashDeskList(CommonUtil.splitToListString(param.getCashDesk()));
        if (!CollectionUtils.isEmpty(transactionQuery.getCashDeskList())) {
            transactionQuery.setQueryCashDesk(true);
        }

        transactionQuery.setBuyerUids(CommonUtil.splitToListString(param.getBuyerUids()));

        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();

        ImmutablePair<Boolean, List<Integer>> tradeStatusInfo = AccountBookUtils.getSortedTradeStatus(param.getTradeStatus());
        boolean isFullTypeQuery = tradeStatusInfo.getLeft();
        List<Integer> types = tradeStatusInfo.getRight();

        List<String> groupBys = CommonUtil.splitToListString(param.getGroupBys());

        if (!CollectionUtils.isEmpty(groupBys)) {
            transactionQuery.setGroupByKeys(EnumUtils.getSetName(TransactionGroupByKey.class, new HashSet<>(groupBys)));
        }
        List<Integer> tradeTypes = Lists.newArrayList();
        List<Integer> notTradeTypes = Lists.newArrayList();
        if (isFullTypeQuery) {
            //交易账本
            if (param.getIsStoreAccount()) {
                notTradeTypes.add(TransactionType.DEPOSIT_CANCEL.getCode());
                notTradeTypes.add(TransactionType.DEPOSIT_FREEZE.getCode());
            }
        } else {
            //收款成功
            if (types.contains(TradeType.PAID.getCode())) {
                tradeTypes.add(TransactionType.PAYMENT.getCode());
                tradeTypes.add(TransactionType.CHARGE.getCode());
                tradeTypes.add(TransactionType.ORDER_TAKE.getCode());
                tradeTypes.add(TransactionType.STORE_PAY.getCode());
                tradeTypes.add(TransactionType.STORE_IN.getCode());
                //门店账本
                if (param.getIsStoreAccount()) {
                    tradeTypes.add(TransactionType.DEPOSIT_CONSUME.getCode());
                }
            }
            //退款
            if (types.contains(TradeType.REFUNDED.getCode())) {
                tradeTypes.add(TransactionType.CANCEL.getCode());
                tradeTypes.add(TransactionType.REFUND.getCode());
                tradeTypes.add(TransactionType.CHARGE_REFUND.getCode());
                tradeTypes.add(TransactionType.ORDER_TAKE_REFUND.getCode());
                tradeTypes.add(TransactionType.STORE_REFUND.getCode());
                tradeTypes.add(TransactionType.IN_REFUND.getCode());
            }
            //预授权消费撤销
            if (types.contains(TradeType.DEPOSIT_CONSUME_CANCEL.getCode())) {
                tradeTypes.add(TransactionType.DEPOSIT_CONSUME_CANCEL.getCode());
            }
            //预授权状态与正常状态区分开
            //交易账本
            if (!param.getIsStoreAccount()) {
                if (types.contains(TradeType.DEPOSIT_FREEZE.getCode())) {
                    tradeTypes.add(TransactionType.DEPOSIT_FREEZE.getCode());
                }
                if (types.contains(TradeType.DEPOSIT_CANCEL.getCode())) {
                    tradeTypes.add(TransactionType.DEPOSIT_CANCEL.getCode());
                }
                if (types.contains(TradeType.DEPOSIT_CONSUME.getCode())) {
                    tradeTypes.add(TransactionType.DEPOSIT_CONSUME.getCode());
                }
            }
        }

        if (CollectionUtils.isEmpty(tradeTypes) && !isFullTypeQuery) {
            //不查询
            statusTypeSubPayWayQuery.setValid(false);
        }

        statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(TransactionStatus.SUCCESS.getCode()));
        if (isFullTypeQuery) {
            if (notTradeTypes.size() > 0) {
                statusTypeSubPayWayQuery.setNotTypeList(notTradeTypes);
            }
        } else {
            statusTypeSubPayWayQuery.setTypeList(tradeTypes);
        }

        //收款失败||全部可见状态
        if ((!Objects.isNull(param.getSimple()) && !param.getSimple()) || (Objects.isNull(param.getSimple()) && types.contains(TradeType.PAY_ERROR.getCode()))) {
            StatusTypeSubPayWayQuery specialStatusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
            specialStatusTypeSubPayWayQuery.setTypeList(Lists.newArrayList(TransactionType.PAYMENT.getCode(), TransactionType.DEPOSIT_CONSUME.getCode()));
            specialStatusTypeSubPayWayQuery.setSubPayWayList(Lists.newArrayList(
                    SubPayWayType.SUB_PAYWAY_BARCODE.getCode(),
                    SubPayWayType.SUB_PAYWAY_QRCODE.getCode()));
            specialStatusTypeSubPayWayQuery.setNotStatusList(Lists.newArrayList(TransactionStatus.INIT.getCode(), TransactionStatus.IN_PROG.getCode(), TransactionStatus.SUCCESS.getCode()));
            //错误可见条件
            transactionQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.ERROR, specialStatusTypeSubPayWayQuery);

        }

        transactionQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);

        Map<String, Integer> payWayStrs = businessService.getAllPayWayNameEn().entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
        transactionQuery.setPayways(payWayTransform(CommonUtil.splitToListString(param.getTradeType()), payWayStrs));

        int pageNum = param.getPageNum() != null && param.getPageNum() > 0 ? param.getPageNum() : 1;
        int pageSize = param.getPageSize() != null && param.getPageSize() > 0 ? param.getPageSize() : 15;
        transactionQuery.setOffset((pageNum - 1) * pageSize);
        transactionQuery.setLimit(pageSize);

        transactionQuery.setMerchantId(param.getMerchant_id());

        transactionQuery.setTransactionSn(param.getTransactionSn());

        transactionQuery.setProductFlags(CommonUtil.splitToListString(param.getProductFlag()));

        List<String> terminalList = Lists.newArrayList();
        terminalList.addAll(CommonUtil.splitToListString(param.getPcplugin()));
        terminalList.addAll(CommonUtil.splitToListString(param.getPos()));
        terminalList.addAll(CommonUtil.splitToListString(param.getPosplus()));
        terminalList.addAll(CommonUtil.splitToListString(param.getMaya()));
        terminalList.addAll(CommonUtil.splitToListString(param.getMobile()));
        terminalList.addAll(CommonUtil.splitToListString(param.getQrcode()));
        terminalList.addAll(CommonUtil.splitToListString(param.getSocode()));
        terminalList.addAll(CommonUtil.splitToListString(param.getNpos2()));
        terminalList.addAll(CommonUtil.splitToListString(param.getGroupmeal()));
        terminalList.addAll(CommonUtil.splitToListString(param.getFaceScan()));
        if (!StringUtils.isEmpty(param.getTerminals())) {
            terminalList.addAll(CommonUtil.splitToListString(param.getTerminals()));
        }
        transactionQuery.setTerminalList(terminalList);

        if (!StringUtils.isEmpty(param.getNotContainTerminals())) {
            transactionQuery.setNotContainTerminalList(CommonUtil.splitToListString(param.getNotContainTerminals()));
        }

        if (!Objects.isNull(param.getStartTime())) {
            transactionQuery.setStartTime(param.getStartTime().getTime());
        } else {
            transactionQuery.setStartTime(new DateTime(System.currentTimeMillis()).minusMonths(1).withTimeAtStartOfDay().getMillis());
        }

        if (!Objects.isNull(param.getEndTime())) {
            transactionQuery.setBackUpTime(param.getEndTime().getTime());
        }

        if (Objects.isNull(param.getLastRecordTime())) {
            if (Objects.isNull(param.getEndTime())) {
                transactionQuery.setEndTime(new DateTime(System.currentTimeMillis()).plusDays(1).withTimeAtStartOfDay().getMillis());
            } else {
                transactionQuery.setEndTime(param.getEndTime().getTime());
            }
        } else {
            transactionQuery.setEndTime(param.getLastRecordTime().getTime());
        }

        transactionQuery.setOffsetHour(param.getOffsetHour());

        if (param.getOrderBy() != null) {
            transactionQuery.setOrderBy(AccountBookUtils.getOrderBy(param.getOrderBy()));

            long currentTimeMillis = System.currentTimeMillis();
            if (param.getLastRecordTime() == null && transactionQuery.getEndTime() > currentTimeMillis) {
                transactionQuery.setEndTime(currentTimeMillis);
            }
        }
        transactionQuery.setSummaryExts(CommonUtil.splitToListString(param.getSummaryExts()));
        transactionQuery.setMinOriginalAmount(param.getMinTotalAmount());
        transactionQuery.setMaxOriginalAmount(param.getMaxTotalAmount());
        return transactionQuery;
    }

    private static List<Integer> payWayTransform(List<String> payWays, Map<String, Integer> payWayStrs) {
        List<Integer> payWaysList = Lists.newArrayList();
        //支付宝特别处理
        if (payWays.contains(CommonConstant.ALI_PAY)) {
            payWaysList.add(Order.PAYWAY_ALIPAY2);
        }
        // ******** 储值卡特殊处理。 通过礼品卡做的储值卡 迁移到 独立的储值卡后， 查询需要全部查。 等过了一段时间，可联系产品经理去除此段逻辑。
        if (payWays.contains("GIFTCARD")){
            payWays.add("STORE");
        }else if (payWays.contains("STORE")){
            payWays.add("GIFTCARD");
        }

        Integer temp;
        for (String payWayStr : payWays) {
            temp = MapUtil.getInteger(payWayStrs, payWayStr);
            if (!Objects.isNull(temp)) {
                payWaysList.add(temp);
            }
        }
        return payWaysList;

    }

    public List<String> getSummaryExts() {
        return summaryExts;
    }

    public void setSummaryExts(List<String> summaryExts) {
        this.summaryExts = summaryExts;
    }

}
