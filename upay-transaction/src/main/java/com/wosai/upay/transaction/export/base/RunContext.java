package com.wosai.upay.transaction.export.base;

import avro.shaded.com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.transaction.enums.StatementTaskStyle;
import com.wosai.upay.transaction.model.StatementObjectConfig;
import com.wosai.upay.transaction.util.LanguageUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.wosai.upay.transaction.util.LanguageUtil.DEFAULT_LANGUAGE;

public class RunContext extends ConcurrentHashMap<String, Object> {

    public static final Logger logger = LoggerFactory.getLogger(ExportHandler.class);

    private static final ThreadLocal<? extends RunContext> threadLocal = ThreadLocal.withInitial(() -> {
        try {
            return RunContext.class.newInstance();
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    });

    @Override
    public void clear() {
        threadLocal.remove();
    }


    public static RunContext currentContext() {
        return threadLocal.get();
    }


    private static final String KEY_SERVICE = "run_service";


    public BaseExportService getService() {
        return (BaseExportService) currentContext().get(KEY_SERVICE);
    }

    public void setService(BaseExportService baseExportService) {
        currentContext().put(KEY_SERVICE, baseExportService);
    }

    private static final String KEY_TASK_LOG_ID = "run_task_log_id";

    public String getTaskLogId() {
        return (String) currentContext().get(KEY_TASK_LOG_ID);
    }

    public void setTaskLogId(String taskLogId) {
        currentContext().put(KEY_TASK_LOG_ID, taskLogId);
    }

    private static final String LOCAL_DIR_PATH = "local_dir_path";

    public String getLocalDirPath() {
        return (String) currentContext().get(LOCAL_DIR_PATH);
    }

    public void setLocalDirPath(String path) {
        currentContext().put(LOCAL_DIR_PATH, path);
    }


    private static final String KEY_START_TIME = "start_time";
    private static final String KEY_END_TIME = "end_time";
    private static final String KEY_OPERATE_TIME = "operate_time";

    public void start() {
        currentContext().put(KEY_START_TIME, System.currentTimeMillis());
    }

    public void finish() {
        RunContext context = currentContext();
        if (context.get(KEY_END_TIME) == null) {
            long endTime = System.currentTimeMillis();
            context.put(KEY_END_TIME, endTime);
        }
    }


    //   运行时间信息
    public Map<String, Object> runInfo() {
        RunContext context = currentContext();
        Map<String, Object> info = Maps.newHashMap();
        Long start = (Long) context.getOrDefault(KEY_START_TIME, 0L);
        Long end = (Long) context.getOrDefault(KEY_END_TIME, 0L);
        info.put(KEY_START_TIME, start);
        info.put(KEY_END_TIME, end);
        info.put(KEY_OPERATE_TIME, end - start);
        return info;
    }

    //执行时间
    public long operatorTime() {
        RunContext context = currentContext();
        Long start = (Long) context.getOrDefault(KEY_START_TIME, 0L);
        Long end = (Long) context.getOrDefault(KEY_END_TIME, 0L);
        return end - start;
    }


    //  运行产生数据量信息
    private static final String KEY_TOTAL_ROW = "total_row";

    public void setTotalRow(long row) {
        currentContext().put(KEY_TOTAL_ROW, row);
    }

    public long totalRow() {
        return (long) currentContext().getOrDefault(KEY_TOTAL_ROW, 0L);
    }


    private static final String KEY_EXPORT_FILE_URL = "statement_oss_url";

    //运行文件oss路径
    public void setExportFileUrl(String url) {
        currentContext().put(KEY_EXPORT_FILE_URL, url);
    }

    public String getExportFileUrl() {
        return (String) currentContext().get(KEY_EXPORT_FILE_URL);
    }


    private static final String KEY_RUN_CONFIG = "run_config";

    //导出配置信息
    public Map getRunConfig() {
        return (Map) currentContext().getOrDefault(KEY_RUN_CONFIG, Maps.newHashMap());
    }

    public void setRunConfig(Map config) {
        config = config == null ? Maps.newHashMap() : config;
        currentContext().put(KEY_RUN_CONFIG, config);
    }

    private static final String KEY_RUN_DETAIL = "run_detail";

    //导出详情信息
    public Map getRunDetail() {
        return (Map) currentContext().getOrDefault(KEY_RUN_DETAIL, Maps.newHashMap());
    }

    public void setRunDetail(Map detail) {
        detail = detail == null ? Maps.newHashMap() : detail;
        currentContext().put(KEY_RUN_DETAIL, detail);
    }

    public boolean crossMchRefundEnable() {
        try {
            Map detailParams = (Map) BeanUtil.getProperty(getRunConfig(), StatementObjectConfig.SHEET_DETAIL_PARAMS);
            int sheetDetailType = BeanUtil.getPropInt(detailParams, StatementObjectConfig.SHEET_DETAIL_TYPE, 0);
            return sheetDetailType == 1;
        } catch (Exception e) {
            logger.error("query  cross mch  refund enable error config :{}", getRunConfig(), e);
            return false;
        }
    }


    public void initLanguage() {
        String language = BeanUtil.getPropString(getRunConfig(), StatementObjectConfig.LANGUAGE, DEFAULT_LANGUAGE);
        LanguageUtil.setLanguage(language);
    }


    private static final String KEY_INCLUDES = "includes";

    private static final String KEY_IS_KA_STATEMENT = "ka_statement";

    public String getExportIncludes() {
        return (String) currentContext().get(KEY_INCLUDES);
    }

    public void setExportIncludes(String includes) {
        currentContext().put(KEY_INCLUDES, includes);
    }

    public boolean getIsKAStatement() {
        return (boolean) currentContext().get(KEY_IS_KA_STATEMENT);
    }

    public void setIsKAStatement(boolean kaStatement) {
        currentContext().put(KEY_IS_KA_STATEMENT, kaStatement);
    }
    private static final String KEY_STYLE = "style";

    public StatementTaskStyle getExportStyle() {
        return (StatementTaskStyle) currentContext().get(KEY_STYLE);
    }

    public void setExportStyle(StatementTaskStyle includes) {
        currentContext().put(KEY_STYLE, includes);
    }


}