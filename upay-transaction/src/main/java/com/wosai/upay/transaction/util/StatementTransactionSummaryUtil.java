package com.wosai.upay.transaction.util;

import com.google.common.collect.Lists;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.upay.ProfitSharing;
import com.wosai.profit.sharing.util.UpayProfitSharingUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.CashDesk;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.export.base.RunContext;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.StatementSummary;
import com.wosai.upay.transaction.model.StatementTaskLog;
import com.wosai.upay.transaction.model.Transaction;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.wosai.upay.transaction.util.LanguageUtil.getValue;

/**
 * <AUTHOR>
 */
public class StatementTransactionSummaryUtil {
    public static  final Logger logger = LoggerFactory.getLogger(StatementTransactionSummaryUtil.class);


    /**
     * 按商户，门店，收银台，终端，收银员五个维度的汇总流水数据
     *
     * @param transactions
     * @param summary
     * @return
     */
    public static void updateStatementSummaryInfo(List<Map<String, Object>> transactions, Map summary) {
        //保存业务对象名称等基本信息
        Map merchantBasicInfoMap = getSummaryInitialData(summary, StatementSummary.MERCHANT_BASIC_INFO_MAP);
        Map storeBasicInfoMap = getSummaryInitialData(summary, StatementSummary.STORE_BASIC_INFO_MAP);
        Map cashDeskBasicInfoMap = getSummaryInitialData(summary, StatementSummary.CASH_DESK_BASIC_INFO_MAP);
        Map terminalBasicInfoMap = getSummaryInitialData(summary, StatementSummary.TERMINAL_BASIC_INFO_MAP);
        Map operatorBasicInfoMap = getSummaryInitialData(summary, StatementSummary.OPERATOR_BASIC_INFO_MAP);
        Map cashDeskCashierBasicInfoMap = getSummaryInitialData(summary, StatementSummary.CASH_DESK_CASHIER_BASIC_INFO_MAP);
        Map cashierBasicInfoMap = getSummaryInitialData(summary, StatementSummary.CASHIER_BASIC_INFO_MAP);

        //保存业务对象上下级关联关系
        Map<String, List> merchantSnStoresMap = getSummaryInitialData(summary, StatementSummary.MERCHANT_SN_STORES_MAP);
        Map<String, List> storeSnCashDesksMap = getSummaryInitialData(summary, StatementSummary.STORE_SN_CASHDESKS_MAP);
        Map<String, List> storeSnTerminalsMap = getSummaryInitialData(summary, StatementSummary.STORE_SN_TERMINALS_MAP);
        Map<String, List> terminalSnOperatorsMap = getSummaryInitialData(summary, StatementSummary.TERMINAL_SN_OPERATORS_MAP);
        Map<String, Map<String, List>> cashDeskStoreSnCashierMap = getSummaryInitialData(summary, StatementSummary.CASH_DESK_STORE_SN_CASHIERS_MAP);
        Map<String, List> storeSnCashierMap = getSummaryInitialData(summary, StatementSummary.STORE_SN_CASHIERS_MAP);

        //保存业务对象汇总数据
        Map paywaySummaryMap = getSummaryInitialData(summary, StatementSummary.PAYWAY_SUMMARY_MAP);
        Map merchantSummaryMap = getSummaryInitialData(summary, StatementSummary.MERCHANT_SUMMARY_MAP);
        Map storeSummaryMap = getSummaryInitialData(summary, StatementSummary.STORE_SUMMARY_MAP);
        Map cashDeskSummaryMap = getSummaryInitialData(summary, StatementSummary.CASH_DESK_SUMMARY_MAP);
        Map terminalSummaryMap = getSummaryInitialData(summary, StatementSummary.TERMINAL_SUMMARY_MAP);
        Map operatorSummaryMap = getSummaryInitialData(summary, StatementSummary.OPERATOR_SUMMARY_MAP);
        Map unionSummaryMap = getSummaryInitialData(summary, StatementSummary.UNION_SUMMARY_MAP);
        Map cashDeskCashierSummaryMap = getSummaryInitialData(summary, StatementSummary.CASH_DESK_CASHIER_SUMMARY_MAP);
        Map cashierSummaryMap = getSummaryInitialData(summary, StatementSummary.CASHIER_SUMMARY_MAP);

        //初始化支付通道统计汇总字段
        Map recordTemplate = getRecordTemplate();

        //是否展示收银台信息
        boolean containsCashDesk = RunContext.currentContext().getExportIncludes().contains(StatementTaskLog.INCLUDE_CASH_DESK + "");

        for (Map transaction : transactions) {
            int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
            int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
            long originalAmount = MapUtil.getLongValue(transaction, Transaction.ORIGINAL_AMOUNT);
            long discountAmount = MapUtil.getLongValue(transaction, Transaction.WOSAI_FAVORABLE_AMOUNT);
            //收钱吧商户优惠 (红包 + 立减)
            long merchantDiscount = MapUtil.getLongValue(transaction, Transaction.MCH_FAVORABLE_AMOUNT);

            String merchantSn = MapUtil.getString(transaction, ConstantUtil.KEY_MERCHANT_SN);
            String storeSn = MapUtil.getString(transaction, ConstantUtil.KEY_STORE_SN);
            String terminalSn = MapUtil.getString(transaction, ConstantUtil.KEY_TERMINAL_SN);
            String operator = MapUtil.getString(transaction, Transaction.OPERATOR);
            String operatorName = MapUtil.getString(transaction, "operator_name");
            String currency = MapUtil.getString(transaction, TransactionParam.CURRENCY);
            long channelDiscount = MapUtil.getLongValue(transaction, Transaction.CHANNEL_AGENT_FAVORABLE_AMOUNT);
            long channelMchDiscount = MapUtil.getLongValue(transaction, Transaction.CHANNEL_MCH_FAVORABLE_AMOUNT);
            long channelMchTopUpDiscount = MapUtil.getLongValue(transaction, Transaction.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT);
            long receiveAmount = MapUtil.getLongValue(transaction, Transaction.ACTUAL_RECEIVE_AMOUNT);
            long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            long tradeFee = MapUtil.getLongValue(transaction, Transaction.FEE);
            long clearingAmount = MapUtil.getLongValue(transaction, Transaction.CLEARING_AMOUNT);
            long sharingAmount = MapUtil.getLongValue(transaction, Transaction.SHARING_AMOUNT, 0);
            long tradeServiceSharingAmount = MapUtil.getLongValue(transaction, Transaction.TRADE_SERVICE_SHARING_AMOUNT, 0);
            long hbfqMchDiscountAmount = MapUtil.getLongValue(transaction, Transaction.FQ_MCH_DISCOUNT_AMOUNT, 0);
            Object crossMchParam = BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS_CROSS_MCH_REFUND);
            String productFlag = MapUtil.getString(transaction, Transaction.PRODUCT_FLAG, "");
            //商家记账结算金额
            long chargeClearingAmount = (Objects.equals(type, TransactionType.CHARGE.getCode()) || Objects.equals(type, TransactionType.CHARGE_REFUND.getCode())) ? clearingAmount : 0;
            //储值核销实收金额
            long storePayReceiveAmount = Order.PAYWAY_STORE == payway ? clearingAmount + sharingAmount + tradeServiceSharingAmount : 0;
            //现金储值充值结算金额
            long storeInCashClearingAmount = (type == TransactionType.STORE_IN.getCode() || type == TransactionType.IN_REFUND.getCode()) && Order.PAYWAY_CASH == payway ? clearingAmount : 0;
            //其他方式记账储值充值结算金额
            long storeInOtherChargeClearingAmount = (type == TransactionType.STORE_IN.getCode() || type == TransactionType.IN_REFUND.getCode()) && Order.PAYWAY_OTH_CHG == payway ? clearingAmount : 0;
            /*
              直清结算金额 liquidation_next_day = false
              但需要剔除记账交易、储值核销交易、现金储值充值交易
             */
            long liquidationClearingAmount = 0;
            boolean liquidationNextDay = BeanUtil.getPropBoolean(transaction, TransactionParam.LIQUIDATION_NEXT_DAY, false);
            if (type != TransactionType.CHARGE.getCode()
                    && type != TransactionType.CHARGE_REFUND.getCode()
                    && type != TransactionType.STORE_IN.getCode()
                    && type != TransactionType.IN_REFUND.getCode()
                    && Order.PAYWAY_STORE != payway
                    && !liquidationNextDay) {
                liquidationClearingAmount = clearingAmount;
            }
            long storeInLiquidationClearingAmount = 0;
            if (((type == TransactionType.STORE_IN.getCode() || type == TransactionType.IN_REFUND.getCode()) && Order.PAYWAY_CASH != payway && Order.PAYWAY_OTH_CHG != payway)
                    && !liquidationNextDay) {
                storeInLiquidationClearingAmount = clearingAmount;
            }
            long foreignCardTradeClearingAmount = 0;
            //外卡交易
            if (TransactionUtil.isForeignCardTrade(transaction)) {
                foreignCardTradeClearingAmount = clearingAmount;
            }

            //非（支付或取消退款），金额取负数
            if (TransactionTypeRelatedUtil.isNegativeType(type)) {
                originalAmount = originalAmount * -1;
                discountAmount = discountAmount * -1;
                merchantDiscount = merchantDiscount * -1;
                channelDiscount *= -1;
                channelMchDiscount *= -1;
                channelMchTopUpDiscount *= -1;
                receiveAmount *= -1;
                tradeFee *= -1;
                clearingAmount *= -1;
                sharingAmount *= -1;
                tradeServiceSharingAmount *= -1;
                hbfqMchDiscountAmount *= -1;
                chargeClearingAmount *= -1;
                storePayReceiveAmount *= -1;
                storeInCashClearingAmount *= -1;
                storeInOtherChargeClearingAmount *= -1;
                liquidationClearingAmount *= -1;
                storeInLiquidationClearingAmount *= -1;
                foreignCardTradeClearingAmount *= -1;
            }

            Map record = new HashMap(16);
            //默认值
            record.putAll(recordTemplate);

            record.put("merchantSn", merchantSn);
            record.put("storeSn", storeSn);
            record.put("terminalSn", terminalSn);
            record.put("operatorName", operatorName);
            record.put("operator", operator);
            record.put("payway", String.valueOf(payway));
            record.put("merchantSn-storeSn-payway", merchantSn + storeSn + payway);

            String prefix = "";
            if (TransactionTypeRelatedUtil.isStoreType(type)) {
                prefix += StatementSummary.PREFIX;
            }
            //成交到账金额 = 结算金额 - 商家记账结算金额 - 储值核销实收金额 - 直清交易结算金额
            //此处减去储值核销实收金额而不是储值核销结算金额的原因为，储值核销存在分账金额之后，该分账金额是从余额里面扣除的， 故储值核销的分账金额需从非储值交易的结算金额中扣除
            long dealToAccountAmount = clearingAmount - chargeClearingAmount - storePayReceiveAmount - liquidationClearingAmount;
            if (StatementSummary.PREFIX.equals(prefix)) {
                //储值充值到账金额 = 结算金额 - 现金储值充值结算金额 - 其他方式记账储值结算金额 - 直清储值充值结算金额
                dealToAccountAmount = clearingAmount - storeInCashClearingAmount - storeInOtherChargeClearingAmount - storeInLiquidationClearingAmount;
            }

            record.put(prefix + "tradeCount", TransactionTypeRelatedUtil.isPositiveType(type) ? 1 : 0);
            record.put(prefix + "tradeAmount", TransactionTypeRelatedUtil.isPositiveType(type) ? originalAmount : 0);
            record.put(prefix + "refundCount", TransactionTypeRelatedUtil.isNegativeType(type) ? 1 : 0);
            record.put(prefix + "refundAmount", TransactionTypeRelatedUtil.isNegativeType(type) ? originalAmount : 0);
            if (DBSelectContext.getContext().getSelectDb() != UpayQueryType.UPAY_SWIPE) {
                record.put(prefix + "merchantDiscount", merchantDiscount);
                record.put(prefix + "shouqianbaDiscount", discountAmount);
                record.put(prefix + "channelDiscount", channelDiscount);
                record.put(prefix + "channelMchDiscount", channelMchDiscount);
                record.put(prefix + "channelMchTopUpDiscount", channelMchTopUpDiscount);
            }
            record.put(prefix + "receiveAmount", receiveAmount);
            record.put(prefix + "tradeFee", tradeFee);
            record.put(prefix + "currency", currency);
            record.put(prefix + "incomeAmount", clearingAmount);
            record.put(prefix + payway + ":incomeAmount", clearingAmount);
            record.put(prefix + "sharingAmount", sharingAmount);
            record.put(prefix + "tradeServiceSharingAmount", tradeServiceSharingAmount);
            record.put(prefix + StatementSummary.HBFQ_MCH_DISCOUNT_AMOUNT, hbfqMchDiscountAmount);
            record.put("chargeIncomeAmount", chargeClearingAmount);
            record.put("storePayReceiveAmount", storePayReceiveAmount);
            record.put("liquidationIncomeAmount", liquidationClearingAmount);
            if (StatementSummary.PREFIX.equals(prefix)) {
                record.put(prefix + "cashIncomeAmount", storeInCashClearingAmount);
                record.put(prefix + "otherChargeIncomeAmount", storeInOtherChargeClearingAmount);
                record.put(prefix + "liquidationIncomeAmount", storeInLiquidationClearingAmount);
            }
            record.put(prefix + "foreignCardIncomeAmount", foreignCardTradeClearingAmount);
            record.put(prefix + "dealToAccountAmount", dealToAccountAmount);
            if (crossMchParam != null) {
                record.put(prefix + "crossMchRefundCount", 1);
                record.put(prefix + "crossMchRefundAmount", effectiveAmount);
            }
            if (StringUtil.empty(terminalSn)) {
                terminalSn = storeSn;
            }
            //操作员属于不同的门店下
            operator = storeSn + "@_@" + terminalSn + "@_@" + operator;

            //保存业务对象 基本信息
            merchantBasicInfoMap.put(merchantSn, CollectionUtil.hashMap(
                    ConstantUtil.KEY_MERCHANT_SN, MapUtil.getString(transaction, ConstantUtil.KEY_MERCHANT_SN),
                    ConstantUtil.KEY_MERCHANT_NAME, MapUtil.getString(transaction, ConstantUtil.KEY_MERCHANT_NAME),
                    "merchant_currency", MapUtil.getString(transaction, "merchant_currency")

            ));

            storeBasicInfoMap.put(storeSn, CollectionUtil.hashMap(
                    ConstantUtil.KEY_STORE_NAME, MapUtil.getString(transaction, ConstantUtil.KEY_STORE_NAME),
                    "store_client_sn", MapUtil.getString(transaction, "store_client_sn"),
                    "merchant_currency", MapUtil.getString(transaction, "merchant_currency")
            ));

            terminalBasicInfoMap.put(terminalSn, CollectionUtil.hashMap(
                    "terminal_name", transaction.get("terminal_name"),
                    "terminal_client_sn", transaction.get("terminal_client_sn"),
                    "terminal_type", getValue(OrderUtil.getTerminalTypeLanguageKey(MapUtil.getString(transaction, "terminal_type"))),
                    "terminal_device_fingerprint", transaction.get("terminal_device_fingerprint"),
                    "merchant_currency", MapUtil.getString(transaction, "merchant_currency")
            ));
            operatorBasicInfoMap.put(operator, CollectionUtil.hashMap(
                    "operator_name", transaction.get("operator_name"),
                    "merchant_currency", MapUtil.getString(transaction, "merchant_currency")
            ));

            //保存业务对象
            updateBusinessRelation(merchantSnStoresMap, merchantSn, storeSn);
            updateBusinessRelation(storeSnTerminalsMap, storeSn, terminalSn);
            updateBusinessRelation(terminalSnOperatorsMap, terminalSn, operator);

            //保存业务对象汇总数据
            Set<String> keys = new HashSet<>(recordTemplate.keySet());
            updateStatementSummaryInfoByBusiness(keys, paywaySummaryMap, record, payway + "");
            updateStatementSummaryInfoByBusiness(keys, merchantSummaryMap, record, merchantSn);
            updateStatementSummaryInfoByBusiness(keys, storeSummaryMap, record, storeSn);
            updateStatementSummaryInfoByBusiness(keys, terminalSummaryMap, record, terminalSn);
            updateStatementSummaryInfoByBusiness(keys, operatorSummaryMap, record, operator);
            updateStatementSummaryInfoByBusiness(keys, unionSummaryMap, record, merchantSn + storeSn + payway);

            //更新汇总数据
            summary.put(StatementSummary.MERCHANT_BASIC_INFO_MAP, merchantBasicInfoMap);
            summary.put(StatementSummary.STORE_BASIC_INFO_MAP, storeBasicInfoMap);
            summary.put(StatementSummary.TERMINAL_BASIC_INFO_MAP, terminalBasicInfoMap);
            summary.put(StatementSummary.OPERATOR_BASIC_INFO_MAP, operatorBasicInfoMap);

            summary.put(StatementSummary.MERCHANT_SN_STORES_MAP, merchantSnStoresMap);
            summary.put(StatementSummary.STORE_SN_TERMINALS_MAP, storeSnTerminalsMap);
            summary.put(StatementSummary.TERMINAL_SN_OPERATORS_MAP, terminalSnOperatorsMap);

            summary.put(StatementSummary.PAYWAY_SUMMARY_MAP, paywaySummaryMap);
            summary.put(StatementSummary.MERCHANT_SUMMARY_MAP, merchantSummaryMap);
            summary.put(StatementSummary.STORE_SUMMARY_MAP, storeSummaryMap);
            summary.put(StatementSummary.TERMINAL_SUMMARY_MAP, terminalSummaryMap);
            summary.put(StatementSummary.OPERATOR_SUMMARY_MAP, operatorSummaryMap);
            summary.put(StatementSummary.UNION_SUMMARY_MAP, unionSummaryMap);

            if (containsCashDesk) {
                String cashDeskId = BeanUtil.getPropString(transaction, Transaction.CASH_DESK_ID);
                String cashDeskName = BeanUtil.getPropString(transaction, Transaction.CASH_DESK_NAME);
                if (Strings.isNotBlank(cashDeskId)) {
                    record.put("cashDeskId", cashDeskId);

                    cashDeskBasicInfoMap.put(cashDeskId, CollectionUtil.hashMap(
                            "cash_desk_name", cashDeskName,
                            "merchant_currency", BeanUtil.getPropString(transaction, "merchant_currency")
                    ));

                    //保存业务对象
                    updateBusinessRelation(storeSnCashDesksMap, storeSn, cashDeskId);
                    //保存业务对象汇总数据
                    updateStatementSummaryInfoByBusiness(keys, cashDeskSummaryMap, record, cashDeskId);
                    //更新汇总数据
                    summary.put(StatementSummary.CASH_DESK_BASIC_INFO_MAP, cashDeskBasicInfoMap);
                    summary.put(StatementSummary.CASH_DESK_SUMMARY_MAP, cashDeskSummaryMap);
                    summary.put(StatementSummary.STORE_SN_CASHDESKS_MAP, storeSnCashDesksMap);

                    // 收银台批次
                    String cashierBatchId = MapUtil.getString(transaction, "cashier_batch_id"); 
                    if (!StringUtil.empty(cashierBatchId)) {
                        if (!cashDeskCashierBasicInfoMap.containsKey(cashierBatchId)) {
                            Map<String, Object> basicInfo = MapUtil.copyInclusive(transaction, "change_shifts_start_date", "change_shifts_end_date", "cashier_name", "merchant_currency", "change_shifts_batch");
                            basicInfo.put("cash_desk_id", cashDeskId);
                            cashDeskCashierBasicInfoMap.put(cashierBatchId, basicInfo);
                        }
                        //保存业务对象结构
                        Map<String, List> storeSnCashDeskMap = cashDeskStoreSnCashierMap.get(storeSn);
                        if (storeSnCashDeskMap == null) {
                            storeSnCashDeskMap = new HashMap<>();
                            cashDeskStoreSnCashierMap.put(storeSn, storeSnCashDeskMap);
                        }
                        updateBusinessRelation(storeSnCashDeskMap, cashDeskId, cashierBatchId);
                        //保存业务对象汇总数据
                        updateStatementSummaryInfoByBusiness(keys, cashDeskCashierSummaryMap, record, cashierBatchId);
                        //更新汇总数据
                        summary.put(StatementSummary.CASH_DESK_CASHIER_BASIC_INFO_MAP, cashDeskCashierBasicInfoMap);
                        summary.put(StatementSummary.CASH_DESK_CASHIER_SUMMARY_MAP, cashDeskCashierSummaryMap);
                        summary.put(StatementSummary.CASH_DESK_STORE_SN_CASHIERS_MAP, cashDeskStoreSnCashierMap);
                    }
                }
            }
            String cashierId = MapUtil.getString(transaction, "cashier_id"); 
            if (!StringUtil.empty(cashierId)) {
                // 收银员名称可能会发生变更，汇总时按照收银员id+收银员名称作为唯一key
                String cashierName = MapUtil.getString(transaction, "cashier_name");
                String unionCashierKey = cashierId + "|" + cashierName;
                if (!cashDeskBasicInfoMap.containsKey(unionCashierKey)) {
                    cashierBasicInfoMap.put(unionCashierKey, MapUtil.copyInclusive(transaction, "cashier_id", "cashier_name", "cashier_phone", "merchant_currency"));
                    //保存业务对象
                    updateBusinessRelation(storeSnCashierMap, storeSn, unionCashierKey);
                    //保存业务对象汇总数据
                    updateStatementSummaryInfoByBusiness(keys, cashierSummaryMap, record, storeSn + unionCashierKey);
                    //更新汇总数据
                    summary.put(StatementSummary.CASHIER_BASIC_INFO_MAP, cashierBasicInfoMap);
                    summary.put(StatementSummary.CASHIER_SUMMARY_MAP, cashierSummaryMap);
                    summary.put(StatementSummary.STORE_SN_CASHIERS_MAP, storeSnCashierMap);
                }
            }
        }
    }

    /**
     * 更新收银台批次汇总数据
     *
     * @param transactions
     * @param summary
     * @return
     */
    public static void updateCashDeskStatementSummaryInfo(List<Map<String, Object>> transactions, Map summary) {

        //保存业务对象汇总数据
        Map cashierSummaryMap = getSummaryInitialData(summary, StatementSummary.CASH_DESK_CASHIER_SUMMARY_MAP);

        //初始化支付通道统计汇总字段
        Map recordTemplate = getRecordTemplate();

        //是否展示收银台信息
        boolean containsCashDesk = RunContext.currentContext().getExportIncludes().contains(StatementTaskLog.INCLUDE_CASH_DESK + "");

        for (Map transaction : transactions) {
            int type = MapUtil.getIntValue(transaction, Transaction.TYPE);
            int payway = MapUtil.getIntValue(transaction, Transaction.PAYWAY);
            long originalAmount = MapUtil.getLongValue(transaction, Transaction.ORIGINAL_AMOUNT);
            long discountAmount = MapUtil.getLongValue(transaction, Transaction.WOSAI_FAVORABLE_AMOUNT);
            //收钱吧商户优惠 (红包 + 立减)
            long merchantDiscount = MapUtil.getLongValue(transaction, Transaction.MCH_FAVORABLE_AMOUNT);

            String merchantSn = MapUtil.getString(transaction, ConstantUtil.KEY_MERCHANT_SN);
            String storeSn = MapUtil.getString(transaction, ConstantUtil.KEY_STORE_SN);
            String terminalSn = MapUtil.getString(transaction, ConstantUtil.KEY_TERMINAL_SN);
            String operator = MapUtil.getString(transaction, Transaction.OPERATOR);
            String operatorName = MapUtil.getString(transaction, "operator_name");
            String currency = MapUtil.getString(transaction, TransactionParam.CURRENCY);
            long channelDiscount = MapUtil.getLongValue(transaction, Transaction.CHANNEL_AGENT_FAVORABLE_AMOUNT);
            long channelMchDiscount = MapUtil.getLongValue(transaction, Transaction.CHANNEL_MCH_FAVORABLE_AMOUNT);
            long channelMchTopUpDiscount = MapUtil.getLongValue(transaction, Transaction.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT);
            long receiveAmount = MapUtil.getLongValue(transaction, Transaction.ACTUAL_RECEIVE_AMOUNT);
            long effectiveAmount = MapUtil.getLongValue(transaction, Transaction.EFFECTIVE_AMOUNT);
            long tradeFee = MapUtil.getLongValue(transaction, Transaction.FEE);
            long clearingAmount = MapUtil.getLongValue(transaction, Transaction.CLEARING_AMOUNT);
            long sharingAmount = MapUtil.getLongValue(transaction, Transaction.SHARING_AMOUNT, 0);
            long tradeServiceSharingAmount = MapUtil.getLongValue(transaction, Transaction.TRADE_SERVICE_SHARING_AMOUNT, 0);
            long hbfqMchDiscountAmount = MapUtil.getLongValue(transaction, Transaction.FQ_MCH_DISCOUNT_AMOUNT, 0);
            Object crossMchParam = BeanUtil.getNestedProperty(transaction, Transaction.EXTRA_PARAMS_CROSS_MCH_REFUND);
            //商家记账结算金额
            long chargeClearingAmount = (Objects.equals(type, TransactionType.CHARGE.getCode()) || Objects.equals(type, TransactionType.CHARGE_REFUND.getCode())) ? clearingAmount : 0;
            //储值核销实收金额
            long storePayReceiveAmount = Order.PAYWAY_STORE == payway ? clearingAmount + sharingAmount + tradeServiceSharingAmount: 0;
            //现金储值充值结算金额
            long storeInCashClearingAmount = (type == TransactionType.STORE_IN.getCode() || type == TransactionType.IN_REFUND.getCode()) && Order.PAYWAY_CASH == payway ? clearingAmount : 0;
            //其他方式记账储值充值结算金额
            long storeInOtherChargeClearingAmount = (type == TransactionType.STORE_IN.getCode() || type == TransactionType.IN_REFUND.getCode()) && Order.PAYWAY_OTH_CHG == payway ? clearingAmount : 0;
            /*
              直清结算金额 liquidation_next_day = false
              但需要剔除记账交易、储值核销交易、现金储值充值交易
             */
            long liquidationClearingAmount = 0;
            boolean liquidationNextDay = BeanUtil.getPropBoolean(transaction, TransactionParam.LIQUIDATION_NEXT_DAY, false);
            if (type != TransactionType.CHARGE.getCode()
                    && type != TransactionType.CHARGE_REFUND.getCode()
                    && type != TransactionType.STORE_IN.getCode()
                    && type != TransactionType.IN_REFUND.getCode()
                    && Order.PAYWAY_STORE != payway
                    && !liquidationNextDay) {
                liquidationClearingAmount = clearingAmount;
            }
            long storeInLiquidationClearingAmount = 0;
            if (((type == TransactionType.STORE_IN.getCode() || type == TransactionType.IN_REFUND.getCode()) && Order.PAYWAY_CASH != payway && Order.PAYWAY_OTH_CHG != payway)
                    && !liquidationNextDay) {
                storeInLiquidationClearingAmount = clearingAmount;
            }

            //非（支付或取消退款），金额取负数
            if (TransactionTypeRelatedUtil.isNegativeType(type)) {
                originalAmount = originalAmount * -1;
                discountAmount = discountAmount * -1;
                merchantDiscount = merchantDiscount * -1;
                channelDiscount *= -1;
                channelMchDiscount *= -1;
                channelMchTopUpDiscount *= -1;
                receiveAmount *= -1;
                tradeFee *= -1;
                clearingAmount *= -1;
                sharingAmount *= -1;
                tradeServiceSharingAmount *= -1;
                hbfqMchDiscountAmount *= -1;
                chargeClearingAmount *= -1;
                storePayReceiveAmount *= -1;
                storeInCashClearingAmount *= -1;
                storeInOtherChargeClearingAmount *= -1;
                liquidationClearingAmount *= -1;
                storeInLiquidationClearingAmount *= -1;
            }

            Map record = new HashMap(16);
            //默认值
            record.putAll(recordTemplate);

            record.put("merchantSn", merchantSn);
            record.put("storeSn", storeSn);
            record.put("terminalSn", terminalSn);
            record.put("operatorName", operatorName);
            record.put("operator", operator);
            record.put("payway", String.valueOf(payway));
            record.put("merchantSn-storeSn-payway", merchantSn + storeSn + payway);

            String prefix = "";
            if (TransactionTypeRelatedUtil.isStoreType(type)) {
                prefix += StatementSummary.PREFIX;
            }

            //成交到账金额 = 结算金额 - 商家记账结算金额 - 储值核销实收金额 - 直清交易结算金额
            //此处减去储值核销实收金额而不是储值核销结算金额的原因为，储值核销存在分账金额之后，该分账金额是从余额里面扣除的， 故储值核销的分账金额需从非储值交易的结算金额中扣除
            long dealToAccountAmount = clearingAmount - chargeClearingAmount - storePayReceiveAmount - liquidationClearingAmount;
            if (StatementSummary.PREFIX.equals(prefix)) {
                //储值充值到账金额 = 结算金额 - 现金储值充值结算金额 - 其他方式记账储值结算金额 - 直清储值充值结算金额
                dealToAccountAmount = clearingAmount - storeInCashClearingAmount - storeInOtherChargeClearingAmount - storeInLiquidationClearingAmount;
            }

            record.put(prefix + "tradeCount", TransactionTypeRelatedUtil.isPositiveType(type) ? 1 : 0);
            record.put(prefix + "tradeAmount", TransactionTypeRelatedUtil.isPositiveType(type) ? originalAmount : 0);
            record.put(prefix + "refundCount", TransactionTypeRelatedUtil.isNegativeType(type) ? 1 : 0);
            record.put(prefix + "refundAmount", TransactionTypeRelatedUtil.isNegativeType(type) ? originalAmount : 0);
            if (DBSelectContext.getContext().getSelectDb() != UpayQueryType.UPAY_SWIPE) {
                record.put(prefix + "merchantDiscount", merchantDiscount);
                record.put(prefix + "shouqianbaDiscount", discountAmount);
                record.put(prefix + "channelDiscount", channelDiscount);
                record.put(prefix + "channelMchDiscount", channelMchDiscount);
                record.put(prefix + "channelMchTopUpDiscount", channelMchTopUpDiscount);
            }
            record.put(prefix + "receiveAmount", receiveAmount);
            record.put(prefix + "tradeFee", tradeFee);
            record.put(prefix + "currency", currency);
            record.put(prefix + "incomeAmount", clearingAmount);
            record.put(prefix + payway + ":incomeAmount", clearingAmount);
            record.put(prefix + "sharingAmount", sharingAmount);
            record.put(prefix + "tradeServiceSharingAmount", tradeServiceSharingAmount);
            record.put(prefix + StatementSummary.HBFQ_MCH_DISCOUNT_AMOUNT, hbfqMchDiscountAmount);
            record.put("chargeIncomeAmount", chargeClearingAmount);
            record.put("storePayReceiveAmount", storePayReceiveAmount);
            record.put("liquidationIncomeAmount", liquidationClearingAmount);
            if(StatementSummary.PREFIX.equals(prefix)) {
                record.put(prefix + "cashIncomeAmount", storeInCashClearingAmount);
                record.put(prefix + "otherChargeIncomeAmount", storeInOtherChargeClearingAmount);
                record.put(prefix + "liquidationIncomeAmount", storeInLiquidationClearingAmount);
            }
            record.put(prefix + "dealToAccountAmount", dealToAccountAmount);
            if (crossMchParam != null) {
                record.put(prefix + "crossMchRefundCount", 1);
                record.put(prefix + "crossMchRefundAmount", effectiveAmount);
            }

            //保存业务对象汇总数据
            Set<String> keys = new HashSet<>(recordTemplate.keySet());

            if (containsCashDesk) {
                String cashDeskId = BeanUtil.getPropString(transaction, Transaction.CASH_DESK_ID);
                String cashDeskName = BeanUtil.getPropString(transaction, Transaction.CASH_DESK_NAME);
                String cashierBatchId = MapUtil.getString(transaction, "cashier_batch_id"); 
                if (!StringUtil.empty(cashierBatchId)) {
                    //保存业务对象汇总数据
                    updateStatementSummaryInfoByBusiness(keys, cashierSummaryMap, record, cashierBatchId);
                    //更新汇总数据
                    summary.put(StatementSummary.CASH_DESK_CASHIER_SUMMARY_MAP, cashierSummaryMap);
                }
            }
        }
    }

    /**
     * 按商户，门店，收银台，终端，收银员，班次收银员六个维度的汇总流水数据
     *
     * @param keys
     * @param businessSummaryMap
     * @param record
     * @param businessKey
     * @return
     */
    private static void updateStatementSummaryInfoByBusiness(Set<String> keys, Map businessSummaryMap, Map record, String businessKey) {
        Map businessSummary = (Map) businessSummaryMap.get(businessKey);
        if (businessSummary == null) {
            businessSummary = new HashMap(16);
        }
        for (String summaryField : keys) {
            long value = MapUtil.getLongValue(businessSummary, summaryField, 0);
            try {
                long newValue = MapUtil.getLongValue(record, summaryField);
                value += newValue;
            } catch (Exception e) {
                //此处不打印日志，因为keys里面有些字段是不能相加的，如果写日志，会有很多错误日志， 如果开发新功能牵涉到此处逻辑有问题，需要调试的时候，可写日志。
//                String message = String.format("plus value error, record: %s, field: %s, msg: %s", record, summaryField, e.getMessage());
//                logger.error(message, e);
            }
            businessSummary.put(summaryField, value);
        }
        businessSummaryMap.put(businessKey, businessSummary);

    }


    /**
     * 保存业务对象 上下级关联关系
     *
     * @param parentChildrenMap
     * @param parentKey
     * @param child
     * @return
     */
    private static void updateBusinessRelation(Map parentChildrenMap, String parentKey, String child) {
        List childrenList = (List) parentChildrenMap.get(parentKey);
        if (childrenList == null) {
            childrenList = new ArrayList();
        }
        if (child == null) {
            child = "";
        }
        if (!childrenList.contains(child)) {
            childrenList.add(child);
            parentChildrenMap.put(parentKey, childrenList);
        }
    }

    /**
     * 初始化汇总数据
     *
     * @param summary
     * @param key
     * @return
     */
    public static Map getSummaryInitialData(Map summary, String key) {
        Map initialData = (Map) MapUtil.getObject(summary, key);
        if (initialData == null) {
            initialData = new HashMap(16);
        }
        return initialData;
    }

    public static void appendOperatorSummaryByStyle(SXSSFSheet summarySheet, Map operatorBasicInfoMap, Map operatorSummaryMap, String operatorSn, boolean isKAStatement) {
        Map operatorSummary = (Map) operatorSummaryMap.get(operatorSn);
        String[] operators = operatorSn.split("@_@");
        int length = operators.length;
        if (length > 0) {
            operatorSn = operators[length - 1];
        }
        Map<String, Object> operator = (Map<String, Object>) operatorBasicInfoMap.get(operatorSn);
        List<String> basicValues = Lists.newArrayList(
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                operatorSn,
                MapUtil.getString(operator, "operator_name"));

        StatementSummaryStyleUtil.appendByStyle(summarySheet,  operatorSummary, operator, basicValues, true, true, isKAStatement);
    }


    public static void appendTerminalSummaryByStyle(SXSSFSheet summarySheet, Map terminalBasicInfoMap, Map terminalSummaryMap, String terminalSn, boolean isKAStatement) {
        Map terminalSummary = (Map) terminalSummaryMap.get(terminalSn);
        Map<String, Object> terminal = (Map<String, Object>) terminalBasicInfoMap.get(terminalSn);
        List<String> basicValues = Lists.newArrayList(
                "",
                "",
                "",
                terminalSn,
                MapUtil.getString(terminal, "terminal_client_sn"),
                MapUtil.getString(terminal, "terminal_name"),
                MapUtil.getString(terminal, "terminal_type"),
                MapUtil.getString(terminal, "terminal_device_fingerprint"),
                "",
                "");
        StatementSummaryStyleUtil.appendByStyle(summarySheet, terminalSummary, terminal, basicValues, true, true, isKAStatement);
    }


    public static void appendStoreSummaryByStyle(SXSSFSheet summarySheet, Map storeBasicInfoMap, Map storeSummaryMap, String storeSn, boolean isKAStatement) {
        Map storeSummary = (Map) storeSummaryMap.get(storeSn);
        Map<String, Object> store = (Map<String, Object>)storeBasicInfoMap.get(storeSn);
        List<String> basicValues = Lists.newArrayList(
                storeSn,
                MapUtil.getString(store, "store_client_sn"),
                MapUtil.getString(store, "store_name"),
                "",
                "",
                "",
                "",
                "",
                "",
                "");
        StatementSummaryStyleUtil.appendByStyle(summarySheet, storeSummary, store, basicValues, true, true, isKAStatement);
    }

    public static void appendCashDeskSummaryByStyle(SXSSFSheet summarySheet, Map cashDeskBasicInfoMap, Map cashDeskSummaryMap, String cashDeskId, boolean isKAStatement) {
        Map cashDeskSummary = (Map) cashDeskSummaryMap.get(cashDeskId);
        Map<String, Object> cashDesk = (Map<String, Object>)cashDeskBasicInfoMap.get(cashDeskId);
        List<String> basicValues = Lists.newArrayList(
                "",
                BeanUtil.getPropString(cashDesk, "cash_desk_name"));
        StatementSummaryStyleUtil.appendByStyle(summarySheet, cashDeskSummary, cashDesk, basicValues, true, true, isKAStatement);
    }

    public static void appendCashDeskChangeShiftsSummaryByStyle(SXSSFSheet summarySheet, Map cashierBasicInfoMap, Map cashierSummaryMap, String batchId, boolean isKAStatement) {
        Map cashDeskSummary = (Map) cashierSummaryMap.get(batchId);
        Map<String, Object> changeShiftsBatch = (Map<String, Object>)cashierBasicInfoMap.get(batchId);
        List<String> basicValues = Lists.newArrayList(
                "",
                "",
                "",
                MapUtil.getString(changeShiftsBatch, "change_shifts_batch"),
                DateTimeUtil.format(DateTimeUtil.DATE_TIME_FORMAT, MapUtil.getLongValue(changeShiftsBatch, "change_shifts_start_date")),
                DateTimeUtil.format(DateTimeUtil.DATE_TIME_FORMAT, MapUtil.getLongValue(changeShiftsBatch, "change_shifts_end_date")),
                MapUtil.getString(changeShiftsBatch, "cashier_name"));
        StatementSummaryStyleUtil.appendByStyle(summarySheet, cashDeskSummary, changeShiftsBatch, basicValues, true, true, isKAStatement);
    }


    public static void appendCashierSummaryByStyle(SXSSFSheet summarySheet, Map cashierBasicInfoMap, Map cashierSummaryMap, String storeSn, String cashier, boolean isKAStatement) {
        Map cashDeskSummary = (Map) cashierSummaryMap.get(storeSn + cashier);
        Map<String, Object> cashierInfo = (Map<String, Object>)cashierBasicInfoMap.get(cashier);
        List<String> basicValues = Lists.newArrayList(
                "",
                MapUtil.getString(cashierInfo, "cashier_name"),
                MapUtil.getString(cashierInfo, "cashier_phone"));
        StatementSummaryStyleUtil.appendByStyle(summarySheet, cashDeskSummary, cashierInfo, basicValues, true, true, isKAStatement);
    }

    public static Map getRecordTemplate() {
        Map recordTemplate = CollectionUtil.hashMap(
                "merchantSn", "",
                "storeSn", "",
                "terminalSn", "",
                "payway", "",
                //交易笔数
                "tradeCount", 0,
                //交易金额
                "tradeAmount", 0,
                //退款笔数
                "refundCount", 0,
                //退款金额
                "refundAmount", 0,
                //商户优惠
                "merchantDiscount", 0,
                //收钱吧优惠
                "shouqianbaDiscount", 0,
                //收款通道机构优惠
                "channelDiscount", 0,
                //收款通道商户预充值优惠
                "channelMchDiscount", 0,
                //收款通道商户免充值优惠
                "channelMchTopUpDiscount", 0,
                //实收金额
                "receiveAmount", 0,
                //手续费
                "tradeFee", 0,
                //结算金额
                "incomeAmount", 0,
                // 分账金额
                "sharingAmount", 0,
                // 技术服务费
                "tradeServiceSharingAmount", 0,
                //跨主体退货笔数
                "crossMchRefundCount", 0,
                //跨主体退货金额
                "crossMchRefundAmount", 0,
                //商家记账结算金额
                "chargeIncomeAmount", 0,
                //直清结算金额
                "liquidationIncomeAmount", 0,
                //外卡交易结算金额
                "foreignCardIncomeAmount", 0,
                //成交到账金额
                "dealToAccountAmount", 0,

                //花呗分期商户贴息
                StatementSummary.HBFQ_MCH_DISCOUNT_AMOUNT, 0,

//                -------------- 以下为储值单独统计
                //交易笔数
                StatementSummary.PREFIX + "tradeCount", 0,
                //交易金额
                StatementSummary.PREFIX + "tradeAmount", 0,
                //退款笔数
                StatementSummary.PREFIX + "refundCount", 0,
                //退款金额
                StatementSummary.PREFIX + "refundAmount", 0,
                //商户优惠
                StatementSummary.PREFIX + "merchantDiscount", 0,
                //收钱吧优惠
                StatementSummary.PREFIX + "shouqianbaDiscount", 0,
                //收款通道机构优惠
                StatementSummary.PREFIX + "channelDiscount", 0,
                //收款通道商户预充值优惠
                StatementSummary.PREFIX + "channelMchDiscount", 0,
                //收款通道商户免充值优惠
                StatementSummary.PREFIX + "channelMchTopUpDiscount", 0,
                //实收金额
                StatementSummary.PREFIX + "receiveAmount", 0,
                //手续费
                StatementSummary.PREFIX + "tradeFee", 0,
                //结算金额
                StatementSummary.PREFIX + "incomeAmount", 0,
                // 分账金额
                StatementSummary.PREFIX + "sharingAmount", 0,
                // 技术服务费
                StatementSummary.PREFIX + "tradeServiceSharingAmount", 0,
                //跨主体退货笔数
                StatementSummary.PREFIX + "crossMchRefundCount", 0,
                //跨主体退货金额
                StatementSummary.PREFIX + "crossMchRefundAmount", 0,
                //花呗分期商户贴息
                StatementSummary.PREFIX + StatementSummary.HBFQ_MCH_DISCOUNT_AMOUNT, 0,
                //现金储值充值结算金额
                StatementSummary.PREFIX + "cashIncomeAmount", 0,
                //其他方式记账储值充值结算金额
                StatementSummary.PREFIX + "otherChargeIncomeAmount", 0,
                //储值充值直清交易结算金额
                StatementSummary.PREFIX + "liquidationIncomeAmount", 0,
                //储值核销实收金额
                "storePayReceiveAmount", 0,
                //外卡交易结算金额
                StatementSummary.PREFIX + "foreignCardIncomeAmount", 0,
                //储值充值到账金额
                StatementSummary.PREFIX + "dealToAccountAmount", 0
        );
        for (int payway : OrderUtil.paywayList) {
            //支付通道的结算金额
            recordTemplate.put(payway + ":incomeAmount", 0);
            recordTemplate.put(StatementSummary.PREFIX + payway + ":incomeAmount", 0);
        }

        return recordTemplate;
    }


    public static class ByLength implements Comparator<String> {
        @Override
        public int compare(String a, String b) {
            return a.length() - b.length();
        }
    }
}
