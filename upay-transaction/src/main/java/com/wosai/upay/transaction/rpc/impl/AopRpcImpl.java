package com.wosai.upay.transaction.rpc.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.transaction.model.SwipeCardReceiveRequest;
import com.wosai.upay.transaction.model.SwipeCardReceiveResponse;
import com.wosai.upay.transaction.rpc.AopRpc;
import com.wosai.upay.transaction.service.service.AopService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@AutoJsonRpcServiceImpl
public class AopRpcImpl implements AopRpc {

    @Autowired
    private AopService aopService;

    @Override
    public SwipeCardReceiveResponse getSwipeCardOriginalAmount(SwipeCardReceiveRequest request) {

        return this.aopService.getSwipeCardOriginalAmount(request);
    }
}
