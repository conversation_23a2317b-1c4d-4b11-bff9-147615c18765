package com.wosai.upay.transaction.util;


/**
 * <AUTHOR>
 */
public class AsyncLoadMsg {

    private long startTime;

    private String taskLogId;

    private String traceId;

    private long totalRows;

    private Integer type;

    public AsyncLoadMsg(String taskLogId) {
        this.startTime = System.currentTimeMillis();
        this.taskLogId = taskLogId;
    }

    public long getStartTime() {
        return startTime;
    }

    public String getTaskLogId() {
        return taskLogId;
    }

    public void setTaskLogId(String taskLogId) {
        this.taskLogId = taskLogId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public long getTotalRows() {
        return totalRows;
    }

    public void setTotalRows(long totalRows) {
        this.totalRows = totalRows;
    }

    public void resetStartTime(){
        this.startTime = System.currentTimeMillis();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
