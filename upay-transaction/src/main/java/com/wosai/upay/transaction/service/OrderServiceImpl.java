package com.wosai.upay.transaction.service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.alibaba.csp.sentinel.EntryType;
import com.alibaba.csp.sentinel.SphO;
import com.wosai.pantheon.util.CollectionUtil;
import com.wosai.upay.transaction.service.service.BizHistoryTransactionService;
import com.wosai.upay.transaction.service.service.impl.AccountBookBaseServiceImpl;
import com.wosai.upay.transaction.util.*;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections.MapUtils;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.transaction.TransactionEnhanceFields;
import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.api.alipay.BusinessV2Fields;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.DatabaseQueryConstant;
import com.wosai.upay.transaction.constant.QueryFlagConstant;
import com.wosai.upay.transaction.constant.TransactionConstant;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.StatementObjectConfig;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.repository.DataRepository;
import com.wosai.upay.transaction.service.config.HbaseDispatcherTimeConfig;
import com.wosai.upay.transaction.service.dao.OrderHBaseDao;
import com.wosai.upay.transaction.service.dao.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.query.OrderHBaseQuery;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.service.service.common.BuyerCommonService;

import lombok.extern.slf4j.Slf4j;

import static com.wosai.upay.transaction.model.Transaction.*;

/**
 * <AUTHOR>
 */

@Slf4j
@AutoJsonRpcServiceImpl
public class OrderServiceImpl implements OrderService {

    public static final Logger logger = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Autowired
    public DataRepository dataRepository;
    @Autowired
    public MerchantService merchantService;
    @Autowired
    public StoreService storeService;
    @Autowired
    public TerminalService terminalService;
    @Autowired
    public BusinessService businessService;
    @Autowired
    private BuyerCommonService buyerCommonService;
    @Autowired
    private OrderHBaseDao orderHbaseDao;
    @Autowired
    private TransactionHBaseDao transactionHbaseDao;
    @Autowired
    private HbaseDispatcherTimeConfig hbaseDispatcherTimeConfig;
    @Autowired
    private CacheService cacheService;

    @Autowired
    private  MetaCacheUtil metaCacheUtil;


    @Autowired
    private BizHistoryTransactionService bizHistoryTransactionService;

    @Autowired
    private AccountBookBaseServiceImpl accountBookBaseService;



    private final AtomicInteger DELAY_COUNT =  new AtomicInteger(0);
    private static final int MAX_DELAY_COUNT = 5;

    private static final List<String> QUERY_FLAGS = Arrays.asList(
                QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO
        );

    private static final List<String> QUERY_ORDER_DETAIL_FLAGS = Arrays.asList(
                QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_CASH_DESK_INFO,
                QueryFlagConstant.ORDER_DETAILS_ADD_CASHIER_INFO
        );

    private final static String SQB = "sqb_";

    private final List<Integer> aliPayList = Arrays.asList(
            Order.PAYWAY_ALIPAY,
            Order.PAYWAY_ALIPAY2
    );
    
    /**
     * 订单列表
     *
     * @param pageInfo
     * @param queryFilter terminal_id
     *                    terminal_sn
     *                    terminal_name
     *                    store_idTPayWayIcon
     *                    store_sn
     *                    store_name
     *                    merchant_id
     *                    merchant_sn
     *                    merchant_name
     *                    merchant_alias
     *                    seller_id
     *                    seller_path
     *                    tenant_id
     *                    provider
     *                    payway
     *                    sub_payway
     *                    status
     *                    order_sn
     *                    trade_no
     *                    device_fingerprint
     *                    min_total_amount
     *                    max_total_amount
     * @return
     */
    @Override
    public ListResult getOrderList(PageInfo pageInfo, Map<String, Object> queryFilter) {
        Long currentTime = System.currentTimeMillis();

        pageInfo = CommonUtil.setPageInfoDefaultValueIfNull(pageInfo);
        if (pageInfo.getPageSize() > DatabaseQueryConstant.MAX_PAGE_SIZE_LIMIT) {
            throw new BizException(BizException.CODE_EXCEED_MAX_PAGE_SIZE, "每页数据过多，禁止查询");
        }

        List queryFlags = QUERY_FLAGS;
        String payway = MapUtil.getString(queryFilter, "payway");
        String terminalId = MapUtil.getString(queryFilter, "terminal_id");
        List<String> terminalIds = (List) BeanUtil.getProperty(queryFilter, "terminal_ids");
        String terminalSn = MapUtil.getString(queryFilter, "terminal_sn");
        String terminalName = MapUtil.getString(queryFilter, "terminal_name");
        String clientSn = MapUtil.getString(queryFilter, "client_sn");
        String storeId = MapUtil.getString(queryFilter, "store_id");
        String storeSn = MapUtil.getString(queryFilter, "store_sn");
        String storeName = MapUtil.getString(queryFilter, "store_name");
        List<String> storeIds = (List) BeanUtil.getProperty(queryFilter, "store_ids");
        String merchantId = MapUtil.getString(queryFilter, "merchant_id");
        String merchantSn = MapUtil.getString(queryFilter, "merchant_sn");
        String merchantName = MapUtil.getString(queryFilter, "merchant_name");
        String merchantAlias = MapUtil.getString(queryFilter, "merchant_alias");
        List<String> merchantIds = (List) BeanUtil.getProperty(queryFilter, "merchant_ids");
        boolean selectCount = MapUtil.getBoolean(queryFilter, "query_result_select_count", true);
        boolean selectResult = MapUtil.getBoolean(queryFilter, "query_result_select_result", true);

        Integer subPayway = MapUtil.getInteger(queryFilter, "sub_payway");
        Integer status = MapUtil.getInteger(queryFilter, "status");
        String orderSn = BeanUtil.getPropString(queryFilter, "order_sn");
        String tradeNo = BeanUtil.getPropString(queryFilter, "trade_no");
        String deviceFingerprint = BeanUtil.getPropString(queryFilter, "device_fingerprint");
        Long minTotalAmount = CommonUtil.parseLong(BeanUtil.getPropString(queryFilter, "min_total_amount"));
        Long maxTotalAmount = CommonUtil.parseLong(BeanUtil.getPropString(queryFilter, "max_total_amount"));
        int hbaseTimeout = MapUtil.getIntValue(queryFilter, CommonConstant.HBASE_TIMEOUT);
        int solrTimeout = MapUtil.getIntValue(queryFilter, CommonConstant.SOLR_TIMEOUT);

        List<List<String>> allMerchantIds = new ArrayList<>();
        List<List<String>> allStoreIds = new ArrayList<>();
        List<List<String>> allTerminalIds = new ArrayList<>();
        if (!StringUtil.empty(terminalSn)) {
            Map<String, Object> terminal = terminalService.getTerminalBySn(terminalSn);
            String localTerminalId = BeanUtil.getPropString(terminal, DaoConstants.ID);
            String localMerchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
            if (StringUtil.empty(localTerminalId)) {
                return ListResult.emptyListResult();
            }
            allTerminalIds.add(Arrays.asList(localTerminalId));
            allMerchantIds.add(Arrays.asList(localMerchantId));
        }
        if (!StringUtil.empty(terminalId)) {
            Map<String, Object> terminal = terminalService.getTerminal(terminalId);
            if (terminal == null) {
                return ListResult.emptyListResult();
            }
            String localMerchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
            allTerminalIds.add(Arrays.asList(terminalId));
            allMerchantIds.add(Arrays.asList(localMerchantId));
        }
        if (terminalIds != null) {
            allTerminalIds.add(terminalIds);
        }
        if (!StringUtil.empty(storeSn)) {
            Map<String, Object> store = storeService.getStoreByStoreSn(storeSn);
            if (store == null) {
                return ListResult.emptyListResult();
            }
            String localStoreId = BeanUtil.getPropString(store, DaoConstants.ID);
            String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
            allStoreIds.add(Arrays.asList(localStoreId));
            allMerchantIds.add(Arrays.asList(localMerchantId));
        }
        if (!StringUtil.empty(storeId)) {
            Map<String, Object> store = storeService.getStore(storeId);
            if (store == null) {
                return ListResult.emptyListResult();
            }
            String localStoreId = BeanUtil.getPropString(store, DaoConstants.ID);
            String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
            allStoreIds.add(Arrays.asList(localStoreId));
            allMerchantIds.add(Arrays.asList(localMerchantId));
        }
        if (storeIds != null) {
            List localMerchantIds = new ArrayList();
            List localStoreIds = new ArrayList();
            for (String localstoreId : storeIds) {
                Map<String, Object> store = storeService.getStore(localstoreId);
                if (store == null) {
                    continue;
                }
                String localMerchantId = BeanUtil.getPropString(store, Store.MERCHANT_ID);
                localStoreIds.add(localstoreId);
                localMerchantIds.add(localMerchantId);
            }
            allStoreIds.add(localStoreIds);
            allMerchantIds.add(localMerchantIds);
        }
        if (!StringUtil.empty(merchantSn)) {
            Map<String, Object> merchant = merchantService.getMerchantBySn(merchantSn);
            if (merchant == null) {
                return ListResult.emptyListResult();
            }
            String localMerchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            if (localMerchantId != null) {
                allMerchantIds.add(Arrays.asList(localMerchantId));
            }
        }
        if (!StringUtil.empty(merchantId)) {
            allMerchantIds.add(Arrays.asList(merchantId));
        }
        if (!StringUtil.empty(merchantName)) {
            List<Map> merchantInfos = businessService.getMerchantInfosByMerchantName(merchantName);
            if (merchantInfos == null || merchantInfos.size() == 0) {
                return ListResult.emptyListResult();
            }
            List<String> localMerchantIds = new ArrayList<>();
            for (Map merchant : merchantInfos) {
                String localMerchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
                localMerchantIds.add(localMerchantId);
            }
            allMerchantIds.add(localMerchantIds);
        }
        if (!StringUtil.empty(merchantAlias)) {
            List<Map> merchantInfos = businessService.getMerchantInfosByMerchantAlias(merchantAlias);
            if (merchantInfos == null || merchantInfos.size() == 0) {
                return ListResult.emptyListResult();
            }
            List<String> localMerchantIds = new ArrayList<>();
            for (Map merchant : merchantInfos) {
                String localMerchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
                localMerchantIds.add(localMerchantId);
            }
            allMerchantIds.add(localMerchantIds);
        }
        if (merchantIds != null && merchantIds instanceof List) {
            allMerchantIds.add(merchantIds);
        }
        if (!StringUtil.empty(terminalName)) {
            Set<String> allSameMerchantIds = CommonUtil.getAllSameElementInLists(allMerchantIds);
            String searchStoreOrTerminalMerchantId = null;
            if (allSameMerchantIds.size() == 1) {
                // 仅merchantId过滤到一个时才这么做
                searchStoreOrTerminalMerchantId = allSameMerchantIds.iterator().next();
            }
            List<Map> terminalInfos = businessService.getTerminalInfosByTerminalName(searchStoreOrTerminalMerchantId, terminalName);
            if (terminalInfos == null || terminalInfos.size() == 0) {
                return ListResult.emptyListResult();
            }
            List<String> localTerminalIds = new ArrayList<>();
            List<String> localMerchantIds = new ArrayList<>();
            for (Map terminal : terminalInfos) {
                String localTerminalId = BeanUtil.getPropString(terminal, DaoConstants.ID);
                String localMerchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
                localTerminalIds.add(localTerminalId);
                localMerchantIds.add(localMerchantId);
            }
            allTerminalIds.add(localTerminalIds);
            allMerchantIds.add(localMerchantIds);
        }
        if (!StringUtil.empty(deviceFingerprint)) {
            Set<String> allSameMerchantIds = CommonUtil.getAllSameElementInLists(allMerchantIds);
            String searchStoreOrTerminalMerchantId = null;
            // 仅merchantId过滤到一个时才这么做
            if (allSameMerchantIds.size() == 1) {
                searchStoreOrTerminalMerchantId = allSameMerchantIds.iterator().next();
            }
            List<Map> terminalInfos = businessService.getTerminalInfosByDeviceFingerprint(searchStoreOrTerminalMerchantId, deviceFingerprint);
            if (terminalInfos == null || terminalInfos.size() == 0) {
                return ListResult.emptyListResult();
            }
            List<String> localTerminalIds = new ArrayList<>();
            List<String> localMerchantIds = new ArrayList<>();
            for (Map terminal : terminalInfos) {
                String localTerminalId = BeanUtil.getPropString(terminal, DaoConstants.ID);
                String localMerchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
                localTerminalIds.add(localTerminalId);
                localMerchantIds.add(localMerchantId);
            }
            allTerminalIds.add(localTerminalIds);
            allMerchantIds.add(localMerchantIds);
        }
        if (!StringUtil.empty(storeName)) {
            Set<String> allSameMerchantIds = CommonUtil.getAllSameElementInLists(allMerchantIds);
            String searchStoreOrTerminalMerchantId = null;
            // 仅merchantId过滤到一个时才这么做
            if (allSameMerchantIds.size() == 1) {
                searchStoreOrTerminalMerchantId = allSameMerchantIds.iterator().next();
            }
            List<Map> storeInfos = businessService.getStoreInfosByStoreName(searchStoreOrTerminalMerchantId, storeName);
            if (storeInfos == null || storeInfos.size() == 0) {
                return ListResult.emptyListResult();
            }
            List<String> localStoreIds = new ArrayList<>();
            List<String> localMerchantIds = new ArrayList<>();
            for (Map localStore : storeInfos) {
                String localStoreId = BeanUtil.getPropString(localStore, DaoConstants.ID);
                String localMerchantId = BeanUtil.getPropString(localStore, Store.MERCHANT_ID);
                localStoreIds.add(localStoreId);
                localMerchantIds.add(localMerchantId);
            }
            allStoreIds.add(localStoreIds);
            allMerchantIds.add(localMerchantIds);
        }
        OrderHBaseQuery orderHbaseQuery = new OrderHBaseQuery();
        if (allMerchantIds.size() > 0) {
            Set<String> allSameMerchantIds = CommonUtil.getAllSameElementInLists(allMerchantIds);
            if (allSameMerchantIds.size() == 0) {
                return ListResult.emptyListResult();
            }
            orderHbaseQuery.setMerchantIds(new ArrayList<>(allSameMerchantIds));
        }

        //门店id索引
        if (allStoreIds.size() > 0) {
            Set<String> allSameStoreIds = CommonUtil.getAllSameElementInLists(allStoreIds);
            if (allSameStoreIds.size() == 0) {
                return ListResult.emptyListResult();
            }
            orderHbaseQuery.setStoreIds(new ArrayList<>(allSameStoreIds));
        }

        //终端id索引
        if (allTerminalIds.size() > 0) {
            Set<String> allSameTerminalIds = CommonUtil.getAllSameElementInLists(allTerminalIds);
            if (allSameTerminalIds.size() == 0) {
                return ListResult.emptyListResult();
            }
            orderHbaseQuery.setTerminalIds(new ArrayList<>(allSameTerminalIds));
        }

        orderHbaseQuery.setClientSn(clientSn);
        if (StringUtils.hasText(payway)) {
            List<Integer> payways = Lists.newArrayList(payway.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
            orderHbaseQuery.setPayWays(payways);
        }
        if (subPayway != null) {
            orderHbaseQuery.setSubPayWays(Lists.newArrayList(subPayway));
        }
        if (BeanUtil.getProperty(queryFilter, "provider") != null) {
            if ("null".equals(BeanUtil.getProperty(queryFilter, "provider"))) {
                orderHbaseQuery.setProviderIsNull(true);
            } else {
                String provider = MapUtil.getString(queryFilter, "provider");
                if (StringUtils.hasText(provider)) {
                    List<Integer> providerList = Lists.newArrayList(provider.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
                    orderHbaseQuery.setProviders(providerList);
                }
            }
        }

        if (status != null) {
            orderHbaseQuery.setStatusList(Lists.newArrayList(status.intValue()));
        }

        orderHbaseQuery.setOrderSn(orderSn);
        orderHbaseQuery.setTradeNo(tradeNo);
        orderHbaseQuery.setMinTotalAmount(minTotalAmount);
        orderHbaseQuery.setMaxTotalAmount(maxTotalAmount);

        Long start = pageInfo.getDateStart();
        if (start == null) {
            // 默认开始时间设置为solr索引建立的开始时间
            start = hbaseDispatcherTimeConfig.getHotIndexStartTime();
        }
        orderHbaseQuery.setStartTime(start);
        Long dateEnd = pageInfo.getDateEnd();
        //若上送dateEnd 小于 当前时间减去延迟时间，那么以dateEnd为准进行查询；否则以当前时间减去延迟时间进行查询
        if (pageInfo.getDateEnd() != null) {
            dateEnd  = (pageInfo.getDateEnd() + ConfigServiceUtils.getDelayTime()) >= currentTime ? (currentTime - ConfigServiceUtils.getDelayTime()) : pageInfo.getDateEnd();
        }
        orderHbaseQuery.setEndTime(dateEnd);
        orderHbaseQuery.setSolrTimeout(solrTimeout);
        orderHbaseQuery.sethBaseTimeout(hbaseTimeout);

        List list = new ArrayList();
        long totalCount = 0;
        // 进入到退款详情页后，会重新进行一次订单明细查询，此时参数只有订单号
        if (pageInfo.getDateStart() == null 
                && pageInfo.getDateEnd() == null
                && !StringUtil.empty(orderSn)) {
            Map<String, Object> order = cacheService.getCacheMap(CommonConstant.CACHE_KEY_COLD_ORDER, orderSn);
            if (order != null) {
                start = MapUtil.getLongValue(order, DaoConstants.CTIME);
                dateEnd = DateTimeUtil.getOneDayEnd(start);
                orderHbaseQuery.setMerchantIds(Arrays.asList(MapUtil.getString(order, Order.MERCHANT_ID)));
            }
        }
        // 查询单个订单信息时，返回收银台和收银员信息
        if (orderSn != null
                && (DBSelectContext.getContext().getSelectDb() == null
                        || DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY)) {
            queryFlags = new ArrayList<>(queryFlags);
            queryFlags.add(QueryFlagConstant.ORDER_DETAILS_ADD_CASHIER_INFO);
            queryFlags.add(QueryFlagConstant.ORDER_DETAILS_ADD_CASH_DESK_INFO);
        }
        // 历史交易查询校验
        if (start < hbaseDispatcherTimeConfig.getHotIndexStartTime()) {
            if (StringUtil.empty(orderSn)) {
                throw new BizException(CommonException.CODE_ACCESS_DENIED, String.format("%s年前交易仅支持按收钱吧单号查询", hbaseDispatcherTimeConfig.getHotIndexLimitYear()));
            } else if (dateEnd == null || DateTimeUtil.getOneDayEnd(start) < dateEnd) {
                boolean throwException = true;
                // 进入到流水明细页后，会重新进行一次订单查询，此时不会带上结束时间和商户id
                if (dateEnd == null && orderHbaseQuery.getMerchantIds() == null) {
                    Map<String, Object> cacheValue = cacheService.getCacheMap(CommonConstant.CACHE_KEY_COLD_ORDER, orderSn);
                    if (cacheValue != null) {
                        orderHbaseQuery.setMerchantIds(Arrays.asList(MapUtil.getString(cacheValue, Order.MERCHANT_ID)));
                        dateEnd = start + 60 * 1000;
                        throwException = false;
                    }
                }
                if (throwException) {
                    throw new BizException(CommonException.CODE_ACCESS_DENIED, String.format("%s年前交易仅支持按天查询", hbaseDispatcherTimeConfig.getHotIndexLimitYear()));
                }
            } else if (CollectionUtils.isEmpty(orderHbaseQuery.getMerchantIds())) {
                // 进入到订单详情页后，会重新进行一次订单查询，此时不会带上商户ID
                Map<String, Object> cacheValue = cacheService.getCacheMap(CommonConstant.CACHE_KEY_COLD_ORDER, orderSn);
                if (cacheValue != null && orderHbaseQuery.getMerchantIds() == null) {
                    orderHbaseQuery.setMerchantIds(Arrays.asList(MapUtil.getString(cacheValue, Order.MERCHANT_ID)));
                } else {
                    throw new BizException(CommonException.CODE_ACCESS_DENIED, String.format("%s年前交易查询需要输入商户号或门店号或终端号", hbaseDispatcherTimeConfig.getHotIndexLimitYear()));
                }
            } else if (orderHbaseQuery.getMerchantIds().size() > 1) {
                return new ListResult(0, Collections.emptyList());
            }
            String queryMerchantId = orderHbaseQuery.getMerchantIds().get(0);
            Map<String, Object> order = orderHbaseDao.queryBySn(queryMerchantId, orderSn, start, dateEnd);
            if (order != null) {
                totalCount = 1;
                list.add(order);
                long ctime = MapUtil.getLongValue(order, DaoConstants.CTIME);
                Map<String, Object> transaction = transactionHbaseDao.queryBySn(queryMerchantId, orderSn, ctime - 60 * 1000, ctime + 60 * 1000);
                formatOrder(order, transaction == null ? Collections.emptyList() : Arrays.asList(transaction), queryFlags);
                // 支付类交易缓存1小时，退款时使用
                if (MapUtil.getInteger(transaction, Transaction.TYPE) == Transaction.TYPE_PAYMENT
                            && MapUtil.getInteger(transaction, Transaction.STATUS) == Transaction.STATUS_SUCCESS) {
                    String id = MapUtils.getString(order, DaoConstants.ID);
                    byte[] rowKey = Bytes.add(Bytes.toBytes(queryMerchantId), Bytes.toBytes(ctime), Bytes.toBytes(id));
                    cacheService.setCache(CommonConstant.CACHE_KEY_COLD_ORDER, orderSn, MapUtil.hashMap(DaoConstants.ID, Hex.encodeHexString(rowKey),
                            Order.MERCHANT_ID, queryMerchantId,
                            DaoConstants.CTIME, ctime
                        ), TimeUnit.HOURS, 1);
                }
            }
        } else {
            try {
                if (selectCount) {
                    //统计总条数
                    totalCount = orderHbaseDao.count(orderHbaseQuery);
                }
                if ((!selectCount || totalCount > 0) && selectResult) {
                    orderHbaseQuery.getOrderBys().addAll(pageInfo.getOrderBy());
                    int limit = pageInfo.getPageSize();
                    int offset = (pageInfo.getPage() - 1) * limit;
                    orderHbaseQuery.setLimit(limit);
                    orderHbaseQuery.setOffset(offset);

                    list = orderHbaseDao.queryList(orderHbaseQuery);
                    formatOrders(list, queryFlags);
                }
            }catch (Throwable t){
                logger.error("获取订单异常,", t);
                throw t;
            }
        }
        return new ListResult(totalCount, list);
    }


    @Override
    public ListResult getOrderListV2(PageInfo pageInfo, Map<String, Object> queryFilter) {
        ListResult orderList = new ListResult();
        List list = new ArrayList<>();
        boolean queryOdps = false;
        Exception ex = null;
        try {
            orderList = getOrderList(pageInfo, queryFilter);
        } catch (Exception e) {
            queryOdps = true;
            ex = e;
        }
        if (!CollectionUtils.isEmpty(orderList.getRecords())) {
            return orderList;
        }
        List queryFlags = QUERY_FLAGS;
        String orderSn = BeanUtil.getPropString(queryFilter, "order_sn");
        // 查询单个订单信息时，返回收银台和收银员信息
        if (orderSn != null
                && (DBSelectContext.getContext().getSelectDb() == null
                || DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY)) {
            queryFlags = new ArrayList<>(queryFlags);
            queryFlags.add(QueryFlagConstant.ORDER_DETAILS_ADD_CASHIER_INFO);
            queryFlags.add(QueryFlagConstant.ORDER_DETAILS_ADD_CASH_DESK_INFO);
        }
        if (!CollectionUtil.isEmpty(orderList.getRecords())) {
            return orderList;
        }
        String tradeNo = BeanUtil.getPropString(queryFilter, "provider_trade_no", "");
        String channelTradeNo = BeanUtil.getPropString(queryFilter, "channel_trade_no", "");
        List queryFlag = queryFlags;
        long totalCount = 0;
        if (queryOdps || CollectionUtil.isEmpty(orderList.getRecords())) {
            totalCount = 1;
            // 如果收款渠道订单号和支付源订单号都为空且存在返回异常则返回原有异常
            if (StringUtils.isEmpty(tradeNo) && StringUtils.isEmpty(channelTradeNo) && queryOdps) {
                throw new RuntimeException(ex.getMessage(), ex);
            } else if (StringUtils.isEmpty(tradeNo) && StringUtils.isEmpty(channelTradeNo)) {
                return new ListResult(0, Collections.emptyList());
            }

            if (!SphO.entry("query-history-order-Hbase", EntryType.IN, 1, StringUtils.isEmpty(channelTradeNo) ? tradeNo : channelTradeNo)) {
                throw new RuntimeException("请求过于频繁,请稍后重试");
            }
            try {
                String cacheKey = channelTradeNo + "_" + tradeNo;
                Map<String, Object> cacheTransaction = cacheService.getCacheMap(CommonConstant.CACHE_KEY_ORDER, cacheKey);
                if (cacheTransaction != null) {
                    list.add(cacheTransaction);
                }
                if (!CollectionUtils.isEmpty(list)) {
                    return new ListResult(totalCount, list);
                }

                List<String> dateList = new ArrayList<>();
                if (!StringUtils.isEmpty(tradeNo)) {
                    dateList.addAll(DateTimeUtil.extractDateWithContext(tradeNo));
                }
                if (!StringUtils.isEmpty(channelTradeNo)) {
                    dateList.addAll(DateTimeUtil.extractDateWithContext(channelTradeNo));
                }
                if(CollectionUtils.isEmpty(dateList)){
                    return new ListResult(0, Collections.emptyList());
                }
                List<Map<String, Object>> result = bizHistoryTransactionService.getOdpsResult(dateList, tradeNo, channelTradeNo);
                List<Map<String, Object>> transactions = new ArrayList<>();
                if (!CollectionUtils.isEmpty(result)) {
                    transactions.addAll(result);
                }
                logger.info("查询历史交易数据，结果：{}", result);
                if (!CollectionUtils.isEmpty(transactions)) {
                    Map<String, Object> transaction = queryOrderTransaction(transactions, queryFlag);
                    list.add(transaction);
                    if (!CollectionUtil.isEmpty(list)) {
                        cacheService.setCache(CommonConstant.CACHE_KEY_ORDER, cacheKey, list.get(0), TimeUnit.MINUTES, 2);
                        return new ListResult(totalCount, list);
                    }
                }
            } catch (Exception e) {
                logger.error("查询历史交易数据异常", e);
                throw new RuntimeException("查询历史交易数据异常");
            } finally {
                SphO.exit(1);
            }
        }
        if (queryOdps && CollectionUtils.isEmpty(list)) {
            throw new RuntimeException(ex.getMessage(), ex);
        }
        return new ListResult(totalCount, list);
    }


    private Map<String, Object> queryOrderTransaction(List<Map<String, Object>> transactions, List queryFlags) {
        String transactionSn;
        Map<String, Object> transaction = new HashMap<>();
        transaction = transactions.get(0);
        String queryMerchantId = MapUtil.getString(transaction, Transaction.MERCHANT_ID);
        long ctime = MapUtil.getLongValue(transaction, DaoConstants.CTIME);
        transactionSn = MapUtil.getString(transaction, Transaction.TSN);
        Map<String, Object> order = accountBookBaseService.queryOneByOid(queryMerchantId, ctime, "o" + transactionSn, false);
        if(order == null){
            throw new BizException(CommonException.CODE_ACCESS_DENIED, "查询历史订单失败");
        }
        transaction = accountBookBaseService.queryOneByTid(queryMerchantId, ctime, "t" + transactionSn);
        try {
            formatOrder(order, transaction == null ? Collections.emptyList() : Arrays.asList(transaction), queryFlags);
        } catch (Exception e) {
            logger.error("查询订单相关流水信息异常", e);
        }
        // history_flag 表示是历史查询
        order.put("history_flag", true);
        return order;
    }

    //-------------------------订单详情，封装次序由公共到具体-------------------------

    /**
     * 查询订单
     *
     * @param merchantId
     * @param orderSn
     * @return
     */
    private Map<String, Object> getOrderDetailsByMerchantIdAndOrderSnWithFlag(String merchantId, String orderSn, List queryFlags) {
        if (StringUtil.empty(orderSn)) {
            return null;
        }
        if (queryFlags == null) {
            queryFlags = new ArrayList();
        }

        OrderHBaseQuery orderHbaseQuery = new OrderHBaseQuery();
        orderHbaseQuery.setOrderSn(orderSn);

        if (!StringUtil.empty(merchantId)) {
            orderHbaseQuery.setMerchantIds(Lists.newArrayList(merchantId));
        }
        List<Map<String, Object>> orderList = orderHbaseDao.queryList(orderHbaseQuery);
        if (CollectionUtils.isEmpty(orderList)) {
            return null;
        }

        Map order = orderList.get(0);
        //对接威富通拉卡拉兴业后，订单号传递的有可能是他们的订单号
        if (order == null) {
            orderHbaseQuery.setOrderSn(null);
            orderHbaseQuery.setTradeNo(orderSn);
            orderHbaseQuery.setLimit(2);
            List<Map<String, Object>> orders = orderHbaseDao.queryList(orderHbaseQuery);
            if (orders.size() == 1) {
                order = orders.get(0);
            }
        }
        formatOrders(order, queryFlags);
        return order;
    }

    @Override
    public Map<String, Object> getOrderByMerchantIdAndOrderSn(String merchantId, String orderSn) {
        return getOrderIfDelay(merchantId, orderSn, null);
    }


    @Override
    public Map<String, Object> getOrderByMerchantIdAndClientSn(Map param) {
        String clientSn = WosaiMapUtils.getString(param, CommonConstant.CLIENT_SN);
        String merchantId = WosaiMapUtils.getString(param, Transaction.MERCHANT_ID);
        int loadBuyer = WosaiMapUtils.getIntValue(param, CommonConstant.LOAD_BUYER);
        OrderHBaseQuery orderHbaseQuery = new OrderHBaseQuery();
        orderHbaseQuery.setClientSn(clientSn);
        orderHbaseQuery.setMerchantIds(Lists.newArrayList(merchantId));
        List<Map<String, Object>> orders = orderHbaseDao.queryList(orderHbaseQuery);
        Map<String, Object> order;
        if (CollectionUtils.isEmpty(orders)) {
            return Maps.newHashMap();
        } else {
            order = orders.get(0);
        }

        Map<String, Object> map = Maps.newHashMap();
        map.put(CommonConstant.CLIENT_SN, clientSn);
        map.put(Transaction.STATUS, Order.Status.fromCode(WosaiMapUtils.getInteger(order, Order.STATUS)).name());
        map.put(CommonConstant.MERCHANT_ID, merchantId);
        if (loadBuyer > 0) {
            String buyerUid = MapUtil.getString(order, Order.BUYER_UID);
            int payWay = MapUtil.getIntValue(order, Order.PAYWAY);
            map.put(CommonConstant.BUYER_ID, buyerUid);
            Map<String, Object> buyerInfo = buyerCommonService.getBuyerInfo(buyerUid, payWay);
            if (MapUtils.isNotEmpty(buyerInfo)) {
                map.putAll(buyerInfo);
            }
        }
        return map;
    }

    @Override
    public Map<String, Object> getOrderDetailsByMerchantIdAndOrderSn(String merchantId, String orderSn) {
        return getOrderIfDelay(merchantId, orderSn, QUERY_ORDER_DETAIL_FLAGS);
    }

    @Override
    public Map getOrderDetailByOrderSn(String orderSn) {
        Map<String, Object> idCtimeMap = cacheService.getCacheMap(CommonConstant.CACHE_KEY_COLD_ORDER, orderSn);
        if (MapUtil.isNotEmpty(idCtimeMap)) {
            Pair<String, Long> idCtimePair = Pair.of(MapUtils.getString(idCtimeMap, DaoConstants.ID), MapUtils.getLong(idCtimeMap, DaoConstants.CTIME));
            List<Pair<String, Long>> idCtimeList = Collections.singletonList(idCtimePair);
            List<Map<String, Object>> orderList = orderHbaseDao.rowFilterByIds(idCtimeList, null, 3000);
            Map<String, Object> order = null;
            if (!CollectionUtils.isEmpty(orderList)) {
                order = orderList.get(0);
                order.put("trade_date", DateTimeUtil.format(DateTimeUtil.DAY_FORMAT, MapUtils.getLong(idCtimeMap, DaoConstants.CTIME)));
                long ctime = MapUtil.getLongValue(order, DaoConstants.CTIME);
                Map<String, Object> transaction = transactionHbaseDao.queryBySn(MapUtil.getString(order, Order.MERCHANT_ID), orderSn, ctime - 60 * 1000, ctime + 60 * 1000);
                formatOrder(order, transaction == null ? Collections.emptyList() : Arrays.asList(transaction), Arrays.asList(
                        QueryFlagConstant.ORDER_DETAILS_ADD_OPERATOR_INFO,
                        QueryFlagConstant.ORDER_DETAILS_ADD_TERMINAL_INFO,
                        QueryFlagConstant.ORDER_DETAILS_ADD_STORE_INFO,
                        QueryFlagConstant.ORDER_DETAILS_ADD_MERCHANT_INFO,
                        QueryFlagConstant.ORDER_DETAILS_ADD_TRANSACTION_INFO
                ));
            }
            return order;
        } else {
            return getOrderDetailsByMerchantIdAndOrderSn(null, orderSn);
        }
    }

    /**
     * 订单添加费率，是否待结算，分账金额等信息
     *
     * @param orders 订单或订单列表（order or orders）（map or list）
     */
    private Map<String, List<Map<String, Object>>> addTransactionInfo(Object orders, List<Map<String, Object>> transactions) {
        List<Map<String, Object>> orderList = new ArrayList<>();
        if (orders instanceof Map) {
            orderList.add((Map<String, Object>) orders);
        } else if (orders instanceof List) {
            orderList = (List) orders;
        }
        List<String> orderSns = new ArrayList<>();
        Set<String> merchantIds = new HashSet<>();
        long startTime = System.currentTimeMillis();
        for (Map<String, Object> order : orderList) {
            orderSns.add(BeanUtil.getPropString(order, Order.SN));
            merchantIds.add(BeanUtil.getPropString(order, Order.MERCHANT_ID));
            if (startTime > MapUtil.getLongValue(order, DaoConstants.CTIME)) {
                startTime = MapUtil.getLongValue(order, DaoConstants.CTIME);
            }
        }

        // 先用 orderSn 列表一次查询出交易列表
        Map<String, List<Map<String, Object>>> cached = null;
        if (orderSns.size() > 0 && transactions == null) {
            TransactionHBaseQuery transactionHbaseQuery = new TransactionHBaseQuery();
            transactionHbaseQuery.sethBaseTimeout(DatabaseQueryConstant.TIMEOUT_5000);
            transactionHbaseQuery.setMerchantIds(new ArrayList<>(merchantIds));
            transactionHbaseQuery.setStartTime(startTime);
            transactionHbaseQuery.setLimit(3000); // 最大值限制，防止查崩

            StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
            statusTypeSubPayWayQuery.setTypeList(TransactionTypeRelatedUtil.ORDER_QUERY_FILTER);
            transactionHbaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);
            transactions = this.queryTransactionsByOrderSnsPartition(transactionHbaseQuery, orderSns);

        }
        if (transactions != null) {
            cached = transactions.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(transaction -> MapUtils.getString(transaction, Transaction.ORDER_SN)));
        }
        if (MapUtil.isEmpty(cached)) {
            return null;
        }


        for (Map<String, Object> order : orderList) {
            String orderSn = BeanUtil.getPropString(order, Order.SN);
            // 获取该 orderSn 对应的 交易列表
            List<Map<String, Object>> transactionList;
            if (CollectionUtils.isEmpty(transactionList = cached.get(orderSn))) {
                continue;
            }
            // 从交易列表中获取支付交易
            Map<String, Object> transaction = getPaymentTransaction(transactionList);
            // 不存在收款交易就退出本次循环
            if (MapUtils.isEmpty(transaction)) {
                continue;
            }
            // 返回交易流水类型，SP后台退款时使用
            order.put(Transaction.TYPE, MapUtil.getIntValue(transaction, Transaction.TYPE));
            Object extraOutField = BeanUtil.getProperty(transaction, Transaction.EXTRA_OUT_FIELDS);
            Map extraOut = CommonUtil.getMapFromJsonObject(extraOutField);
            List payments = (List) BeanUtil.getProperty(extraOut, Transaction.PAYMENTS);
            Map overseas = (Map) BeanUtil.getProperty(extraOut, "overseas");
            //先从overseas 取，没有的话，从extraOut取。。。。。
            String actualPayCurrency = BeanUtil.getPropString(overseas, TransactionParam.CURRENCY);
            if (StringUtils.isEmpty(actualPayCurrency)) {
                actualPayCurrency = BeanUtil.getPropString(extraOut, TransactionParam.CURRENCY);
            }
            // combo_id
            CommonUtil.putToMapIfValueNotEmpty(order, COMBO_ID, BeanUtil.getPropString(extraOut, COMBO_ID));
            Map<String, Object> configSnapshot = CommonUtil.getMapFromJsonObject(BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT));
            //添加terminal_vendor_app_appid
            order.put(Transaction.VENDOR_APP_APPID, BeanUtil.getPropString(configSnapshot, "terminal_vendor_app_appid"));
            Map lakalaTradeParams = (Map) BeanUtil.getProperty(configSnapshot, Transaction.LAKALA_TRADE_PARAMS);
            order.put(Transaction.LAKALA_MERC_ID, BeanUtil.getPropString(lakalaTradeParams, Transaction.LAKALA_MERC_ID));
            order.put(Transaction.LAKALA_TERM_ID, BeanUtil.getPropString(lakalaTradeParams, Transaction.LAKALA_TERM_ID));
            Map<String, Object> tradeInfo = TransactionUtil.getTradeConfigInfoByConfigSnapshot(configSnapshot);
            order.put(TransactionParam.FEE_RATE, BeanUtil.getPropString(tradeInfo, TransactionParam.FEE_RATE));
            order.put(Transaction.CHANNEL_FINISH_TIME, transaction.get(Transaction.CHANNEL_FINISH_TIME));
            order.put(TransactionParam.CURRENCY, BeanUtil.getPropString(tradeInfo, TransactionParam.CURRENCY, "CNY"));
            order.put(TransactionParam.COMMON_SWITCH, MapUtil.getString(configSnapshot, TransactionParam.COMMON_SWITCH));
            //微信支付给支付币种默认值
            if (BeanUtil.getPropInt(order, Order.PAYWAY) == 3 && StringUtils.isEmpty(actualPayCurrency)) {
                actualPayCurrency = StatementObjectConfig.CURRENCY_CNY;
            }
            if (BeanUtil.getPropInt(order, Order.PAYWAY) == 1 && !StringUtils.isEmpty(actualPayCurrency) && actualPayCurrency.equals(StatementObjectConfig.CURRENCY_HKD)) {
                //香港本地支付
                order.put(Transaction.PAYWAY, StatementObjectConfig.ALIPAY_HK_LOCAL);
            }
            order.put("actual_pay_currency", actualPayCurrency);
            order.put(TransactionParam.LIQUIDATION_NEXT_DAY, BeanUtil.getPropBoolean(tradeInfo, TransactionParam.LIQUIDATION_NEXT_DAY));
            if (!StringUtil.empty(BeanUtil.getPropString(tradeInfo, TransactionParam.FEE_RATE_ORIGINAL))) {
                order.put(TransactionParam.FEE_RATE_ORIGINAL, BeanUtil.getPropString(tradeInfo, TransactionParam.FEE_RATE_ORIGINAL));
            }
            order.put(Order.PAYMENTS, payments);
            Object extraParamsObj = transaction.get(Transaction.EXTRA_PARAMS);
            Map extraParams = CommonUtil.getMapFromJsonObject(extraParamsObj);
            // sqb_wallet_name
            CommonUtil.putToMapIfValueNotEmpty(order,SQB_WALLET_NAME, MapUtils.getString(extraParams, SQB_WALLET_NAME));
            // sqb_pay_source
            CommonUtil.putToMapIfValueNotEmpty(order,SQB_PAY_SOURCE, MapUtils.getString(extraParams, SQB_PAY_SOURCE));

            Map<String, Object> enhanceExtraParams = TransactionUtils.enhanceExtraParams(extraParams);
            Integer sharingFlag = BeanUtil.getPropInt(enhanceExtraParams, TransactionEnhanceFields.SHARING_FLAG.getField(), 0);
            Double ratioTotal = MapUtils.getDouble(enhanceExtraParams, TransactionEnhanceFields.RATIO_TOTAL.getField(), 0.0);
            //分账标识
            order.put(TransactionEnhanceFields.SHARING_FLAG.getField(), sharingFlag);
            //分账比例
            order.put(TransactionEnhanceFields.RATIO_TOTAL.getField(), ratioTotal);
            // 产品标识
            String productFlag = MapUtils.getString(transaction, PRODUCT_FLAG, "");
            order.put(Transaction.PRODUCT_FLAG, productFlag);
            Object extendedParamsObj = transaction.get(Transaction.EXTENDED_PARAMS);
            Map extendedParams = CommonUtil.getMapFromJsonObject(extendedParamsObj);
            if ( Order.PAYWAY_WEIXIN == BeanUtil.getPropInt(order, Order.PAYWAY)) {
                //订单详情返回weixin_sub_appid
                if (MapUtils.isNotEmpty(extendedParams) && extendedParams.containsKey(Transaction.SUB_APPID)) {
                    //先从extended_params中获取上送的sub_appid
                    order.put(TransactionParam.WEIXIN_SUB_APP_ID, MapUtils.getString(extendedParams, Transaction.SUB_APPID));
                } else {
                    //再从config_snapshot中获取
                    int subPayWay = BeanUtil.getPropInt(order, Order.SUB_PAYWAY);
                    String subAppid = MapUtils.getString(tradeInfo, TransactionParam.WEIXIN_SUB_APP_ID);
                    if (Order.SUB_PAYWAY_MINI == subPayWay && !StringUtils.isEmpty(MapUtils.getString(tradeInfo, TransactionParam.WEIXIN_MINI_SUB_APP_ID))) {
                        subAppid = MapUtils.getString(tradeInfo, TransactionParam.WEIXIN_MINI_SUB_APP_ID);
                    }
                    order.put(TransactionParam.WEIXIN_SUB_APP_ID, subAppid);
                }
            }
            Object extendParamsObj = null;
            if (MapUtils.isNotEmpty(extendedParams)) {
                extendParamsObj = extendedParams.get(BusinessV2Fields.EXTEND_PARAMS);
                if (Order.PAYWAY_JDWALLET == BeanUtil.getPropInt(order, Order.PAYWAY)) {
                    if (extendParamsObj != null && extendParamsObj instanceof Map) {
                        Map extendParams = (Map) extendParamsObj;
                        order.put(TransactionParam.FQ_NUM, MapUtil.getIntValue(extendParams, TransactionParam.BAITIAO_FQ_NUM));
                    }
                }
            }
            if (MapUtils.isNotEmpty(extraOut)) {
                if (Order.PAYWAY_WEIXIN == BeanUtil.getPropInt(order, Order.PAYWAY)) {
                    Map installInfo = MapUtils.getMap(extraOut, WX_INSTALLMENT_INFO);
                    if (MapUtils.isNotEmpty(installInfo)) {
                        order.put(TransactionParam.FQ_NUM, MapUtil.getIntValue(installInfo, WX_INSTALLMENT_INFO_NUM));
                        order.put(Transaction.FQ_TYPE, Transaction.FQ_TYPE_SELLER);
                        order.put(TransactionParam.FQ_SELLER_PERCENT, 100);
                    }
                }
            }
            try {
                if (extraOut == null) {
                    continue;
                }
                order.put(Transaction.PAY_TYPE, TransactionUtil.getUnionPayType(payments));
                order.put(Transaction.EXTRA_CHANNEL_TRADE_NO, TransactionUtil.getChannelTradeNo(transaction));
                order.put(Transaction.BATCH_BILL_NO, BeanUtil.getPropString(extraOut, Transaction.BATCH_BILL_NO));
                order.put(Transaction.SYS_TRACE_NO, BeanUtil.getPropString(extraOut, Transaction.SYS_TRACE_NO));
                order.put(Transaction.BANK_TYPE, BeanUtil.getPropString(extraOut, Transaction.BANK_TYPE));
                if (BeanUtil.getPropBoolean(extraOut, TransactionParam.HB_FQ, false)) {
                    if (extendedParams == null || extraParams == null) {
                        continue;
                    }
                    if (extendParamsObj == null) {
                        continue;
                    }
                    if (extendParamsObj instanceof Map) {
                        Map extendParams = (Map) extendParamsObj;
                        int fqNum = MapUtil.getIntValue(extendParams, TransactionParam.FQ_NUM, 0);
                        boolean useHbfq = productFlag.contains(HbfqTransactionUtils.HBFQ_PRODUCT_FLAG);
                        int sellerPercent = MapUtil.getIntValue(extendParams, TransactionParam.FQ_SELLER_PERCENT, 0);
                        long fqAmount = MapUtil.getLongValue(extraParams, HbfqTransactionUtils.FQ_AMOUNT, -1);
                        order.put(HbfqTransactionUtils.COMBINATION_PAY, MapUtil.getBooleanValue(extraParams, HbfqTransactionUtils.COMBINATION_PAY, false));
                        if (fqAmount != -1) {
                            order.put(HbfqTransactionUtils.FQ_AMOUNT, fqAmount);
                        }
                        //以前的数据没有product_flag的概念,只有hb_fq_num字段，这里useHbfq为true一定是花呗分期，
                        //useHbfq如果为false不一定就是不是花呗分期，如果fq_num是空，hb_fq_num不是空 (信用卡分期一定会上送fq_num,而老版本的没有fq_num字段)
                        if (useHbfq || fqNum == 0) {
                            //兼容老的参数
                            if (fqNum == 0) {
                                fqNum = MapUtil.getIntValue(extendParams, TransactionParam.HB_FQ_NUM, 0);
                            }
                            order.put(TransactionParam.HB_FQ_NUM, fqNum);
                            sellerPercent = MapUtil.getIntValue(extendParams, TransactionParam.HB_FQ_SELLER_PERCENT, 0);
                            order.put(TransactionParam.HB_FQ_SELLER_PERCENT, sellerPercent);
                            order.put(TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE, extraParams.get(TransactionParam.SQB_HB_FQ_SELLER_SERVICE_CHARGE));
                        }
                        order.put(TransactionParam.FQ_NUM, fqNum);
                        order.put(TransactionParam.FQ_SELLER_PERCENT, sellerPercent);
                        order.put(TransactionConstant.APPEND_SQB_FQ_SELLER_SERVICE_CHARGE, extraParams.get(TransactionConstant.APPEND_SQB_FQ_SELLER_SERVICE_CHARGE));
                    }

                }
            } catch (Exception ex) {
                logger.info("ex:", ex);
            }

        }
        return cached;
    }

    private void formatOrder(Map order, List<Map<String, Object>> transactions, List queryFlags) {
        addTransactionInfo(order, transactions);
        businessService.addBusinessInfoWithQueryFlag(order, queryFlags);
        OrderUtil.transformReflect(order);
        OrderUtil.jsonFormatOrder(order);
        OrderUtil.expandOrderItemsPayments(order);
        OrderUtil.caculateExtendFields(order, transactions);
        TransactionUtils.expandMchDiscountOriginType(order);
        OrderUtil.expandSharingBooks(order, transactions, businessService);

    }

    /**
     * 订单中添加tradeApp等元信息
     *
     * @param order
     * @return
     */
    public Map<String, Object> expandOrderMetaInfo(Map<String, Object> order) {

        String tradeApp = MapUtils.getString(order, Order.TRADE_APP);
        String bizModel = MapUtils.getString(order, Order.SQB_BIZ_MODEL);
        Integer payway = MapUtils.getInteger(order, Transaction.PAYWAY);
        if (payway != null && aliPayList.contains(payway)) {
            payway = Order.PAYWAY_ALIPAY2;
        }
        String paySource = MapUtils.getString(order, Order.SQB_PAY_SOURCE);
        // 如果是用户打开源是收钱吧 payway设置为0
        if (paySource != null && paySource.startsWith(SQB)) {
            payway = 0;
        }
        String payPath = MapUtils.getString(order, Order.SQB_PAY_PATH);
        order.put(Order.TRADE_APP_NAME, metaCacheUtil.getTradeAppName(tradeApp));
        order.put(Order.SQB_BIZ_MODEL_NAME, metaCacheUtil.getMetaBizModelName(bizModel));
        order.put(Order.SQB_PAY_SOURCE_NAME, metaCacheUtil.getMetaPaySourceName(paySource, payway));
        order.put(Order.SQB_PAY_PATH_NAME, metaCacheUtil.getMetaPayPathName(payPath));
        return order;

    }

    private void formatOrders(Object orders, List queryFlags) {
        List<Map<String, Object>> list = new LinkedList();
        if (orders instanceof List) {
            list = (List) orders;
        } else if (orders instanceof Map) {
            list.add((Map<String, Object>) orders);
        } else {
            return;
        }
        Map<String, List<Map<String, Object>>> orderTransactions = addTransactionInfo(list, null);
        businessService.addBusinessInfoWithQueryFlag(list, queryFlags);
        for (Map<String, Object> order : list) {
            String sn = BeanUtil.getPropString(order, Order.SN);
            OrderUtil.transformReflect(order);
            OrderUtil.jsonFormatOrder(order);
            OrderUtil.expandOrderItemsPayments(order);
            OrderUtil.caculateExtendFields(order, orderTransactions == null ? null : orderTransactions.get(sn));
            TransactionUtils.expandMchDiscountOriginType(order);
            OrderUtil.expandSharingBooks(order, orderTransactions == null ? null : orderTransactions.get(sn), businessService);
            expandOrderMetaInfo(order);
        }
    }

    /**
     * 从交易列表中获取支付交易（遇到第一个就返回第一个）（支付交易包含类型 13 预授权完成和类型 30 支付）
     */
    private Map<String, Object> getPaymentTransaction(List<Map<String, Object>> transactionList) {
        if (transactionList == null) {
            return null;
        }

        for (Map<String, Object> transaction : transactionList) {
            if (TransactionTypeRelatedUtil.isPayment(MapUtils.getIntValue(transaction, Transaction.TYPE))) {
                return transaction;
            }
        }

        return null;
    }

    /**
     * 查询交易流水列表（如果 orderSns 超过 Solr 的 maxBooleanClauses 值会报异常）
     */
    private List<Map<String, Object>> queryTransactionsByOrderSnsPartition(TransactionHBaseQuery hBaseQueryWithoutOrderSns,
                                                                           List<String> orderSns) {
        hBaseQueryWithoutOrderSns.setOrderSns(null); // init

        if (!CollectionUtils.isEmpty(orderSns)) {
            List<Map<String, Object>> transactions = new LinkedList<>();

            Lists.partition(orderSns, 1000).forEach(partition -> {
                hBaseQueryWithoutOrderSns.setOrderSns(partition);
                transactions.addAll(transactionHbaseDao.queryList(hBaseQueryWithoutOrderSns));
            });

            return transactions;

        } else {
            return transactionHbaseDao.queryList(hBaseQueryWithoutOrderSns);
        }
    }

    /**
     * 获取订单详情，如果出现空指针，那么就延迟指定时间后再查
     * @param merchantId
     * @param orderSn
     * @return
     */
    private Map<String, Object> getOrderIfDelay(String merchantId, String orderSn, List queryFlags){
        Map<String, Object> result = null;
        try {
            result = getOrderDetailsByMerchantIdAndOrderSnWithFlag(merchantId, orderSn, queryFlags);
        }catch (NullPointerException e){
            if (DELAY_COUNT.incrementAndGet() <= MAX_DELAY_COUNT) {
                log.warn("延迟指定时间再查询, 订单号为{}", orderSn);
                try {
                    Thread.sleep(ConfigServiceUtils.getDelayQueryTime());
                } catch (InterruptedException ex) {
                }
                try {
                    result = getOrderDetailsByMerchantIdAndOrderSnWithFlag(merchantId, orderSn, queryFlags);
                }catch (NullPointerException exception){
                    logger.error("查询失败, 订单号为{}", orderSn);
                    throw exception;
                }finally {
                    DELAY_COUNT.decrementAndGet();
                }
            } else {
                throw e;
            }
        }

        return result;
    }
}
