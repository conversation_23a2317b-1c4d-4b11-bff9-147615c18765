package com.wosai.upay.transaction.repository;

import com.wosai.data.dao.Dao;
import org.springframework.jdbc.core.JdbcTemplate;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DataRepository {

    private Dao<Map<String, Object>> statementConfigDao;

    private JdbcTemplate statementJdbcTemplate;

    public Dao<Map<String, Object>> getStatementConfigDao() {
        return statementConfigDao;
    }

    public void setStatementConfigDao(Dao<Map<String, Object>> statementConfigDao) {
        this.statementConfigDao = statementConfigDao;
    }

    public JdbcTemplate getStatementJdbcTemplate() {
        return statementJdbcTemplate;
    }

    public void setStatementJdbcTemplate(JdbcTemplate statementJdbcTemplate) {
        this.statementJdbcTemplate = statementJdbcTemplate;
    }

}
