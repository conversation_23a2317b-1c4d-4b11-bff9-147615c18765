package com.wosai.upay.transaction.service.model.vo;

import com.wosai.upay.transaction.model.TAccountSumV;


/**
 * <AUTHOR>
 */
public class TransactionSumV {

    /**
     * 支付源
     */
    private Integer payWay;

    /**
     * 交易笔数
     */
    protected int salesCount = 0;

    /**
     * 交易金额
     */
    protected long salesAmount = 0L;

    /**
     * 收款成功数量
     */
    protected int paidCount = 0;

    /**
     * 收款成功金额
     */
    protected long paidAmount = 0L;

    /**
     * 退款数量
     */
    protected Integer refundedCount = 0;

    /**
     * 退款金额
     */
    protected long refundedAmount = 0L;

    /**
     * 撤销数量
     */
    protected int canceledCount = 0;

    /**
     * 撤销金额
     */
    protected long canceledAmount = 0L;


    /**
     * 预授权完成数量
     */
    protected Integer depositCount = 0;

    /**
     * 预授权完成金额
     */
    protected Long depositAmount = 0L;


    /**
     * 预授权完成撤销金额
     */
    protected Long depositCanceledAmount = 0L;

    /**
     * 预授权完成撤销笔数
     */
    protected Integer depositCanceledCount = 0;


    /**
     * 储值交易笔数（储值充值，储值退款）
     */
    protected Long storeInTotalCount = 0L;

    /**
     * 储值交易金额（储值充值，储值退款）
     */
    protected Long storeInTotalAmount = 0L;

    /**
     * 储值充值笔数
     */
    protected Long storeInCount = 0L;

    /**
     * 储值充值金额
     */
    protected Long storeInAmount = 0L;

    /**
     * 储值退款笔数
     */
    protected Long storeInRefundCount = 0L;

    /**
     * 储值退款金额
     */
    protected Long storeInRefundAmount = 0L;

    /**
     * 预授权数量
     */
    protected Integer depositFreezeCount = 0;

    /**
     * 预授权金额
     */
    protected Long depositFreezeAmount = 0L;

    /**
     * 预授权撤销金额
     */
    protected Long depositFreezeCanceledAmount = 0L;

    /**
     * 预授权撤销笔数
     */
    protected Integer depositFreezeCanceledCount = 0;

    public Long getStoreInTotalCount() {
        return storeInTotalCount;
    }

    public void setStoreInTotalCount(Long storeInTotalCount) {
        this.storeInTotalCount = storeInTotalCount;
    }

    public Long getStoreInCount() {
        return storeInCount;
    }

    public void setStoreInCount(Long storeInCount) {
        this.storeInCount = storeInCount;
    }

    public Long getStoreInRefundCount() {
        return storeInRefundCount;
    }

    public void setStoreInRefundCount(Long storeInRefundCount) {
        this.storeInRefundCount = storeInRefundCount;
    }

    public Long getStoreInTotalAmount() {
        return storeInTotalAmount;
    }

    public void setStoreInTotalAmount(Long storeInTotalAmount) {
        this.storeInTotalAmount = storeInTotalAmount;
    }

    public Long getStoreInAmount() {
        return storeInAmount;
    }

    public void setStoreInAmount(Long storeInAmount) {
        this.storeInAmount = storeInAmount;
    }

    public Long getStoreInRefundAmount() {
        return storeInRefundAmount;
    }

    public void setStoreInRefundAmount(Long storeInRefundAmount) {
        this.storeInRefundAmount = storeInRefundAmount;
    }


    public int getSalesCount() {
        return salesCount;
    }

    public void setSalesCount(Integer salesCount) {
        this.salesCount = salesCount;
    }

    public Long getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(Long salesAmount) {
        this.salesAmount = salesAmount;
    }

    public TransactionSumV() {
    }


    public TransactionSumV(Integer salesCount, Long salesAmount,Long storeInTotalCount, Long storeInTotalAmount) {
        this.salesCount = salesCount;
        this.salesAmount = salesAmount;
        this.storeInTotalCount = storeInTotalCount;
        this.storeInTotalAmount = storeInTotalAmount;
    }


    public Integer getPaidCount() {
        return paidCount;
    }

    public void setPaidCount(Integer paidCount) {
        this.paidCount = paidCount;
    }

    public Long getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Long paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Integer getRefundedCount() {
        return refundedCount;
    }

    public void setRefundedCount(Integer refundedCount) {
        this.refundedCount = refundedCount;
    }

    public Long getRefundedAmount() {
        return refundedAmount;
    }

    public void setRefundedAmount(Long refundedAmount) {
        this.refundedAmount = refundedAmount;
    }


    public Integer getPayWay() {
        return payWay;
    }

    public void setPayWay(Integer payWay) {
        this.payWay = payWay;
    }

    public Integer getDepositCount() {
        return depositCount;
    }

    public void setDepositCount(Integer depositCount) {
        this.depositCount = depositCount;
    }

    public Long getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(Long depositAmount) {
        this.depositAmount = depositAmount;
    }

    public Long getDepositCanceledAmount() {
        return depositCanceledAmount;
    }

    public void setDepositCanceledAmount(Long depositCanceledAmount) {
        this.depositCanceledAmount = depositCanceledAmount;
    }

    public Integer getDepositCanceledCount() {
        return depositCanceledCount;
    }

    public void setDepositCanceledCount(Integer depositCanceledCount) {
        this.depositCanceledCount = depositCanceledCount;
    }

    public int getCanceledCount() {
        return canceledCount;
    }

    public void setCanceledCount(int canceledCount) {
        this.canceledCount = canceledCount;
    }

    public long getCanceledAmount() {
        return canceledAmount;
    }

    public void setCanceledAmount(long canceledAmount) {
        this.canceledAmount = canceledAmount;
    }

    public Integer getDepositFreezeCount() {
        return depositFreezeCount;
    }

    public void setDepositFreezeCount(Integer depositFreezeCount) {
        this.depositFreezeCount = depositFreezeCount;
    }

    public Long getDepositFreezeAmount() {
        return depositFreezeAmount;
    }

    public void setDepositFreezeAmount(Long depositFreezeAmount) {
        this.depositFreezeAmount = depositFreezeAmount;
    }

    public Long getDepositFreezeCanceledAmount() {
        return depositFreezeCanceledAmount;
    }

    public void setDepositFreezeCanceledAmount(Long depositFreezeCanceledAmount) {
        this.depositFreezeCanceledAmount = depositFreezeCanceledAmount;
    }

    public Integer getDepositFreezeCanceledCount() {
        return depositFreezeCanceledCount;
    }

    public void setDepositFreezeCanceledCount(Integer depositFreezeCanceledCount) {
        this.depositFreezeCanceledCount = depositFreezeCanceledCount;
    }

    public TAccountSumV buildTAccountSumV() {
        TAccountSumV sumV = new TAccountSumV();
        sumV.setCanceldAmount(canceledAmount);
        sumV.setCanceldCount(canceledCount);
        sumV.setPaidAmount(paidAmount);
        sumV.setPaidCount(paidCount);
        sumV.setRefundedAmount(refundedAmount);
        sumV.setRefundedCount(refundedCount);
        sumV.setSalesAmount(salesAmount);
        sumV.setSalesCount(salesCount);
        sumV.setPayWay(payWay);
        sumV.setDepositCount(depositCount);
        sumV.setDepositAmount(depositAmount);
        sumV.setDepositCancelAmount(depositCanceledAmount);
        sumV.setDepositCancelCount(depositCanceledCount);
        sumV.setStoreInTotalAmount(storeInTotalAmount);
        sumV.setStoreInTotalCount(storeInTotalCount);
        sumV.setStoreInAmount(storeInAmount);
        sumV.setStoreInCount(storeInCount);
        sumV.setStoreInRefundAmount(storeInRefundAmount);
        sumV.setStoreInRefundCount(storeInRefundCount);
        sumV.setDepositFreezeAmount(depositFreezeAmount);
        sumV.setDepositFreezeCount(depositFreezeCount);
        sumV.setDepositFreezeCanceledAmount(depositFreezeCanceledAmount);
        sumV.setDepositFreezeCanceledCount(depositFreezeCanceledCount);
        return sumV;
    }

}
