package com.wosai.upay.transaction.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.upay.transaction.constant.CommonConstant;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.wosai.upay.transaction.constant.CommonConstant.APP_ID_TERMINAL_TYPE_VALUE;

/**
 * <AUTHOR>
 */
public class ConfigServiceUtils {

    public static final ConcurrentHashMap<String, String> APP_ID_TERMINAL_TYPE_MAP = new ConcurrentHashMap<>();

    public static Map<String, String> VENDOR_APP_CATEGORY_MAP = new HashMap<>();

    private static volatile String recordDetailUrl;

    private static volatile String swipeRecordDetailUrl;

    private static volatile HashMap<String, String> productFlagMapping;

    public static Set<String> chargeNameSet = new HashSet<>();

    static {
        String appIdTerminalType = ConfigService.getAppConfig().getProperty(CommonConstant.APP_ID_TERMINAL_TYPE, APP_ID_TERMINAL_TYPE_VALUE);

        recordDetailUrl = ConfigService.getAppConfig().getProperty(CommonConstant.RECORD_DETAIL_URL, "");

        swipeRecordDetailUrl = ConfigService.getAppConfig().getProperty(CommonConstant.SWIPE_RECORD_DETAIL_URL, "");

        productFlagMapping = JSON.parseObject(ConfigService.getAppConfig().getProperty(CommonConstant.PRODUCT_FLAG_MAPPING, "{}"),
                new TypeReference<HashMap<String, String>>() {{
                }});

        loadChargeSet(ConfigService.getAppConfig().getProperty(CommonConstant.DISPLAY_RATIO_CHARGE_NAME_KEY, ""));

        APP_ID_TERMINAL_TYPE_MAP.putAll(JsonUtil.jsonStrToObject(appIdTerminalType, Map.class));

        VENDOR_APP_CATEGORY_MAP = JSON.parseObject(ConfigService.getAppConfig().getProperty(CommonConstant.VENDOR_APP_CATEGORY_MAP, "{}"),
                new TypeReference<HashMap<String, String>>() {{
                }});

        //监听配置变化事件
        ConfigService.getAppConfig().addChangeListener(new ConfigChangeListener() {
            @Override
            public void onChange(ConfigChangeEvent changeEvent) {
                for (String key : changeEvent.changedKeys()) {
                    String newValue = changeEvent.getChange(key).getNewValue();
                    if (CommonConstant.APP_ID_TERMINAL_TYPE.equals(key)) {
                        if (StringUtils.isNotEmpty(newValue)) {
                            APP_ID_TERMINAL_TYPE_MAP.putAll(JsonUtil.jsonStrToObject(newValue, Map.class));
                        }

                    } else if (CommonConstant.RECORD_DETAIL_URL.equals(key)) {
                        if (StringUtils.isNotEmpty(newValue)) {
                            recordDetailUrl = newValue;
                        }
                    } else if (CommonConstant.SWIPE_RECORD_DETAIL_URL.equals(key)) {
                        if (StringUtils.isNotEmpty(newValue)) {
                            swipeRecordDetailUrl = newValue;
                        }
                    } else if (CommonConstant.PRODUCT_FLAG_MAPPING.equals(key)) {
                        if (StringUtils.isNotEmpty(newValue)) {
                            productFlagMapping = JSON.parseObject(newValue, new TypeReference<HashMap<String, String>>() {{
                            }});
                        } else {
                            productFlagMapping.clear();
                        }
                    }else if(CommonConstant.DISPLAY_RATIO_CHARGE_NAME_KEY.equals(key)){
                        loadChargeSet(newValue);
                    }else if (CommonConstant.VENDOR_APP_CATEGORY_MAP.equals(key)){
                        VENDOR_APP_CATEGORY_MAP = JSON.parseObject(ConfigService.getAppConfig().getProperty(CommonConstant.VENDOR_APP_CATEGORY_MAP, "{}"),
                                new TypeReference<HashMap<String, String>>() {{
                                }});
                    }
                }
            }
        });
    }


    public static long getBigTaskThresholdSize() {
        return ConfigService.getAppConfig().getLongProperty(CommonConstant.BIG_TASK_THRESHOLD_SIZE, CommonConstant.BIG_TASK_THRESHOLD_DEFAULT_SIZE);
    }


    public static long getTaskWarnTimeoutSeconds() {
        return ConfigService.getAppConfig().getLongProperty(CommonConstant.TASK_WARN_TIMEOUT_SECONDS_STR, CommonConstant.TASK_WARN_TIMEOUT_SECONDS);
    }

    public static String getRecordDetailUrl() {
        return recordDetailUrl;
    }

    public static String getSwipeRecordDetailUrl() {
        return swipeRecordDetailUrl;
    }

    @NotNull
    public static HashMap<String, String> getProductFlagMapping() {
        return productFlagMapping;
    }

    /**
     * getOrderList延迟查询时间 毫秒级
     * @return
     */
    public static long getDelayTime() {
        return ConfigService.getAppConfig().getLongProperty(CommonConstant.DELAY_TIME, CommonConstant.DELAY_TIME_DEFAULT_VALUE);
    }

    /**
     * 订单获取不到流水时需要延迟再获取 毫秒级
     * @return
     */
    public static long getDelayQueryTime() {
        return ConfigService.getAppConfig().getLongProperty(CommonConstant.DELAY_QUERY_TIME, CommonConstant.DELAY_TIME_DEFAULT_VALUE);
    }

    private static void loadChargeSet(String newValue){
        if (StringUtils.isNotEmpty(newValue)) {
            chargeNameSet = new HashSet<>(Arrays.asList(newValue.split(",")));
        }
    }

    public static String getVendorAppCategoryDesc(Integer category){
        return VENDOR_APP_CATEGORY_MAP.get(category + "");
    }
}
