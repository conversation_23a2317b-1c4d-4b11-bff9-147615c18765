package com.wosai.upay.transaction.service.service.client;

import java.util.Map;
import java.util.concurrent.Future;

import com.wosai.middleware.carrier.CarrierItem;

/**
 * <AUTHOR>
 */
public interface IAsyncStoreService{

    /**
     * 异步获取门店信息
     * @param storeSn
     * @return
     */
    Future<Map> getStoreByStoreSn(String storeSn, CarrierItem carrierItem);


    /**
     * 异步获取门店信息
     * @param storeId
     * @return
     */
    Future<Map> getStore(String storeId, CarrierItem carrierItem);
}
