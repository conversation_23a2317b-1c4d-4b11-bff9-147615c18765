package com.wosai.upay.transaction.controller;

import com.wosai.upay.transaction.model.SensitiveProperties;
import com.wosai.upay.transaction.util.SmtpMailSender;
import com.wosai.upay.transaction.util.SolrHBaseUtils;
import com.wosai.upay.transaction.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.mail.MessagingException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Slf4j
@Controller
@RequestMapping("")
public class ApolloConfigValidationController {

    @Autowired
    SmtpMailSender smtpMailSender;
    private static SensitiveProperties sensitiveProperties = SpringUtil.getBean(SensitiveProperties.class);

    @RequestMapping("/testApolloConfig")
    @ResponseBody
    public String testApolloConfig(@RequestParam("key") String key) throws MessagingException, UnsupportedEncodingException {
        if (!key.equals("keycnadncpanvvprgnqpifbpiqqnfnqifjnpqiwunfp")) {
            return "密钥错误";
        }

        // 测试发送邮件，本地测试失败，可能是ip白名单或已弃用
        SmtpMailSender.Mail mail = new SmtpMailSender.Mail()
                .setAccountName(sensitiveProperties.getMailUsername())
                .setAccountAlias("smc")
                .setSubject("Test transaction")
                .setHtmlBody("测试")
                .setToAddresses("<EMAIL>");
        smtpMailSender.send(mail);

        // 测试SolrHBaseUtils，测试成功
        SolrHBaseUtils.getConnection(30000);

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(sensitiveProperties.toString().getBytes(StandardCharsets.UTF_8));
            return new BigInteger(1, md.digest()).toString(16);
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5加密失败：" + e.getMessage(), e);
            return "MD5加密错误";
        }
    }
}