package com.wosai.upay.transaction.service.api;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.app.backend.api.consts.BasicConsts;
import com.wosai.app.dto.V2.UcMerchantUserInfo;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.dto.department.TreeDepartmentInfo;
import com.wosai.app.service.DepartmentService;
import com.wosai.app.service.GroupService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.profit.sharing.model.Receiver;
import com.wosai.profit.sharing.model.ReceiverType;
import com.wosai.profit.sharing.model.upay.ProfitSharing;
import com.wosai.profit.sharing.util.UpayProfitSharingUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.core.model.CashDeskDevice;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.CashDeskService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.transaction.annotation.DBSelectParam;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.TransactionConstant;
import com.wosai.upay.transaction.enums.*;
import com.wosai.upay.transaction.exception.BizException;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.*;
import com.wosai.upay.transaction.model.param.AccountRecordParam;
import com.wosai.upay.transaction.model.param.GetTransactionDetailByIdParam;
import com.wosai.upay.transaction.model.param.TransactionDetailParam;
import com.wosai.upay.transaction.service.BusinessService;
import com.wosai.upay.transaction.service.IAccountBookService;
import com.wosai.upay.transaction.service.OrderService;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionDaySumV;
import com.wosai.upay.transaction.service.model.vo.TransactionSumV;
import com.wosai.upay.transaction.service.model.vo.TransactionVo;
import com.wosai.upay.transaction.service.service.IAccountBookBaseService;
import com.wosai.upay.transaction.service.service.common.BuyerCommonService;
import com.wosai.upay.transaction.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.util.MD5Hash;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@AutoJsonRpcServiceImpl
public class AccountBookServiceImpl implements IAccountBookService {

    public static final Logger logger = LoggerFactory.getLogger(AccountBookServiceImpl.class);

    @Autowired
    private IAccountBookBaseService accountBookBaseService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private BuyerCommonService buyerCommonService;

    @Autowired
    @Qualifier("merchantUserDepartmentService")
    public DepartmentService departmentService;

    @Autowired
    @Qualifier("merchantUserGroupService")
    private GroupService groupService;

    @Autowired
    private BusinessService businessService;

    @Resource
    private CashDeskService cashDeskService;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;

    @Resource
    private OrderService orderService;

    public final static String KEY_GET_MORE_ACCOUNT_BOOK_RECORDS_CACHE = "getMoreAccountBookRecords";

    private static final Set<LoadType> RECORD_FOR_DETAIL_LOADTYPES = Sets.newHashSet(LoadType.STORE_STAFF, LoadType.ITEMS, LoadType.EXTRA_OUT_FIELDS, LoadType.TERMINAL_CODE, LoadType.CONFIG_SNAPSHOT, LoadType.PROVIDER_ERROR_INFO, LoadType.REFLECT, LoadType.STORE_NAME, LoadType.CASH_DESK_NAME, LoadType.CASHIER_NAME);
    private static final Set<LoadType> GET_ACCOUNT_BOOK_RECORDS_LOADTYPES = Sets.newHashSet(LoadType.STORE_STAFF, LoadType.CONFIG_SNAPSHOT, LoadType.TERMINAL_CODE, LoadType.ITEMS, LoadType.EXTRA_OUT_FIELDS, LoadType.STORE_NAME);
    private static final Set<LoadType> GET_MORE_ACCOUNT_BOOK_RECORDS_LOADTYPES = Sets.newHashSet(LoadType.STORE_STAFF, LoadType.CONFIG_SNAPSHOT, LoadType.TERMINAL_CODE, LoadType.ITEMS, LoadType.EXTRA_OUT_FIELDS, LoadType.STORE_NAME, LoadType.REFLECT);

    @Autowired
    private StringRedisTemplate redisTemplate;

    private final static ThreadLocal<Map<String, Object>> DETAIL_URL_PARAM = ThreadLocal.withInitial(Maps::newHashMap);

    private List<TransactionVo> queryTransactionVos(TransactionQuery transactionQuery, Set<LoadType> loadTypes) {
        transactionQuery.setLoadTypes(loadTypes);
        List<TransactionVo> transactionVos = Lists.newArrayList();
        if (!transactionQuery.getStoreIdList().contains("-1") && transactionQuery.getLimit() > 0) {
            transactionVos = accountBookBaseService.queryList(transactionQuery);
        }

        //过滤微企付交易，如果是微企付转账超过轮训时间，那么在24小时支付超时时间之内，返回的订单状态由STATUS_ERROR_RECOVERY修改为STATUS_IN_PROG
        transactionVos.forEach(item -> item.setStatus(TransactionUtil.getEntPayStatus(item.getType(), item.getPayWay(), item.getStatus(), item.getCtime())));

        return transactionVos;
    }

    @Override
    public TResultData<List<TAccountRecordDay>> getAccountBookRecords(AccountRecordParam param) {
        try {
            businessService.checkDataPermission(param);
            AccountRecordParam.commonParam(param);
            TransactionQuery transactionQuery = TransactionQuery.buildTransactionQuery(param, DETAIL_URL_PARAM, businessService);
            transactionQuery.setQueryEs(NeedESQueryUtil.getAccountBookRecordsMatch(param));
            //校验收银台和终端的关系
            checkCashDeskInfo(param, transactionQuery);
            List<TransactionVo> transactionVos = queryTransactionVos(transactionQuery, GET_ACCOUNT_BOOK_RECORDS_LOADTYPES);
            Pair<Set<TPayWayIcon>, Map<String, TAccountRecordDay>> pair = buildAccountRecordDayMap(transactionVos, transactionQuery.getOffsetHour());
            Map<String, TransactionDaySumV> transactionDaySumVMap = Maps.newHashMap();
            if (param.getIsSummary()) {
                transactionDaySumVMap = accountBookBaseService.summaryByDay(transactionQuery, pair.snd.keySet());
                completeAccountRecordDay(pair, transactionDaySumVMap);
            }
    
            List<TAccountRecordDay> accountRecordDayList = new LinkedList<>(pair.snd.values());
            if (param.getNeedTodayDefault() && param.getIsSummary() && Objects.isNull(param.getLastRecordTime())) {
                //今日不存在 默认 0
                String today = DateTimeUtil.dateFormat(System.currentTimeMillis(), CommonConstant.PATTERN, transactionQuery.getOffsetHour());
                if (Objects.isNull(transactionDaySumVMap.get(today))) {
                    if (CollectionUtils.isEmpty(accountRecordDayList)) {
                        accountRecordDayList = Lists.newArrayList(new TAccountRecordDay(today, 0L, 0, 0L, 0));
                    } else {
                        accountRecordDayList.add(0, new TAccountRecordDay(today, 0L, 0, 0L, 0));
                    }
                }
            }
            return new TResultData<>(CollectionUtils.isEmpty(transactionVos) ? null : transactionVos.get(transactionVos.size() - 1).getCtime()
                    , pair.fst
                    , accountRecordDayList);
        } finally {
            DETAIL_URL_PARAM.remove();
        }
    }

    @Override
    public TResultData<List<TAccountRecordDay>> getAccountBookRecordsForGroup(AccountRecordParam param) {
        //如果传了部门id集， 获取门店处理 入参：商户存在，部门id集存在，且未指定门店id集
        String merchantIdsStr = param.getMerchantIds();
        if (!org.springframework.util.StringUtils.hasText(merchantIdsStr) &&
                StringUtils.isNotEmpty(param.getGroup_id()) &&
                StringUtils.isNotEmpty(param.getGroup_user_id())){

            Set<String> subMerchantIdsFromGroupId = groupService.getMerchantIds(param.getGroup_user_id());
            if (subMerchantIdsFromGroupId.size() > 400){
                throw new BizException("集团商户下子商户数量过多，不支持app账本功能使用，请前往商户服务平台查询使用");
            }
            merchantIdsStr = subMerchantIdsFromGroupId.stream().collect(Collectors.joining(","));
            param.setMerchantIds(merchantIdsStr);
        }

        if (StringUtils.isNotEmpty(param.getMerchant_user_id()) && StringUtils.isEmpty(param.getStoreIds())){
            if (StringUtils.isEmpty(param.getDepartmentIds())){
                List<TreeDepartmentInfo> treeDepartmentInfo = departmentService.getTreeDepartmentInfo(param.getMerchant_id(), param.getMerchant_user_id());
                if (CollectionUtils.isEmpty(treeDepartmentInfo)){
                    logger.error("部门信息为空，merchant_id :{}, merchant_user_id:{}", param.getMerchant_id(), param.getMerchant_user_id());
                    throw new BizException("该账号对应的部门信息为空");
                }
                param.setDepartmentIds(treeDepartmentInfo.stream().map(TreeDepartmentInfo::getId).collect(Collectors.joining(",")));
            }
            List<String> departmentIds = CommonUtil.splitToListString(param.getDepartmentIds());
            Set<String> storeIds = departmentService.listStoreIdsByDepartmentId(param.getMerchant_id(), departmentIds);
            param.setStoreIds(storeIds.stream().collect(Collectors.joining(",")));
        }
        return getAccountBookRecords(param);
    }

    @Override
    public TResultData<List<TAccountRecordDay>> getMoreAccountBookRecordsWithSource(AccountRecordParam param, String source) {
        businessService.checkDataPermission(param);
        validSource(source);
        AccountRecordParam.commonParam(param);
        TransactionQuery transactionQuery = TransactionQuery.buildTransactionQuery(param, DETAIL_URL_PARAM, businessService);
        transactionQuery.setQueryEs(NeedESQueryUtil.getMoreAccountBookRecordsMatchAndRebuildIfNecessary(param));
        //校验收银台和终端的关系
        checkCashDeskInfo(param, transactionQuery);
        List<TransactionVo> transactionVos = queryTransactionVos(transactionQuery, GET_MORE_ACCOUNT_BOOK_RECORDS_LOADTYPES);
        // 使用ES，未返回足量数据，且startTime为空或超过es存储时间，需要调用查询solr查询部分数据
        if (transactionQuery.isQueryEs()
                && !transactionQuery.isQueryCashDesk()
                && transactionVos.size() != transactionQuery.getLimit()
                && (transactionQuery.getStartTime() == null || transactionQuery.getStartTime() < NeedESQueryUtil.ES_DATA_START)) {
            transactionQuery.setQueryEs(false);
            List<TransactionVo> solrQueryResult = null;
            String requestParams = JsonUtil.toJsonStr(transactionQuery);
            String redisKey = StringUtils.join(new Object[]{KEY_GET_MORE_ACCOUNT_BOOK_RECORDS_CACHE, ":", param.getMerchant_id(), ":", MD5Hash.getMD5AsHex(requestParams.getBytes())});
            BoundValueOperations<String, String> redisValue = redisTemplate.boundValueOps(redisKey);
            if (redisValue.getExpire() == null || redisValue.getExpire() < 0) {
                Long lastEndTime = transactionQuery.getEndTime();
                int lastOffset = transactionQuery.getOffset();
                transactionQuery.setEndTime(NeedESQueryUtil.ES_DATA_START - 1);
                transactionQuery.setOffset(0);
                transactionQuery.setLimit(transactionQuery.getLimit() - transactionVos.size());
                solrQueryResult = queryTransactionVos(transactionQuery, GET_MORE_ACCOUNT_BOOK_RECORDS_LOADTYPES);
                String jsonResult = JsonUtil.toJsonStr(solrQueryResult);
                transactionQuery.setEndTime(lastEndTime);
                transactionQuery.setOffset(lastOffset);
                redisValue.set(jsonResult);
                redisValue.expire(ApolloUtil.getSolrCacheExpireSeconds(), TimeUnit.SECONDS);
                if (transactionVos.isEmpty()) {
                    transactionVos = solrQueryResult;
                } else {
                    transactionVos.addAll(solrQueryResult);
                }
                logger.info("merge solr query, cache key = {}, cache value = {}", requestParams, jsonResult);

            } else {
                try {
                    logger.info("merge solr cache, cache key = {}, cache value = {}", requestParams, redisValue.get());
                    if (!StringUtil.empty(redisValue.get())) {
                        if (transactionVos.isEmpty()) {
                            transactionVos = new ArrayList<TransactionVo>();
                        }
                        solrQueryResult = JsonUtil.objectMapper.readValue(redisValue.get(), new TypeReference<List<TransactionVo>>() {
                        });
                        for (TransactionVo transactionVo : solrQueryResult) {
                            transactionVos.add(transactionVo);
                            if (transactionVos.size() == transactionQuery.getLimit()) {
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("error parse solr redis cache", e);
                    transactionVos = queryTransactionVos(transactionQuery, GET_MORE_ACCOUNT_BOOK_RECORDS_LOADTYPES);
                }
            }
        }
        TransactionSumV transactionSumV = new TransactionSumV();
        if (param.getIsSummary()) {
            // es数据不全，未明确指定开始时间或指定查询时间小于es时，需要查询solr
            if (transactionQuery.isQueryEs()
                    && (transactionQuery.getStartTime() == null || transactionQuery.getStartTime() < NeedESQueryUtil.ES_DATA_START)) {
                transactionQuery.setQueryEs(false);
            }
            transactionSumV = getTransactionSumV(transactionQuery);
        }

        Pair<Set<TPayWayIcon>, TAccountRecordDay> pair = buildAccountRecordDay(transactionVos, transactionSumV, transactionQuery.getOffsetHour());

        Long lastRecordTime = null;
        if (CollectionUtils.isNotEmpty(transactionVos)) {
            // 非排序场景下 lastRecordTime 取本次查询结果集最后一个交易中的 ctime
            if (param.getOrderBy() == null) {
                lastRecordTime = transactionVos.get(transactionVos.size() - 1).getCtime();
            } else { // 有排序场景下 lastRecordTime 一直不变
                lastRecordTime = transactionQuery.getEndTime();
            }
        }

        return new TResultData<>(lastRecordTime, pair.fst, Lists.newArrayList(pair.snd));
    }

    @Override
    public TResultData<List<TAccountRecordDay>> getMoreAccountBookRecords(AccountRecordParam param) {
        return getMoreAccountBookRecordsWithSource(param, CommonConstant.SOURCE_UPAY);
    }


    @Override
    public TResultData<List<TAccountRecordDay>> getMoreAccountBookRecordsForGroup(AccountRecordParam param) {
        //如果传了部门id集， 获取门店处理 入参：商户存在，部门id集存在，且未指定门店id集
        String merchantIdsStr = param.getMerchantIds();
        if (!org.springframework.util.StringUtils.hasText(merchantIdsStr) &&
                StringUtils.isNotEmpty(param.getGroup_id()) &&
                StringUtils.isNotEmpty(param.getGroup_user_id())){

            Set<String> subMerchantIdsFromGroupId = null;
            subMerchantIdsFromGroupId = groupService.getMerchantIds(param.getGroup_user_id());
            if (subMerchantIdsFromGroupId.size() > 400){
                throw new BizException("集团商户下子商户数量过多，不支持app账本功能使用，请前往商户服务平台查询使用");
            }
            merchantIdsStr = subMerchantIdsFromGroupId.stream().collect(Collectors.joining(","));
            param.setMerchantIds(merchantIdsStr);
        }

        if (StringUtils.isNotEmpty(param.getMerchant_user_id()) && StringUtils.isEmpty(param.getStoreIds())){
            if (StringUtils.isEmpty(param.getDepartmentIds())){
                List<TreeDepartmentInfo> treeDepartmentInfo = departmentService.getTreeDepartmentInfo(param.getMerchant_id(), param.getMerchant_user_id());
                if (CollectionUtils.isEmpty(treeDepartmentInfo)){
                    logger.error("部门信息为空，merchant_id :{}, merchant_user_id:{}", param.getMerchant_id(), param.getMerchant_user_id());
                    throw new BizException("该账号对应的部门信息为空");
                }
                param.setDepartmentIds(treeDepartmentInfo.stream().map(TreeDepartmentInfo::getId).collect(Collectors.joining(",")));
            }
            List<String> departmentIds = CommonUtil.splitToListString(param.getDepartmentIds());
            Set<String> storeIds = departmentService.listStoreIdsByDepartmentId(param.getMerchant_id(), departmentIds);
            param.setStoreIds(storeIds.stream().collect(Collectors.joining(",")));
        }
        return getMoreAccountBookRecords(param);
    }

    @Override
    public TAccountSumV getAccountSumWithSource(AccountRecordParam param, String source) {
        businessService.checkDataPermission(param);
        validSource(source);
        AccountRecordParam.commonParam(param);
        param.setGroupBys(null);
        TransactionQuery transactionQuery = TransactionQuery.buildTransactionQuery(param, DETAIL_URL_PARAM, businessService);
        transactionQuery.setQueryEs(NeedESQueryUtil.getAccountSumMatch(param));
        TransactionSumV transactionSumV = getTransactionSumV(transactionQuery);
        Map merchant = merchantService.getMerchantByMerchantId(param.getMerchant_id());
        TAccountSumV accountSumV = transactionSumV.buildTAccountSumV();
        accountSumV.setMerchantId(param.getMerchant_id());
        accountSumV.setMerchantName(BeanUtil.getPropString(merchant, Merchant.NAME, ""));
        return accountSumV;
    }

    @Override
    public TAccountSumV getAccountSum(AccountRecordParam param) {
        return getAccountSumWithSource(param, CommonConstant.SOURCE_UPAY);
    }

    @Override
    public List<TAccountSumV> getAccountSumGroupBy(AccountRecordParam param) {
        businessService.checkDataPermission(param);
        AccountRecordParam.commonParam(param);
        param.setGroupBys(TransactionGroupByKey.payWay.name());//支付源分组汇总
        TransactionQuery transactionQuery = TransactionQuery.buildTransactionQuery(param, DETAIL_URL_PARAM, businessService);
        transactionQuery.setQueryEs(NeedESQueryUtil.getAccountSumGroupByMatch(param));
        List<TransactionSumV> transactionSumV = getTransactionSumVs(transactionQuery);
        Map merchant = merchantService.getMerchantByMerchantId(param.getMerchant_id());
        List<TAccountSumV> tAccountSumVList = Lists.newArrayList();
        TAccountSumV aliPaySum = null;
        for (TransactionSumV v : transactionSumV) {
            TAccountSumV accountSumV = v.buildTAccountSumV();
            //支付宝合并2
            if (v.getPayWay() != null && (Order.PAYWAY_ALIPAY2 == v.getPayWay() || Order.PAYWAY_ALIPAY == v.getPayWay())) {
                if (Objects.isNull(aliPaySum)) {
                    accountSumV.setPayWay(Order.PAYWAY_ALIPAY2);
                    aliPaySum = accountSumV;
                } else {
                    aliPaySum.setCanceldAmount(aliPaySum.getCanceldAmount() + accountSumV.getCanceldAmount());
                    aliPaySum.setCanceldCount(aliPaySum.getCanceldCount() + accountSumV.getCanceldCount());
                    aliPaySum.setDepositAmount(aliPaySum.getDepositAmount() + accountSumV.getDepositAmount());
                    aliPaySum.setDepositCount(aliPaySum.getDepositCount() + accountSumV.getDepositCount());
                    aliPaySum.setPaidAmount(aliPaySum.getPaidAmount() + accountSumV.getPaidAmount());
                    aliPaySum.setPaidCount(aliPaySum.getPaidCount() + accountSumV.getPaidCount());
                    aliPaySum.setSalesAmount(aliPaySum.getSalesAmount() + accountSumV.getSalesAmount());
                    aliPaySum.setSalesCount(aliPaySum.getSalesCount() + accountSumV.getSalesCount());
                    aliPaySum.setRefundedAmount(aliPaySum.getRefundedAmount() + accountSumV.getRefundedAmount());
                    aliPaySum.setRefundedCount(aliPaySum.getRefundedCount() + accountSumV.getRefundedCount());
                    aliPaySum.setDepositCancelCount(aliPaySum.getDepositCancelCount() + accountSumV.getDepositCancelCount());
                    aliPaySum.setDepositCancelAmount(aliPaySum.getDepositCancelAmount() + accountSumV.getDepositCancelAmount());
                    continue;
                }
            }
            accountSumV.setMerchantId(param.getMerchant_id());
            accountSumV.setMerchantName(BeanUtil.getPropString(merchant, Merchant.NAME, ""));
            tAccountSumVList.add(accountSumV);
        }
        return tAccountSumVList;
    }

    private List<TransactionSumV> getTransactionSumVs(TransactionQuery transactionQuery) {
        return accountBookBaseService.summary(transactionQuery);
    }


    private TransactionSumV getTransactionSumV(TransactionQuery transactionQuery) {
        List<TransactionSumV> transactionSumVs = getTransactionSumVs(transactionQuery);
        if (CollectionUtils.isEmpty(transactionSumVs)) {
            return new TransactionDaySumV();
        }
        return transactionSumVs.get(0);
    }

    @Override
    public TAccountRecordDetail getRecordForDetail(String transactionSn, String merchantId) {
        TransactionQuery transactionQuery = new TransactionQuery();
        transactionQuery.setTransactionSn(transactionSn);
        transactionQuery.setMerchantId(merchantId);
        transactionQuery.setLoadTypes(RECORD_FOR_DETAIL_LOADTYPES);
        TransactionVo transactionVo = accountBookBaseService.queryObj(transactionQuery);
        TAccountRecordDetail accountRecordDetail = buildAccountRecordDetail(transactionVo, true);
        setSharingInfo(accountRecordDetail, transactionVo, true); // 设置分账信息（分账列表和分账退回列表）
        if (accountRecordDetail != null) {
            accountRecordDetail.setClearingAmount(Optional.ofNullable(accountRecordDetail.getClearingAmount()).orElse(0L)
                    - Optional.ofNullable(accountRecordDetail.getSharingAmount()).orElse(0L));
        }
        if (accountRecordDetail != null) {
            Map<String, Object> buyerInfo = buyerCommonService.getBuyerInfo(accountRecordDetail.getBuyerUid(), accountRecordDetail.getPayWay());
            if (MapUtils.isNotEmpty(buyerInfo)) {
                accountRecordDetail.setBuyerName(MapUtils.getString(buyerInfo, CommonConstant.BUYER_NAME));
                accountRecordDetail.setBuyerIcon(MapUtils.getString(buyerInfo, CommonConstant.BUYER_ICON));
            }
        }
        return accountRecordDetail;
    }

    @Override
    public TAccountRecordDetail getTransactionDetail(String merchantId, Long ctime, String transactionSn) {
        return getTransactionDetail(merchantId, ctime, transactionSn, true);
    }

    @Override
    public TAccountRecordDetail getTransactionDetail(String merchantId, Long ctime, String transactionSn, Boolean needRefundInfo) {
        GetTransactionDetailByIdParam param = new GetTransactionDetailByIdParam();
        param.setMerchantId(merchantId);
        param.setCtime(ctime);
        param.setTransactionId("t" + transactionSn);
        param.setNeedRefundInfo(Optional.ofNullable(needRefundInfo).orElse(true));
        param.setUpayQueryType(DBSelectContext.getContext().getSelectDb().getCode());
        return this.getTransactionDetailById(param);
    }

    @Override
    public TAccountRecordDetail getTransactionDetailById(GetTransactionDetailByIdParam param) {
        TransactionQuery transactionQuery = new TransactionQuery();
        if (StringUtils.isEmpty(param.getMerchantId()) && StringUtils.isEmpty(param.getMerchantIdForGroupQuery())){
            throw new BizException("商户id 不能为空");
        }
        transactionQuery.setMerchantId(StringUtils.isNotEmpty(param.getMerchantId()) ? param.getMerchantId() : param.getMerchantIdForGroupQuery());
        transactionQuery.setStartTime(param.getCtime());
        transactionQuery.setTransactionId(param.getTransactionId());
        transactionQuery.setLoadTypes(RECORD_FOR_DETAIL_LOADTYPES);

        return buildRecordDetail(transactionQuery, param.getNeedRefundInfo());
    }

    @Override
    public TAccountRecordDetail getTransactionDetailByTsnOrTradeNo(TransactionDetailParam param) {
        TransactionQuery transactionQuery = new TransactionQuery();
        if (StringUtils.isEmpty(param.getTransactionSn()) && StringUtils.isEmpty(param.getTradeNo())){
            throw new BizException("交易流水号 和 支付通道订单号 不能同时为空");
        }
        transactionQuery.setMerchantId(StringUtils.isNotEmpty(param.getMerchantId()) ? param.getMerchantId() : param.getMerchantIdForGroupQuery());
        transactionQuery.setTransactionSn(param.getTransactionSn());
        transactionQuery.setTradeNo(param.getTradeNo());
        //只获取支付交易成功的流水
        StatusTypeSubPayWayQuery remappingFunction = new StatusTypeSubPayWayQuery();
        if (BooleanUtils.isTrue(param.getDepositScan())) { //放开
            remappingFunction.setTypeList(Collections.emptyList());
        } else {
            remappingFunction.setTypeList(Arrays.asList(Transaction.TYPE_PAYMENT, Transaction.TYPE_DEPOSIT_CONSUME));
        }
        transactionQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, remappingFunction);
        transactionQuery.setLoadTypes(RECORD_FOR_DETAIL_LOADTYPES);

        return buildRecordDetail(transactionQuery, param.getNeedRefundInfo());
    }
    @Override
    public TAccountRecordDetail getTransactionDetailByDepositTsn(@NotEmpty(message = "流水单号不能为空") @DBSelectParam String transactionSn,
                                                                 @NotEmpty(message = "商户id不能为空") String merchantId) {
        TransactionQuery currentQuery = new TransactionQuery();
        currentQuery.setTransactionSn(transactionSn);
        currentQuery.setMerchantId(merchantId);
        currentQuery.setLoadTypes(RECORD_FOR_DETAIL_LOADTYPES);
        TransactionVo transactionVo = accountBookBaseService.queryObj(currentQuery);
        if (Objects.isNull(transactionVo)) {
            return null;
        }
        TransactionQuery transactionQuery = new TransactionQuery();
        transactionQuery.setOrderSns(Lists.newArrayList(transactionVo.getOrderSn()));
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
        statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(TransactionStatus.SUCCESS.getCode()));
        transactionQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);
        transactionQuery.setMerchantId(transactionVo.getMerchantId());
        transactionQuery.setLoadTypes(RECORD_FOR_DETAIL_LOADTYPES);
        transactionQuery.setLimit(100); //查出全部明细，目前一笔订单的最大退款次数由apollo配置限制为210次
        if (TransactionType.PAYMENT_TYPES.contains(transactionVo.getType())) {
            transactionQuery.setStartTime(transactionVo.getCtime());
        }
        List<TransactionVo> transactionVos = accountBookBaseService.queryList(transactionQuery);
        List<TransactionVo> transactionList = transactionVos.stream()
                .filter(v -> {
                    if (!Objects.equals(v.getStatus(), Transaction.STATUS_SUCCESS)) {
                        return false;
                    }
                    return TransactionType.DEPOSIT_FREEZE.getCode().equals(v.getType())
                            || TransactionType.DEPOSIT_CONSUME.getCode().equals(v.getType())
                            || TransactionType.DEPOSIT_CANCEL.getCode().equals(v.getType())
                            ;
                }).sorted(Comparator.comparing(TransactionVo::getCtime).reversed()).collect(Collectors.toList());
        return CollectionUtils.isEmpty(transactionList) ? null : buildAccountRecordDetail(transactionList.get(0), false);
    }

    private TAccountRecordDetail buildRecordDetail(TransactionQuery query, Boolean needRefundInfo) {

        TransactionVo transactionVo = accountBookBaseService.queryObj(query);
        TAccountRecordDetail accountRecordDetail = buildAccountRecordDetail(transactionVo, Optional.ofNullable(needRefundInfo).orElse(true));
        setSharingInfo(accountRecordDetail, transactionVo, needRefundInfo); // 设置分账信息（分账列表和分账退回列表）
        if (accountRecordDetail != null) {
            accountRecordDetail.setClearingAmount(Optional.ofNullable(accountRecordDetail.getClearingAmount()).orElse(0L)
                    - Optional.ofNullable(accountRecordDetail.getSharingAmount()).orElse(0L));

            // 添加买家信息
            Map<String, Object> buyerInfo = buyerCommonService.getBuyerInfo(accountRecordDetail.getBuyerUid(), accountRecordDetail.getPayWay());
            if (MapUtils.isNotEmpty(buyerInfo)) {
                accountRecordDetail.setBuyerName(MapUtils.getString(buyerInfo, CommonConstant.BUYER_NAME));
                accountRecordDetail.setBuyerIcon(MapUtils.getString(buyerInfo, CommonConstant.BUYER_ICON));
            }
        }

        return accountRecordDetail;
    }

    private void loadExtraMsg(TransactionVo transactionVo, TAccountRecordDetail tAccountRecordDetail, Boolean needRefundInfo) {
        if (!Boolean.TRUE.equals(needRefundInfo) || transactionVo == null || tAccountRecordDetail == null) {
            return;
        }

        TransactionQuery transactionQuery = new TransactionQuery();
        transactionQuery.setOrderSns(Lists.newArrayList(transactionVo.getOrderSn()));
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
        statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(TransactionStatus.SUCCESS.getCode()));
        transactionQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);
        transactionQuery.setMerchantId(transactionVo.getMerchantId());
        transactionQuery.setLoadTypes(RECORD_FOR_DETAIL_LOADTYPES);
        transactionQuery.setLimit(300); //查出全部退款明细，目前一笔订单的最大退款次数由apollo配置限制为210次
        if (TransactionType.PAYMENT_TYPES.contains(transactionVo.getType())) {
            transactionQuery.setStartTime(transactionVo.getCtime());
        }
        List<TransactionVo> transactionVos = accountBookBaseService.queryList(transactionQuery);
        List<Map> refundMap = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(transactionVos)) {
            Map refund;
            long refundAmount = 0;//退款金额
            long originalAmount = 0;//支付金额
            long refundReceivedAmount = 0;//退款实收金额
            long receivedAmount = 0;//退款实收金额
            for (TransactionVo v : transactionVos) {
                if (v.getType().equals(TransactionType.DEPOSIT_FREEZE.getCode())) {
                    //原商户有授权订单号
                    tAccountRecordDetail.setTradeNoOriginalDeposit(v.getTradeNo());
                    continue;
                } else if(!TransactionConstant.QUERY_TRANSACTION_TYPES.contains(v.getType())) {
                    continue;
                }
                Map extraParams = v.getExtraParams();
                if (Objects.equals(v.getType(), TransactionType.IN_REFUND.getCode())
                        || (Objects.equals(v.getType(), TransactionType.CHARGE_REFUND.getCode())
                        && MapUtils.getBooleanValue(extraParams, "common_charge", false))) {
                    tAccountRecordDetail.setOriginalTransactionId(v.getId());
                    tAccountRecordDetail.setOriginalTransactionSn(v.getTsn());
                    tAccountRecordDetail.setOriginalTransactionCtime(v.getCtime());
                    continue;
                }

                //支付成功
                if ((Objects.equals(v.getType(), TransactionType.PAYMENT.getCode())
                        || Objects.equals(v.getType(), TransactionType.DEPOSIT_CONSUME.getCode())
                        || Objects.equals(v.getType(), TransactionType.CHARGE.getCode())
                        || Objects.equals(v.getType(), TransactionType.ORDER_TAKE.getCode())
                        || Objects.equals(v.getType(), TransactionType.STORE_PAY.getCode())
                        || Objects.equals(v.getType(), TransactionType.STORE_IN.getCode()))
                        && TransactionStatus.SUCCESS.getCode().equals(v.getStatus())) {
                    if (!Objects.equals(v.getTsn(), transactionVo.getTsn())) {
                        tAccountRecordDetail.setOriginalTransactionId(v.getId());
                        tAccountRecordDetail.setOriginalTransactionSn(v.getTsn());
                        tAccountRecordDetail.setOriginalTransactionCtime(v.getCtime());
                    }
                    originalAmount = v.getOriginalAmount();
                    receivedAmount = v.getActualReceiveAmount();
                    continue;
                }
                refundAmount += v.getOriginalAmount();
                refundReceivedAmount += v.getActualReceiveAmount();
                //排除自己
                if (Objects.equals(v.getTsn(), transactionVo.getTsn())) {
                    continue;
                }
                //流水历史列表
                refund = Maps.newTreeMap();
                refund.put("type", v.getType());
                refund.put("transactionId", v.getId());
                refund.put("transactionSn", v.getTsn());
                refund.put("ctime", v.getCtime());
                refund.put("refundFee", v.getOriginalAmount());
                refund.put("actualRefundAmount", v.getActualReceiveAmount());
                refundMap.add(refund);
            }

            if (originalAmount == refundAmount) {
                tAccountRecordDetail.setAllRefund(true);
            }
            tAccountRecordDetail.setNetRefundAmount(originalAmount - refundAmount);
            tAccountRecordDetail.setNetReceivedRefundAmount(receivedAmount - refundReceivedAmount);
        }
        tAccountRecordDetail.setRefundList(refundMap);
    }

    private TAccountRecordDetail buildAccountRecordDetail(TransactionVo transactionVo, Boolean needRefundInfo) {
        if (Objects.isNull(transactionVo)) {
            return null;
        }
        Map<Integer, Object> payWayNames = businessService.getAllPayWayName();
        Set<Integer> chargePayWayCodes = businessService.getAllChargePayWay().keySet();
        TAccountRecordDetail tAccountRecordDetail = transactionVo.buildAccountRecordDetail(payWayNames, chargePayWayCodes);
        //流水历史列表
        loadExtraMsg(transactionVo, tAccountRecordDetail, needRefundInfo);
        return tAccountRecordDetail;
    }

    private Pair<Set<TPayWayIcon>, TAccountRecordDay> buildAccountRecordDay(List<TransactionVo> transactionVos, TransactionSumV transactionSumV, int offsetHour) {

        TAccountRecordDay tAccountRecordResult = new TAccountRecordDay(transactionSumV.getSalesAmount(), transactionSumV.getSalesCount(), transactionSumV.getStoreInTotalAmount(), transactionSumV.getStoreInTotalCount());

        tAccountRecordResult.setTransactions(new ArrayList<>(transactionVos.size()));

        Set<TPayWayIcon> tPayWayIconSet = getDefaultPayWayIcons();

        for (TransactionVo vo : transactionVos) {
            add2AccountRecordDay(tPayWayIconSet, tAccountRecordResult, vo, offsetHour);
        }

        return Pair.of(tPayWayIconSet, tAccountRecordResult);
    }


    private Pair<Set<TPayWayIcon>, Map<String, TAccountRecordDay>> buildAccountRecordDayMap(List<TransactionVo> transactionVos, int offsetHour) {
        Map<String, TAccountRecordDay> tAccountRecordDayMap = Maps.newTreeMap((key1, key2) -> key2.compareTo(key1));
        Set<TPayWayIcon> tPayWayIconSet = getDefaultPayWayIcons();
        TAccountRecordDay tAccountRecordDay;
        String day;
        for (TransactionVo vo : transactionVos) {
            day = DateTimeUtil.dateFormat(vo.getCtime(), CommonConstant.PATTERN, offsetHour);
            tAccountRecordDay = tAccountRecordDayMap.get(day);
            if (tAccountRecordDay == null) {
                tAccountRecordDay = new TAccountRecordDay().setDay(day);
                tAccountRecordDayMap.putIfAbsent(day, tAccountRecordDay);
            }
            add2AccountRecordDay(tPayWayIconSet, tAccountRecordDay, vo, offsetHour);
        }
        return Pair.of(tPayWayIconSet, tAccountRecordDayMap);
    }

    private void add2AccountRecordDay(Set<TPayWayIcon> tPayWayIconSet, TAccountRecordDay tAccountRecordDay, TransactionVo vo, int offsetHour) {
        Map<Integer, Object> payWayIcons = businessService.getAllPayWayIcon();
        Map<Integer, Object> payWayGrayIcons = businessService.getAllPayWayGrayIcon();
        Set<Integer> chargePaywayCodes = businessService.getAllChargePayWay().keySet();

        //payway code和英文名称映射关系
        Map<Integer, String> payWayNameEns = businessService.getAllPayWayNameEn();
        Map<Integer, Object> payWayNames = businessService.getAllPayWayName();
        String payWayStr = MapUtil.getString(payWayNameEns, vo.getPayWay().equals(Order.PAYWAY_ALIPAY2) ? Order.PAYWAY_ALIPAY : vo.getPayWay());
        Integer payWay = StringUtils.isBlank(payWayStr) ? (-1) : vo.getPayWay();
        payWayStr = StringUtils.isEmpty(payWayStr) ? CommonConstant.DEFAULT_PAY_WAY_STR : payWayStr;
        TAccountRecord tAccountRecord = vo.buildAccountRecord(DETAIL_URL_PARAM, payWayStr, payWayNames, chargePaywayCodes, offsetHour);
        tAccountRecordDay.getTransactions().add(tAccountRecord);

        //payway 图标
        tPayWayIconSet.add(new TPayWayIcon(payWayStr
                , MapUtil.getString(payWayIcons, payWay, CommonConstant.DEFAULT_PAYWAY_ICON)
                , MapUtil.getString(payWayGrayIcons, payWay, CommonConstant.DEFAULT_PAYWAY_GRAY_ICON)));
    }

    private Set<TPayWayIcon> getDefaultPayWayIcons() {
        Set<TPayWayIcon> tPayWayIconSet = Sets.newHashSet();
        tPayWayIconSet.add(new TPayWayIcon(CommonConstant.DEFAULT_PAY_WAY_STR
                , CommonConstant.DEFAULT_PAYWAY_ICON
                , CommonConstant.DEFAULT_PAYWAY_GRAY_ICON));
        return tPayWayIconSet;
    }

    private void completeAccountRecordDay(Pair<Set<TPayWayIcon>, Map<String, TAccountRecordDay>> pair, Map<String, TransactionDaySumV> transactionDaySumVMap) {
        TransactionDaySumV transactionDaySumV;
        for (Map.Entry<String, TAccountRecordDay> entry : pair.snd.entrySet()) {
            transactionDaySumV = transactionDaySumVMap.get(entry.getKey());
            entry.getValue().setSalesAmount(transactionDaySumV == null ? 0 : transactionDaySumV.getSalesAmount());
            entry.getValue().setSalesCount(transactionDaySumV == null ? 0 : transactionDaySumV.getSalesCount());
            entry.getValue().setStoreInTotalAmount(transactionDaySumV == null ? 0 : transactionDaySumV.getStoreInTotalAmount());
            entry.getValue().setStoreInTotalCount(transactionDaySumV == null ? 0 : transactionDaySumV.getStoreInTotalCount());
        }
    }

    /**
     * 设置分账相关信息
     */
    private void setSharingInfo(@Nullable TAccountRecordDetail accountRecordDetail, @Nullable TransactionVo transactionVo,
                                Boolean needRefundInfo) {
        if (accountRecordDetail == null || transactionVo == null) {
            return;
        }

        // 如果不是分账交易，无需处理（0 表示不分账；1表示分账）
        Integer sharingFlag = accountRecordDetail.getSharingFlag();
        if (sharingFlag == null || sharingFlag != 1) {
            return;
        }

        Integer transactionType = transactionVo.getType();
        // 正向交易
        if (TransactionType.PAYMENT_TYPES.contains(transactionType)) {
            // 设置分账列表
            accountRecordDetail.setSharingBookList(getSharingBookList(transactionVo));
        } else if (TransactionType.REFUND_TYPES.contains(transactionType)) {
            // 逆向交易（这里是根据当前交易计算的）
            accountRecordDetail.setSharingBookRefundList(getSharingBookList(transactionVo));
        }

        // 设置分账金额
        accountRecordDetail.setSharingAmount(getSharingAmount(transactionVo));
    }

    /**
     * 自己根据分账逻辑计算出分账列表
     */
    private List<TAccountRecordDetail.SharingBook> getSharingBookList(TransactionVo vo) {
        if (vo == null || MapUtils.isEmpty(vo.getExtraParams())) {
            return Collections.emptyList();
        }

        Map extraParams = vo.getExtraParams();
        if (MapUtils.isEmpty(extraParams) || extraParams.get(Transaction.PROFIT_SHARING) == null) {
            return Collections.emptyList();
        }

        Map profitSharing = (Map) extraParams.get(Transaction.PROFIT_SHARING);
        if (MapUtils.isEmpty(profitSharing)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> sharingBookMapList = null;
        Integer type = vo.getType();
        boolean useRound = UpayProfitSharingUtil.useRound(vo.getPayWay(), vo.getProvider());
        // 正向支付
        if (TransactionType.PAYMENT_TYPES.contains(type)) {
            sharingBookMapList = UpayProfitSharingUtil.calculateSharingPayAmount(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), useRound);
        } else if (TransactionType.REFUND_TYPES.contains(type)) { // 反向退款/取消
            Long payClearingAmount = MapUtils.getLong(profitSharing, ProfitSharing.SHARING_PAY_CLEARING_AMOUNT);
            // payClearingAmount 不为空按新的方式计算
            if (payClearingAmount != null) {
                sharingBookMapList = UpayProfitSharingUtil.calculateSharingRestituteAmount(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), payClearingAmount, useRound);
            } else { // payClearingAmount 为空，兼容历史数据
                sharingBookMapList = UpayProfitSharingUtil.calculateSharingPayAmount(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), useRound);
            }
        }
        if (CollectionUtils.isEmpty(sharingBookMapList)) {
            return Collections.emptyList();
        }
        Map<String, Long> sharingReceivers = new HashMap<>();
        Map<String, Map<String, Object>> sharingReceiverDetails = new HashMap<>();
        for (Map<String, Object> sharingBookMap : sharingBookMapList) {
            if(MapUtil.isEmpty(sharingBookMap)) {
                continue;
            }
            long amount = 0L;
            String receiverId = MapUtil.getString(sharingBookMap, ProfitSharing.RECEIVER_ID);
            if(ProfitSharing.SHARING_FLAG_ENABLE.equals(MapUtil.getString(sharingBookMap, ProfitSharing.SHARING_FLAG, ProfitSharing.SHARING_FLAG_ENABLE))) {
                amount = MapUtils.getLongValue(sharingBookMap, ProfitSharing.RECEIVER_AMOUNT);
            }
            
            long totalAmount = MapUtil.getLongValue(sharingBookMap, receiverId) + amount;
            sharingReceivers.put(receiverId, totalAmount);
            sharingReceiverDetails.put(receiverId, sharingBookMap);
        }
        Map<String, Map<String, Object>> recieveInfos = businessService.getReceiverInfo(new ArrayList(sharingReceivers.keySet()));
        // 按照分账业务名称进行分组
        Map<String, TAccountRecordDetail.SharingBook> sharingBooks = new HashMap<String, TAccountRecordDetail.SharingBook>();
        sharingReceivers.forEach((k, v) ->{
            Map<String, Object> receiveInfo = recieveInfos.get(k);
            Integer rType = MapUtil.getInteger(receiveInfo, Receiver.TYPE, ReceiverType.SHARING.getVal());
            String alias = MapUtil.getString(receiveInfo, Receiver.ALIAS, "收款业务分账");
            TAccountRecordDetail.SharingBook sharingBook = sharingBooks.get(alias);
            if(sharingBook == null) {
                sharingBook = new TAccountRecordDetail.SharingBook();
                Map<String, Object> receiver = sharingReceiverDetails.get(k);
                String ratio = MapUtil.getString(receiver, ProfitSharing.RECEIVER_RATIO);
                boolean useSaas = !StringUtil.empty(MapUtil.getString(receiver, "service_fee_id"));
                if (ConfigServiceUtils.chargeNameSet.contains(alias) || useSaas){
                    if (!StringUtil.empty(ratio)) {
                        alias += "（" + ratio + "）";
                    } else if (useSaas) {
                        sharingBook.setShowRatioHelp(true);
                    }
                }
                sharingBooks.put(alias, sharingBook);
                sharingBook.setType(rType);
                sharingBook.setAlias(alias);
                sharingBook.setCtime(vo.getCtime());
                sharingBook.setAmount(0L);
            }
            sharingBook.setAmount(sharingBook.getAmount() + v);
        });
        return new ArrayList<>(sharingBooks.values());
    }


    /**
     * 获取分账金额
     */
    private long getSharingAmount(TransactionVo vo) {
        if (vo == null) {
            return 0L;
        }

        Map extraParams = vo.getExtraParams();
        if (MapUtils.isEmpty(extraParams) || extraParams.get(Transaction.PROFIT_SHARING) == null) {
            return 0L;
        }

        Map profitSharing = (Map) extraParams.get(Transaction.PROFIT_SHARING);
        if (MapUtils.isEmpty(profitSharing)) {
            return 0L;
        }

        long sharingAmount = 0;
        Integer type = vo.getType();
        boolean useRound = UpayProfitSharingUtil.useRound(vo.getPayWay(), vo.getProvider());
        if (TransactionType.PAYMENT_TYPES.contains(type)) {
            sharingAmount = UpayProfitSharingUtil.getSharingPayAmountByProfitSharing(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), useRound);
        } else if (TransactionType.REFUND_TYPES.contains(type)) {
            Long payClearingAmount = MapUtils.getLong(profitSharing, ProfitSharing.SHARING_PAY_CLEARING_AMOUNT);
            if (payClearingAmount != null) {
                sharingAmount = UpayProfitSharingUtil.getSharingRestituteAmountByProfitSharing(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), payClearingAmount, useRound);
            } else { // 兼容历史数据
                sharingAmount = UpayProfitSharingUtil.getSharingPayAmountByProfitSharing(profitSharing, vo.getClearingAmount(), vo.getTradeFee(), useRound);
            }

        }
        return sharingAmount;
    }


    /**
     * 校验收银台和终端等设备关系
     */
    private void checkCashDeskInfo(AccountRecordParam param, TransactionQuery query) {

        if (Strings.isNotBlank(param.getMerchant_user_id())) {
            UcMerchantUserInfo ucMerchantUserInfo = merchantUserServiceV2.getMerchantUserById(param.getMerchant_user_id());
            //如果是收银员，优先查询对应收银台的交易
            if (BasicConsts.CASHIER.equals(ucMerchantUserInfo.getRole())) {
                UcUserInfo ucUserInfo = ucMerchantUserInfo.getUcUserInfo();
                Map<String, Object> cashDesk = cashDeskService.getSimpleCashDeskByMerchantIdAndDeviceId(param.getMerchant_id(), ucUserInfo.getUc_user_id());
                param.setCashDesk(MapUtils.getString(cashDesk, DaoConstants.ID, Strings.EMPTY));
            }
        }
        if (StringUtils.isBlank(param.getCashDesk())) {
            return;
        }
        List<String> cashDeskIds = CommonUtil.splitToListString(param.getCashDesk());
        if (CollectionUtils.isNotEmpty(query.getTerminalList())) {
            List<String> cashDeskDevices = new ArrayList<>();
            cashDeskIds.forEach(cashDeskId -> {
                Map<String, Object> cashDesk = cashDeskService.getCashDeskWithDevicesById(cashDeskId);
                List<Map> devices = (List<Map>) MapUtils.getObject(cashDesk, "devices");
                cashDeskDevices.addAll(devices.stream().map(device -> BeanUtil.getPropString(device, CashDeskDevice.DEVICE_ID)).collect(Collectors.toList()));
            });

            List<String> terminalList = Lists.newArrayList();
            terminalList.addAll(CommonUtil.splitToListString(StringUtils.isNotBlank(param.getPcplugin()) ? param.getPcplugin().replaceAll("pcplugin_", "") : ""));
            terminalList.addAll(CommonUtil.splitToListString(StringUtils.isNotBlank(param.getPos()) ? param.getPos().replaceAll("pos_", "") : ""));
            terminalList.addAll(CommonUtil.splitToListString(StringUtils.isNotBlank(param.getPosplus()) ? param.getPosplus().replaceAll("posplus_", "") : ""));
            terminalList.addAll(CommonUtil.splitToListString(StringUtils.isNotBlank(param.getMaya()) ? param.getMaya().replaceAll("maya_", "") : ""));
            terminalList.addAll(CommonUtil.splitToListString(StringUtils.isNotBlank(param.getMobile()) ? param.getMobile().replaceAll("mobile_", "") : ""));
            CommonUtil.splitToListString(StringUtils.isNotBlank(param.getQrcode()) ? param.getQrcode().replaceAll("qrcode_", "") : "").forEach(qrcode -> {
                terminalList.add(MapUtil.getString(businessService.getTerminalInfoByDeviceFingerprint(qrcode), DaoConstants.ID));
            });
            terminalList.addAll(CommonUtil.splitToListString(StringUtils.isNotBlank(param.getSocode()) ? param.getSocode().replaceAll("socode_", "") : ""));
            terminalList.addAll(CommonUtil.splitToListString(StringUtils.isNotBlank(param.getNpos2()) ? param.getNpos2().replaceAll("npos2_", "") : ""));
            terminalList.addAll(CommonUtil.splitToListString(StringUtils.isNotBlank(param.getGroupmeal()) ? param.getGroupmeal().replaceAll("groupmeal_", "") : ""));
            terminalList.addAll(CommonUtil.splitToListString(StringUtils.isNotBlank(param.getFaceScan()) ? param.getFaceScan().replaceAll("faceScan_", "") : ""));
            if (!StringUtils.isEmpty(param.getTerminals())) {
                terminalList.addAll(CommonUtil.splitToListString(param.getTerminals()));
            }

            List<String> reduce = terminalList.stream().filter(item -> !cashDeskDevices.contains(item)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(reduce)) {
                //若存在非对应收银台的终端查询，即返回空
                query.setTerminalList(reduce);
            }
        }

        query.setQueryCashDesk(true);
        query.setCashDeskList(cashDeskIds);
    }

    /**
     * 校验账本相关接口的业务方权限
     * @param source
     */
    private void validSource(String source) {

        Set<String> sources = ApolloUtil.getFilterSource();
        //默认支持原接口的 source - upay
        sources.add(CommonConstant.SOURCE_UPAY);
        if (!sources.contains(source)) {
            throw ErrorMessageEnum.ACCOUNT_BOOK_QUERY_INVALID_SOURCE_ERROR.getBizException();
        }

    }
}
