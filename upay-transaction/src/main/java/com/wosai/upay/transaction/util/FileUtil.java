package com.wosai.upay.transaction.util;

import com.wosai.upay.transaction.constant.CommonConstant;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class FileUtil {
    public static File excelToFile(SXSSFWorkbook workbook, String filePath) throws IOException{
        File file = createDirFile(filePath);
        FileOutputStream fileOutStream = null;
        try {
            fileOutStream = new FileOutputStream(file);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        BufferedOutputStream out = new BufferedOutputStream(fileOutStream);
        workbook.write(out);
        out.flush();
        out.close();
        workbook.dispose();
        return file;
    }

    public static void iteratorMkdirs(File file) {
        if (file == null) {
            return;
        }
        while (!file.getParentFile().exists()) {
            iteratorMkdirs(file.getParentFile());
            file.getParentFile().mkdirs();
        }
    }

    public static File createDirFile(String pathname) {
        File file = new File(pathname);
        iteratorMkdirs(file);
        return new File(pathname);

    }

    public static String formateStatementPathFile(String dir, String type, long start, long end, String ext) {
        return dir + String.format("%s%s_%s.%s",
                type,
                CommonConstant.DAY_SDF.get().format(start),
                CommonConstant.DAY_SDF.get().format(end),
                ext);
    }

}
