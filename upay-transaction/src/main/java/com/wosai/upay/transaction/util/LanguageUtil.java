package com.wosai.upay.transaction.util;

import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.transaction.model.StatementObjectConfig;
import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class LanguageUtil {

    private static final ThreadLocal<ResourceBundle> LANGUAGE = new ThreadLocal<>();

    public static final String DEFAULT_LANGUAGE = "zh";
    public static final int DEFAULT_SPLIT_TYPE = 1;

    public static final Map DEFAULT_SHEET_PARAM = CollectionUtil.hashMap(StatementObjectConfig.SHEET_TYPE, 1,
            StatementObjectConfig.TERMINAL_TYPE, 0,
            StatementObjectConfig.CRO, Arrays.asList(
                    StatementObjectConfig.TRANSACTION_NO, StatementObjectConfig.TRANSACTION_AMOUNT, StatementObjectConfig.REFUND_NO, StatementObjectConfig.REFUND_AMOUNT, StatementObjectConfig.TRANSACTION_NET_AMOUNT,
                    StatementObjectConfig.MERCHANT_DISCOUNT, StatementObjectConfig.WOSAI_DISCOUNT, StatementObjectConfig.PAYMENT_TYPE_DISCOUNT, StatementObjectConfig.MERCHANT_DISCOUNT_PREPAID_DISCOUNT,
                    StatementObjectConfig.MERCHANT_DISCOUNT_NON_PREPAID_DISCOUNT, StatementObjectConfig.PAID_AMOUNT, StatementObjectConfig.CHANRGE, StatementObjectConfig.SHARING_AMOUNT,
                    StatementObjectConfig.TRADE_SERVICE_SHARING_AMOUNT, StatementObjectConfig.MERCHANT_SHARING_AMOUNT, StatementObjectConfig.SETTLEMENT_AMOUNT, StatementObjectConfig.DEAL_TO_ACCOUNT_AMOUNT),
            StatementObjectConfig.PAYWAY, Arrays.asList(
                    StatementObjectConfig.ALIPAY_OLD, StatementObjectConfig.ALIPAY_NEW, StatementObjectConfig.WEIXIN,
                    StatementObjectConfig.BAIPAY, StatementObjectConfig.JDPAY, StatementObjectConfig.QQPAY,
                    StatementObjectConfig.LKLPAY, StatementObjectConfig.CMCCPAY,
                    StatementObjectConfig.LKL_WEIXIN, StatementObjectConfig.ZSYHPAY, StatementObjectConfig.YLCODEPAY,
                    StatementObjectConfig.YIPAY, StatementObjectConfig.WEIXIN_HK, StatementObjectConfig.ALIPAY_HK_LOCAL, StatementObjectConfig.PAYWAY_DCEP, StatementObjectConfig.BANK_CARD));

    public static final List<String> SUPPORT_LANGUAGE = Arrays.asList("zh", "zh_HK", "en");


    /**
     * /查找文字,第一次全量匹配，第二次忽略的字符串
     */
    private static final String IGNORE_WORD_REGEX = "[:%]";

    /**
     * 起始日期
     */
    public static final String PERIOD = "period";
    /**
     * 商户号
     */
    public static final String MERCHANT_NO = "merchant_no";
    /**
     * 商户名称
     */
    public static final String MERCHANT_NAME = "merchant_name";

    /**
     * 对账单类型
     */
    public static final String STATEMENT_STYLE = "statement_style";


    /**
     * 商户交易汇总
     */
    public static final String MERCHANT_TRANSACTION_SUMMARY = "merchant_transaction_summary";
    /**
     * 商户交易清单
     */
    public static final String MERCHANT_TRANSACTION_SUMMARY_LIST = "merchant_transaction_summary_list";

    /**
     * 门店收款通道维度汇总
     */
    public static final String MERCHANT_STORE_PAYWAY_TRANSACTION_SUMMARY = "merchant_store_payway_transaction_summary";
    /**
     * 收款通道/支付方式
     */
    public static final String PAYMENT_TYPE = "payment_type";
    /**
     * 交易笔数
     */
    public static final String TRANSACTION_NO = "transaction_no";
    /**
     * 交易金额
     */
    public static final String TRANSACTION_AMOUNT = "transaction_amount";
    /**
     * /退款笔数
     */
    public static final String REFUND_NO = "refund_no";
    /**
     * 退款金额
     */
    public static final String REFUND_AMOUNT = "refund_amount";
    /**
     * 收款净额
     */
    public static final String TRANSACTION_NET_AMOUNT = "transaction_net_amount";
    /**
     * 商户优惠
     */
    public static final String MERCHANT_DISCOUNT = "merchant_discount";
    /**
     * 收钱吧优惠
     */
    public static final String WOSAI_DISCOUNT = "wosai_discount";
    /**
     *  收款通道机构优惠
     */
    public static final String PAYMENT_TYPE_DISCOUNT = "payment_type_discount";
    /**
     * 收款通道商户预充值优惠
     */
    public static final String MERCHANT_DISCOUNT_PREPAID_MODE = "merchant_discount_prepaid_mode";
    /**
     * 收款通道商户免充值优惠
     */
    public static final String MERCHANT_DISCOUNT_NON_PREPAID_MODE = "merchant_discount_non_prepaid_mode";
    /**
     * 实收金额
     */
    public static final String PAID_AMOUNT = "paid_amount";
    /**
     * 手续费
     */
    public static final String CHARGE = "charge";
    /**
     * 汇总部分结算金额
     */
    public static final String SETTLEMENT_AMOUNT = "settlement_amount";
    /**
     * 明细部分结算金额
     */
    public static final String DETAIL_SETTLEMENT_AMOUNT = "detail_settlement_amount";
    /**
     * 可结算金额 即到账金额 （由于存在KA机读商户，故保留可结算金额）
     */
    public static final String TOTAL_SETTLEMENT_TO_ACCOUNT_AMOUNT = "total_settlement_to_account_amount";

    /**
     * 到账金额 即可结算金额
     */
    public static final String TOTAL_DEAL_TO_ACCOUNT_AMOUNT = "total_deal_to_account_amount";
    /**
     * 结算金额
     */
    public static final String TOTAL_SETTLEMENT_AMOUNT = "total_settlement_amount";
    /**
     * 营业额可结算金额 即成交到账金额 （由于存在KA机读商户，故保留营业额可结算金额）
     */
    public static final String SETTLEMENT_TO_ACCOUNT_AMOUNT = "settlement_to_account_amount";

    /**
     * 收款净额到账金额 即营业额可结算金额
     */
    public static final String DEAL_TO_ACCOUNT_AMOUNT = "deal_to_account_amount";

    /**
     * 收款净额到账金额(不含外卡)
     */
    public static final String DEAL_NO_FOREIGN_CARD_TO_ACCOUNT_AMOUNT = "deal_no_foreign_card_to_account_amount";

    /**
     * 收款净额到账金额(外卡)
     */
    public static final String FOREIGN_CARD_TRADE_TO_ACCOUNT_AMOUNT = "foreign_card_trade_to_account_amount";

    /**
     * 储值充值结算金额
     */
    public static final String STORE_IN_TOTAL_SETTLEMENT_AMOUNT = "store_in_total_settlement_amount";
    /**
     * 储值充值可结算金额 即储值充值到账金额 （由于存在KA机读商户，故保留储值充值可结算金额）
     */
    public static final String STORE_IN_SETTLEMENT_TO_ACCOUNT_AMOUNT = "store_in_settlement_to_account_amount";

    /**
     * 储值充值到账金额 即储值充值可结算金额
     */
    public static final String STORE_IN_DEAL_TO_ACCOUNT_AMOUNT = "store_deal_to_account_amount";

    /**
     * 商家记账结算金额
     */
    public static final String CHARGE_SETTLEMENT_AMOUNT = "charge_settlement_amount";
    /**
     * 储值核销实收金额
     */
    public static final String STORE_PAY_RECEIVE_AMOUNT = "store_pay_receive_amount";
    /**
     * 现金支付充值金额
     */
    public static final String STORE_IN_CASH_SETTLEMENT_AMOUNT = "store_in_cash_settlement_amount";
    /**
     * 其他方式记账充值金额
     */
    public static final String STORE_IN_OTHER_CHARGE_SETTLEMENT_AMOUNT = "store_in_other_charge_settlement_amount";
    /**
     * 直清交易结算金额
     */
    public static final String LIQUIDATION_SETTLEMENT_AMOUNT = "liquidation_settlement_amount";

    /**
     * 外卡交易结算金额
     */
    public static final String FOREIGN_CARD_SETTLEMENT_AMOUNT = "foreign_card_settlement_amount";
    /**
     * 直清充值结算金额
     */
    public static final String STORE_IN_LIQUIDATION_SETTLEMENT_AMOUNT = "store_in_liquidation_settlement_amount";

    public static final String CROSS_MERCHANT_REFUND_NO  = "cross_merchant_refund_no";
    public static final String CROSS_MERCHANT_REFUND_AMOUNT  = "cross_merchant_refund_amount";

    /**
     * 储值交易笔数
     */
    public static final String STORE_IN_TRANSACTION_NO = "store_transaction_no";
    /**
     * 储值交易金额
     */
    public static final String STORE_IN_TRANSACTION_AMOUNT = "store_transaction_amount";
    /**
     * 储值退款笔数
     */
    public static final String STORE_IN_REFUND_NO = "store_refund_no";
    /**
     * 储值退款金额
     */
    public static final String STORE_IN_REFUND_AMOUNT = "store_refund_amount";
    /**
     * 储值收款净额
     */
    public static final String STORE_IN_TRANSACTION_NET_AMOUNT = "store_transaction_net_amount";
    /**
     * 储值商户优惠
     */
    public static final String STORE_IN_MERCHANT_DISCOUNT = "store_merchant_discount";
    /**
     * 储值收钱吧优惠
     */
    public static final String STORE_IN_WOSAI_DISCOUNT = "store_wosai_discount";
    /**
     * 储值收款通道机构优惠
     */
    public static final String STORE_IN_PAYMENT_TYPE_DISCOUNT = "store_payment_type_discount";
    /**
     * 储值收款通道商户预充值优惠
     */
    public static final String STORE_IN_MERCHANT_DISCOUNT_PREPAID_MODE = "store_merchant_discount_prepaid_mode";
    /**
     * 储值收款通道商户免充值优惠
     */
    public static final String STORE_IN_MERCHANT_DISCOUNT_NON_PREPAID_MODE = "store_merchant_discount_non_prepaid_mode";
    /**
     * 储值实收金额
     */
    public static final String STORE_IN_PAID_AMOUNT = "store_paid_amount";
    /**
     * 储值手续费
     */
    public static final String STORE_IN_CHARGE = "store_charge";
    /**
     * 储值结算金额
     */
    public static final String STORE_IN_SETTLEMENT_AMOUNT = "store_settlement_amount";

    /**
     * 储值分账金额
     */
    public static final String STORE_IN_SHARING_AMOUNT = "store_sharing_amount";

    /**
     * 储值商户自主分账金额
     */
    public static final String STORE_IN_MERCHANT_SHARING_AMOUNT = "store_merchant_sharing_amount";

    /**
     * 储值技术服务费
     */
    public static final String STORE_IN_TRADE_SERVICE_SHARING_AMOUNT = "store_trade_service_sharing_amount";

    /**
     * 支付宝2.0交易笔数
     */
    public static final String ALIPAY_NEW_NO = "alipay_new_no";
    /**
     * 支付宝2.0实收金额
     */
    public static final String ALIPAY_NEW_PAID_AMOUNT  = "alipay_new_paid_amount";
    /**
     * 支付宝2.0结算金额
     */
    public static final String ALIPAY_NEW_SETTLEMENT_AMOUNT  = "alipay_new_settlement_amount";
    /**
     * 微信交易笔数
     */
    public static final String WECHAT_NO = "wechat_no";
    /**
     * 微信实收金额
     */
    public static final String WECHAT_PAID_AMOUNT  = "wechat_paid_amount";
    /**
     * 微信结算金额
     */
    public static final String WECHAT_SETTLEMENT_AMOUNT  = "wechat_settlement_amount";
    /**
     * 银联云闪付交易笔数
     */
    public static final String UNIONPAY_CLOUD_FLASH_PAYMENT_NO = "unionpay_cloud_flash_payment_no";
    /**
     * 银联云闪付实收金额
     */
    public static final String UNIONPAY_CLOUD_FLASH_PAYMENT_PAID_AMOUNT  = "unionpay_cloud_flash_payment_paid_amount";
    /**
     * 银联云闪付结算金额
     */
    public static final String UNIONPAY_CLOUD_FLASH_PAYMENT_SETTLEMENT_AMOUNT  = "unionpay_cloud_flash_payment_settlement_amount";
    /**
     * 银行卡交易笔数
     */
    public static final String BANKCARD_PAYMENT_NO = "bankcard_payment_no";
    /**
     * 银行卡实收金额
     */
    public static final String BANKCARD_PAYMENT_PAID_AMOUNT  = "bankcard_payment_paid_amount";
    /**
     * 银行卡结算金额
     */
    public static final String BANKCARD_PAYMENT_SETTLEMENT_AMOUNT  = "bankcard_payment_settlement_amount";


    /**
     * 数字人民币交易笔数
     */
    public static final String DCEP_PAYMENT_NO = "dcep_payment_no";

    /**
     * 数字人民币实收金额
     */
    public static final String DCEP_PAYMENT_PAID_AMOUNT = "dcep_payment_paid_amount";

    /**
     * 数字人民币结算金额
     */
    public static final String DCEP_PAYMENT_SETTLEMENT_AMOUNT = "dcep_payment_settlement_amount";

    /**
     * 币种  货币类型
     */
    public static final String CURRENCY = "currency";
    /**
     * 门店终端交易汇总清单
     */
    public static final String STORE_TERMINAL_TRANSACTION_SUMMARY_LIST = "store_terminal_transaction_summary_list";
    /**
     * 门店号
     */
    public static final String STORE_NO = "store_no";
    /**
     * 商户门店号
     */
    public static final String MERCHANT_STORE_NO = "merchant_store_no";
    /**
     * 门店名称
     */
    public static final String STORE_NAME = "store_name";
    /**
     * /终端号
     */
    public static final String TERMINAL_NO = "terminal_no";
    /**
     * 商户终端号
     */
    public static final String MERCHANT_TERMINAL_NO = "merchant_terminal_no";
    /**
     * 终端名称
     */
    public static final String TERMINAL_NAME = "terminal_name";
    /**
     * 终端类型
     */
    public static final String TERMINAL_TYPE = "terminal_type";
    /**
     * 设备号
     */
    public static final String DEVICE_ID = "device_id";
    /**
     * 操作员
     */
    public static final String OPERATOR = "operator";
    /**
     * 收银员
     */
    public static final String CASHIER = "cashier";
    /**
     * 收钱吧交易明细
     */
    public static final String WOSAI_TRANSACTION_DETAILS = "wosai_transaction_details";
    /**
     * 交易明细列表
     */
    public static final String TRANSACTION_DETAILS_LIST = "transaction_details_list";
    /**
     * 交易日期
     */
    public static final String TRANSACTION_DATE = "transaction_date";
    /**
     * 时间
     */
    public static final String TIME = "time";
    /**
     * 商户流水号
     */
    public static final String MERCHANT_SERIAL_NO = "merchant_serial_no";
    /**
     * 商品名
     */
    public static final String PRODUCT_NAME = "product_name";
    /**
     * 商户内部订单号
     */
    public static final String MERCHANT_ORDER_NO = "merchant_order_no";
    /**
     * 商户订单号
     */
    public static final String WOSAI_ORDER_NO = "wosai_order_no";
    /**
     * 收款通道订单号
     */
    public static final String PAYMENT_TYPE_ORDER_NO = "payment_type_order_no";
    /**
     * 交易类型 交易模式
     */
    public static final String TRANSACTION_TYPE = "transaction_type";
    /**
     * 交易状态
     */
    public static final String TRANSACTION_STATUS = "transaction_status";
    /**
     * 付款账户
     */
    public static final String CONSUMER_ACCOUNT = "consumer_account";
    /**
     * 消费者实付金额
     */
    public static final String THE_AMOUNT_CONSUMER_PAID = "the_amount_consumer_paid";
    /**
     * 扣率
     */
    public static final String CHARGE_RATE = "charge_rate";
    /**
     * 备注
     */
    public static final String REMARKS = "remarks";

    /**
     * 支付方式
     */
    public static final String PAY_TYPE = "pay_type";
    /**
     * 批次号
     */
    public static final String BATCH_BILL_NO = "batchbillno";
    /**
     * 凭证号
     */
    public static final String SYS_TRACE_NO = "systraceno";
    /**
     * 付款银行
     */
    public static final String BANK_TYPE = "bank_type";
    /**
     * 拉卡拉商户号
     */
    public static final String LAKALA_MERC_ID = "lakala_merc_id";
    /**
     * 拉卡拉终端号
     */
    public static final String LAKALA_TERM_ID = "lakala_term_id";
    /**
     * 系统参考号
     */
    public static final String TRADE_NO = "trade_no";
    /**
     * 刷卡完成时间
     */
    public static final String CHANNEL_FINISH_TIME = "channel_finish_time";


    /**
     * 操作门店号
     */
    public static final String OP_STORE_SN = "op_store_sn";
    /**
     * 操作商户门店号
     */
    public static final String OP_MCH_SN = "op_mch_store_sn";
    /**
     * 操作门店名称
     */
    public static final String OP_STORE_NAME = "op_store_name";
    /**
     * 支付渠道, 针对银行卡交易，在前面加上刷卡业务或外卡刷卡业务
     */
    public static final String PAY_CHANNEL = "pay_channel";
    /**
     * 支付源资金渠道
     */
    public static final String PAY_FUND_CHANNEL = "pay_fund_channel";

    /**
     * 是否跨主体退货
     */
    public static final String IS_CROSS_MCH_REFUND = "is_cross_mch_refund";
    /**
     * 订单状态
     */
    public static final String ORDER_STATUS = "order_status";
    /**
     * 已关闭
     */
    public static final String CLOSED = "closed";
    /**
     * 已部分退款
     */
    public static final String PARTIALLY_REFUNDED = "partially_refunded";
    /**
     * 已全部退款
     */
    public static final String FULL_REFUND = "full_refund";
    /**
     * 订单金额
     */
    public static final String TRANSACTION_ORDER_AMOUNT = "transaction_order_amount";
    /**
     * 是否试用
     */
    public static final String TRIAL_NOT_TRIAL = "trial_not_trial";
    /**
     * /对账单编号
     */
    public static final String STATEMENT_NO = "statement_no";
    /**
     * 集团基本信息
     */
    public static final String GROUP_INFORMATION = "group_information";
    /**
     * 集团编号
     */
    public static final String GROUP_NO = "group_no";
    /**
     * 集团名称
     */
    public static final String GROUP_NAME = "group_name";
    /**
     * 集团交易汇总清单
     */
    public static final String GROUP_TRANSACTION_SUMMARY = "group_transaction_summary";

    /**
     * 付款
     */
    public static final String TRANSACTION_TYPE_PAYMENT = "transaction_type_payment";
    /**
     * 退款
     */
    public static final String TRANSACTION_TYPE_RETURN = "transaction_type_return";
    /**
     * 预授权
     */
    public static final String TRANSACTION_DEPOSIT_FREEZE = "transaction_deposit_freeze";
    /**
     * 预授权撤销
     */
    public static final String TRANSACTION_DEPOSIT_CANCEL = "transaction_deposit_cancel";
    /**
     * 预授权完成
     */
    public static final String TRANSACTION_DEPOSIT_CONSUME = "transaction_deposit_consume";
    /**
     * 预授权完成撤销
     */
    public static final String TRANSACTION_DEPOSIT_CONSUME_CANCEL = "transaction_deposit_consume_cancel";
    /**
     * 储值账户充值
     */
    public static final String TRANSACTION_TYPE_STORE_IN_PAYMENT = "transaction_type_store_in_payment";
    /**
     * 储值账户充值退款
     */
    public static final String TRANSACTION_TYPE_STORE_IN_RETURN = "transaction_type_store_in_return";
    /**
     * 储值账户核销
     */
    public static final String TRANSACTION_TYPE_STORE_PAY_PAYMENT = "transaction_type_store_pay_payment";
    /**
     * 储值账户核销退款
     */
    public static final String TRANSACTION_TYPE_STORE_PAY_RETURN = "transaction_type_store_pay_return";

    /**
     * 成功
     */
    public static final String TRANSACTION_STATUS_SUCCESS = "transaction_status_success";
    /**
     * 失败
     */
    public static final String TRANSACTION_STATUS_FAILURE = "transaction_status_failure";
    /**
     * 处理中
     */
    public static final String TRANSACTION_STATUS_IN_PROCESS = "transaction_status_in_process";
    /**
     * 账单汇总
     */
    public static final String TRANSACTION_SUMMARY = "transaction_summary";
    /**
     * 账单明细
     */
    public static final String TRANSACTION_DETAILS = "transaction_details";
    /**
     * 对账单
     */
    public static final String SETTLEMENT = "settlement";
    /**
     * 退款撤销
     */
    public static final String REFUND_WITHDRAWAL = "refund_withdrawal";
    /**
     * 撤单
     */
    public static final String WITHDRAWAL = "withdrawal";
    /**
     * 商户基本信息
     */
    public static final String MERCHANT_BASIC_INFORMATION = "merchant_basic_information";

    /**
     * 对账单汇总
     */
    public static final String SETTLEMENT_SUMMARY_WOSAI = "settlement_summary_wosai";
    /**
     * 对账单明细
     */
    public static final String SETTLEMENT_DETAILS_WOSAI = "settlement_details_wosai";
    /**
     * 跨商户退款明细
     */
    public static final String SETTLEMENT_DETAILS_CROSS_NCH_REFUND_WOSAI = "settlement_details_cross_mch_refund_wosai";


    public static final String ANDROID = "Android";

    /**
     * 产品标识
     */
    public static final String PRODUCT_FLAG = "product_flag";

    /**
     * 交易模式
     */
    public static final String SUBPAYWAY = "subpayway";

    /**
     * 门店收银台交易汇总清单
     */
    public static final String STORE_CASH_DESK_TRANSACTION_SUMMARY_LIST = "store_cash_desk_transaction_summary_list";

    /**
     * 门店收银台交接班汇总清单
     */
    public static final String STORE_CASH_DESK_CHANGE_SHIFTS_SUMMARY_LIST = "store_cash_desk_change_shifts_summary_list";

    /**
     * 门店收银台交接班汇总清单
     */
    public static final String STORE_CASHIER_TRANSACTION_SUMMARY_LIST = "store_cashier_transaction_summary_list";

    /**
     * 收银台
     */
    public static final String CASH_DESK_NAME = "cash_desk_name";

    /**
     * 收银员（班次收银员)
     */
    public static final String CASHIER_NAME = "cashier_name";

    /**
     * 收银员手机号（班次收银员)
     */
    public static final String CASHIER_PHONE = "cashier_phone";

    /**
     * 收银台编号
     */
    public static final String CASH_DESK_NO = "cash_desk_no";

    /**
     * 签到批次号
     */
    public static final String CHANGE_SHIFTS_BATCH_NO = "change_shifts_batch_no";

    /**
     * 批次签到时间
     */
    public static final String CHANGE_SHIFTS_START_DATE = "change_shifts_start_date";

    /**
     * 批次签退时间
     */
    public static final String CHANGE_SHIFTS_END_DATE = "change_shifts_end_date";

    /**
     * 分账标识
     */
    public static final String SHARING_FLAG = "sharing_flag";
    /**
     * 分账金额
     */
    public static final String SHARING_AMOUNT = "sharing_amount";

    /**
     * 商户自主分账金额
     */
    public static final String MERCHANT_SHARING_AMOUNT = "merchant_sharing_amount";

    /**
     * 技术服务费
     */
    public static final String TRADE_SERVICE_SHARING_AMOUNT = "trade_service_sharing_amount";


    public static final String IOS = "iOS";
    /**
     * Windows桌面版
     */
    public static final String PC_SOFTWARE = "pc_software";
    /**
     * 专用设备
     */
    public static final String PAYMENT_DEVICE = "payment_device";
    /**
     * 门店码
     */
    public static final String STORE_QR_CODE = "store_qr_code";
    /**
     * 服务
     */
    public static final String SERVICE = "service";


    /**
     * 部门
     */
    public static final String DEPARTMENT = "department";
    /**
     * 汇总
     */
    public static final String SUMMARY = "summary";
    /**
     * 门店
     */
    public static final String STORE = "store";
    /**
     * 门店纬度交易汇总
     */
    public static final String STORE_TRANSACTION_SUMMARY = "store_transaction_summary";
    /**
     * 结算资金汇总
     */
    public static final String WITHDRAW_FUNDS_SUMMARY = "withdraw_funds_summary";
    /**
     * 到账资金汇总
     */
    public static final String TO_ACCOUNT_FUNDS_SUMMARY = "to_account_funds_summary";
    /**
     * 到账资金汇总(包含外卡)
     */
    public static final String TO_ACCOUNT_FUNDS_SUMMARY_WITH_FOREIGN_CARD = "to_account_funds_summary_with_foreign_card";
    /**
     * 管理员
     */
    public static final String ADMIN = "admin";
    /**
     * 分期对账单
     */
    public static final String FQ_TRANSACTION_DETAILS = "fq_transaction_details";
    /**
     * 分期交易明细列表
     */
    public static final String FQ_TRANSACTION_DETAILS_LIST = "fq_transaction_details_list";
    /**
     * 分期数
     */
    public static final String FQ_NUM = "fq_num";
    /**
     * 分期商家贴息金额
     */
    public static final String HBFQ_MCH_DISCOUNT_AMOUNT = "fq_mch_discount_amount";
    /**
     * 分期付息类型
     */
    public static final String FQ_PAY_TYPE = "fq_pay_type";
    /**
     * 分期门店纬度交易汇总
     */
    public static final String FQ_STORE_TRANSACTION_SUMMARY = "fq_store_transaction_summary";
    /**
     * 是否使用额度包 true 使用 false不使用
     */
    public static final String USE_QUOTA = "useQuota";

    /**
     * 交易流水号
     */
    public static final String TRADE_TSN = "trade_tsn";

    /**
     * 交易场景
     */
    public static final String SQB_BIZ_MODEL = "sqb_biz_model";

    //智慧点单服务费
    public static final String SMART_ORDER = "smart_order";
    //流量服务费
    public static final String SMART_FLOW_SERVICE = "smart_flowservice";
    //校园配送服务费
    public static final String SMART_CAMPUS_DISTRIBUTION = "smart_campusdistribution";
    //第三方配送服务费
    public static final String SAAS_THIRD_DISTRIBUTION = "smart_thirddistribution";
    //券包出售服务费
    public static final String SAAS_COUPON_SALE = "saas_couponsale";
    //权益卡出售服务费
    public static final String SAAS_EQUITY_CARD_SALE = "saas_equitycardsale";
    //储值充值服务费
    public static final String SAAS_CARD_RECHARGE = "saas_cardrecharge";
    //花呗分期免息营销服务费
    public static final String HBFQ_INDIRECT_TIEXI = "hbfq_indirect_tiexi";
    //先享后付服务费
    public static final String PAY_AFTER_USE = "pay_after_user";
    //流量服务费固定佣金
    public static final String SMART_FLOW_SERVICE_COMMISSION = "smart_flow_service_commission";
    //流量服务费起步价
    public static final String SMART_FLOW_SERVICE_BASE_AMOUNT = "smart_flow_service_base_amount";

    public static final String FQ_TYPE_BUYER = "fq_type_buyer";
    public static final String FQ_TYPE_SELLER = "fq_type_seller";
    public static final String FQ_TYPE = "fq_type";
    public static final String HB_FQ_TYPE = "hb_fq_type";
    public static final String CREDIT_FQ_TYPE = "credit_fq_type";
    public static ResourceBundle getBundle(String language) {
        String[] parts = language.split("_");
        Locale locale = parts.length == 2 ? new Locale(parts[0], parts[1]) : new Locale(language);
        return ResourceBundle.getBundle("language", locale);
    }

    public static String getValue(ResourceBundle bundle, String originKey) {
        if (StringUtils.isEmpty(originKey)) {
            return null;
        }
        if (bundle == null) {
            bundle = getBundle(DEFAULT_LANGUAGE);
        }

        if (bundle.containsKey(originKey)) {
            return bundle.getString(originKey);
        }

        Pattern pattern = Pattern.compile(IGNORE_WORD_REGEX);
        if (pattern.matcher(originKey).find()) {
            String destKey = originKey.replaceAll(IGNORE_WORD_REGEX, "");
            if (bundle.containsKey(destKey)) {
                return bundle.getString(destKey);
            }
        }
        return originKey;
    }

    public static void setLanguage(String language) {
        LANGUAGE.set(getBundle(language));
    }

    public static String getLanguage() {
        if (LANGUAGE.get() == null) {
            return DEFAULT_LANGUAGE;
        }
        Locale locale = LANGUAGE.get().getLocale();
        String county = locale.getCountry();
        String language = locale.getLanguage();
        return StringUtil.empty(county) ? language : (language + "_" + county);
    }

    public static String getValue(String key) {
        return getValue(LANGUAGE.get(), key);
    }

    public static List<String> getValues(String... keys) {
        List<String> data = new ArrayList<>();
        for (String key : keys) {
            data.add(getValue(LANGUAGE.get(), key));
        }
        return data;
    }

    public static List<String> getValueList(List<String> keys) {
        List<String> data = new ArrayList<>();
        for (String key : keys) {
            if (StringUtil.empty(getValue(LANGUAGE.get(), key))){
                continue;
            }
            data.add(getValue(LANGUAGE.get(), key));
        }
        return data;
    }

}


