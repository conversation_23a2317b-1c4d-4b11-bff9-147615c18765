package com.wosai.upay.transaction.constant;


import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.wosai.common.enums.WalletType;
import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.model.Payment;
import com.wosai.upay.transaction.util.DateTimeUtil;
import com.wosai.upay.transaction.util.LanguageUtil;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
public class CommonConstant {

    public static long MILLISECOND_OF_DAY = 1000 * 60 * 60 * 24;

    public static final long MILLISECOND_OF_HOUR = 1000 * 60 * 60;

    public static final String PLACE_HOLDER = "-";

    public static final long MILLISECOND_OF_SECOND = 1000;

    public final static String SOURCE_UPAY = "upay";

    public final static String TOKEN = "token";

    public final static String TRANSACTION_ID = "transactionId";

    /**
     * 集团账号 查询单条流水明细使用
     */
    public final static String MERCHANT_ID_FOR_GROUP_QUERY = "merchantIdForGroupQuery";

    public final static String TRANSACTION_SN = "transactionSn";

    public final static String STORE_ACCOUNT_FLAG = "storeAccountFlag";

    public final static String NEED_REFUND_INFO = "needRefundInfo";

    public static final String TIME_ZONE = "timeZone";

    public static final Integer DEFAULT_PAY_WAY = -1;

    public static final Integer BANK_CARD = 21;

    public static final String UPDATE_VERSION_L = "update_version_l";

    /**
     * excel 表单最大记录
     */
    public static long MAX_SHEET_LINE_NUMBER = 200000;

    public static long MAX_SHEET_LINE_NUMBER_2 = 100000;

    public static final String PATTERN = "yyyy-MM-dd";

    public static final String OUTSIDE_CALL = "out-********";

    public static final String TASK_LOG_ID = "taskLogId";

    public static final String BLOCK_TIME = "blockTime";

    public static final String TOTAL_ROWS = "totalRows";


    public static final String DEFAULT_PAY_WAY_STR = "DEFAULT";

    public static final String BIG_TASK_THRESHOLD_SIZE = "BIG_TASK_THRESHOLD_SIZE";

    public static final String TASK_WARN_BLOCK_SECONDS_STR = "TASK_WARN_BLOCK_SECONDS";

    public static final long TASK_WARN_BLOCK_SECONDS = 300;

    public static final String TASK_WARN_TIMEOUT_SECONDS_STR = "TASK_WARN_TIMEOUT_SECONDS";

    public static final long TASK_WARN_TIMEOUT_SECONDS = 1800;


    public static final long BIG_TASK_THRESHOLD_DEFAULT_SIZE = 500000L;

    public static final String DELAY_TIME = "DELAY_TIME";

    public static final String DELAY_QUERY_TIME = "DELAY_QUERY_TIME";

    public static final long DELAY_TIME_DEFAULT_VALUE = 0;

    public static final String SQB_DISCOUNT_ICON = "https://wosai-statics.oss-cn-hangzhou.aliyuncs.com/app/images/assets/<EMAIL>";

    public static final String OUTER_DISCOUNT_ICON = "https://wosai-statics.oss-cn-hangzhou.aliyuncs.com/app/images/assets/<EMAIL>";

    public static final String HB_FQ_ICON = "https://images.wosaimg.com/56/bcc7a19b8738af361841ddcc4e7ea70406dc32.png";

    public static final String HB_FQ_DISCOUNT_ICON = "https://images.wosaimg.com/13/9904a87f6732cbb0a230af3c8f21bb89d2059e.png";
    /**
     * 预付款图标TAccountRecord
     */
    public static final String DEPOSIT_ICON = "https://images.wosaimg.com/5e/2f96da44ca8b2bde28ca58e837b2d373712ef8.png";


    public static final String JIU_JIU_DISCOUNT_ICON = "https://wosai-statics.oss-cn-hangzhou.aliyuncs.com/app/images/assets/<EMAIL>";

    /**
     * 电宝宝 icon 链接
     */
    public static final String DIAN_BAO_BAO_ICON = "https://shouqianba.oss-cn-hangzhou.aliyuncs.com/4.x%E5%9B%BE%E6%A0%87/dbb%E8%B4%A6%E6%9C%AC%402x.png";

    /**
     * 劵包
     */
    public static final String COUPON_BUY_ICON = "https://shouqianba.oss-cn-hangzhou.aliyuncs.com/app-native/icons/%E5%88%B8%E5%8C%85.png";

    /**
     * 权益卡
     */
    public static final String MEMBER_CARD_BUY_ICON = "https://shouqianba.oss-cn-hangzhou.aliyuncs.com/app-native/icons/%E6%9D%83%E7%9B%8A%E5%8D%A1.png";

    /**
     * 阶段付
     */
    public static final String FITNESS_ICON = "https://images.wosaimg.com/28/7911a2df5ab65ca3bfd43af2c387c2f8343c00.png";
    /**
     * 默认收钱吧图标
     */
    public static final String DEFAULT_PAYWAY_ICON = "https://shouqianba.oss-cn-hangzhou.aliyuncs.com/4.x%E5%9B%BE%E6%A0%87/<EMAIL>";

    /**
     * 默认收钱吧灰图标
     */
    public static final String DEFAULT_PAYWAY_GRAY_ICON = "https://shouqianba.oss-cn-hangzhou.aliyuncs.com/4.x%E5%9B%BE%E6%A0%87/<EMAIL>";

    /**
     * 智慧门店优惠类型
     */
    public static final String ORIGIN_TYPE_MARKET_PROGRAM_DISCOUNT = "market_program_discount";

    /**
     * 红包优惠类型
     */
    public static final String ORIGIN_TYPE_REDPACK_DISCOUNT = "redpack_discount";

    public static final String APP_ID_TERMINAL_TYPE = "APP_ID_TERMINAL_TYPE";

    public static final String PAYWAY = "payWay";

    public static final String APPID = "appId";

    public static final String NAME_PAYWAY = "NAME_PAYWAY";

    public static final String RECORD_DETAIL_URL = "RECORD_DETAIL_URL";
    public static final String QUERY_ES_PROPORTION = "query_es_proportion";

    public static final String SWIPE_RECORD_DETAIL_URL = "swipe_record_detail_url";

    public static final String PRODUCT_FLAG_MAPPING = "product_flag_mapping";

    public static final String VENDOR_APP_CATEGORY_MAP = "vendor_app_category_map";

    public static final String STORE_PAY_CLEARING_MERCHANT_IDS = "store_pay_clearing_merchant_ids";

    public static final String KA_MERCHANT_SNS = "ka_merchant_sns"; //大客户侧存在机读对账单的商户

    public static final String OSS_CLIENT_CONF = "OSS_CLIENT_CONF";

    public static final String NAME_PAYWAY_VALUE = "{\"QQ\":6,\"UNIONPAY\":17,\"BAIFUBAO\":4,\"ALIPAY\":1,\"WEIXIN\":3,\"BESTPAY\":18,\"JD\":5,\"NFC\":7,\"ALIPAY_OPEN\":2,\"DEFAULT\":-1}";

    public static final String APP_ID_TERMINAL_TYPE_VALUE = "{\"2016111100000033\":\"APP\",\"2016090200000007\":\"传统POS\",\"2018031300000576\":\"智能POS|拉卡拉\",\"2016081800000003\":\"智能POS|定制版\",\"2017020600000069\":\"POS终端\",\"2016050300000001\":\"扫码王1\",\"2017110200000406\":\"派派小盒|增强版\",\"2018033100000614\":\"QR68|百富\",\"2018050400000669\":\"QM500|联迪\",\"2018070400000806\":\"QM800|联迪\",\"2018051400000698\":\"ME50C|新大陆\",\"2018051000000691\":\"C2|华智融\",\"2018050700000676\":\"SL51|波普安创\",\"2018051400000699\":\"Q50|升腾\",\"2018051400000700\":\"81G|云码智能\",\"2018051400000701\":\"QP33|新国都\",\"2018052200000725\":\"FR010|商米\",\"2017030600000083\":\"PC版插件\",\"2018041000000638\":\"安卓版插件\"}";

    public static final String DISPLAY_RATIO_CHARGE_NAME_KEY = "display_ratio_charge_name";

    public static final String CACHE_KEY_COLD_ORDER = "cold-order:";

    public static final String CACHE_KEY_CASH_DESK = "cash-desk:";

    public static final String CACHE_KEY_TRANSACTION = "cache_key_transaction:";


    public static final String CACHE_KEY_ORDER = "cache_key_order:";

    public static final int ROW_ACCESS_WINDOW_SIZE = 5000;

    public static final String DETAIL_SDF_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final String DAY_SDF_PATTERN_YYYYMMDD = "yyyyMMdd";

    public static final String DAY_SDF_PATTERN_YYYYMM = "yyyyMM";

    public static final String DAY_SDF_PATTERN = "yyyy-MM-dd";

    public static final String TIME_SDF_PATTERN = "HH:mm:ss";

    public static final String DETAIL_SDF_PATTERN1 = "MM/dd/yyyy HH:mm";

    public static final String DETAIL_SDF_PATTERN2 = "MM/dd/yyyy";
    public static final String DETAIL_QRCODEDOWNLOAD_PATTERN = "yyyyMMddHHmmss";

    public static final ThreadLocal<DateFormat> DETAIL_SDF = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat(DETAIL_SDF_PATTERN);
        }
    };

    public static final ThreadLocal<DateFormat> DAY_SDF_YYYYMMDDHHMMSS = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat(DETAIL_QRCODEDOWNLOAD_PATTERN);
        }
    };

    public static final ThreadLocal<DateFormat> DETAIL_SDF1 = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat(DETAIL_SDF_PATTERN1);
        }
    };

    public static final ThreadLocal<DateFormat> DETAIL_SDF2 = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat(DETAIL_SDF_PATTERN2);
        }
    };

    public static final ThreadLocal<DateFormat> DAY_SDF = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat(DAY_SDF_PATTERN);
        }
    };


    public static final ThreadLocal<DateFormat> DAY_SDF_YYYYMMDD = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat(DAY_SDF_PATTERN_YYYYMMDD);
        }
    };


    public static final ThreadLocal<DateFormat> TIME_SDF = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat(TIME_SDF_PATTERN);
        }
    };

    /**
     * 预授权冻结  预授权撤销 汇总不统计
     */

    public static final List<Integer> NOT_SUMMARY_TRANSACTION_TYPES = Lists.newArrayList(TransactionType.DEPOSIT_CANCEL.getCode(), TransactionType.DEPOSIT_FREEZE.getCode());


    public static final ThreadLocal<String> TIME_ZONE_TL = new ThreadLocal<String>() {
        @Override
        protected String initialValue() {
            return "";
        }
    };


    public static final String UPAY_QUERY_TYPE = "upayQueryType";
    public static final String IS_KA_STATEMENT = "isKAStatement"; //是否是KA对账单

    public static void initTimeZone(String timeZoneStr) {
        if (StringUtils.hasText(timeZoneStr)) {
            TIME_ZONE_TL.set(timeZoneStr);
        }

        TimeZone timeZone = DateTimeUtil.getTimeZone(timeZoneStr);
        CommonConstant.DAY_SDF.get().setTimeZone(timeZone);
        CommonConstant.TIME_SDF.get().setTimeZone(timeZone);
        CommonConstant.DAY_SDF_YYYYMMDD.get().setTimeZone(timeZone);
        CommonConstant.DETAIL_SDF.get().setTimeZone(timeZone);
    }


    public static final List<String> BANK_CARD_HEAD_NAME = Lists.newArrayList(LanguageUtil.PAY_TYPE
            , LanguageUtil.BATCH_BILL_NO
            , LanguageUtil.SYS_TRACE_NO
            , LanguageUtil.BANK_TYPE
            , LanguageUtil.LAKALA_MERC_ID
            , LanguageUtil.LAKALA_TERM_ID
            , LanguageUtil.TRADE_NO);


    public static final List<String> NOT_BANK_CARD_HEAD_NAME = Lists.newArrayList(LanguageUtil.MERCHANT_DISCOUNT
            , LanguageUtil.WOSAI_DISCOUNT
            , LanguageUtil.PAYMENT_TYPE_DISCOUNT
            , LanguageUtil.MERCHANT_DISCOUNT_PREPAID_MODE
            , LanguageUtil.MERCHANT_DISCOUNT_NON_PREPAID_MODE
            , LanguageUtil.THE_AMOUNT_CONSUMER_PAID);


    /**
     * 支付方式
     */
    public static final ImmutableMap<String, String> PAY_TYPE_MAP = ImmutableMap.<String, String>builder()
            .put("00", "借记卡")
            .put("01", "贷记卡")
            .put("91", "微信")
            .put("92", "支付宝")
            .put("93", "百度钱包")
            .put("94", "拉卡拉钱包")
            .put("04", "未知卡类型")
            .build();

    public static final String SUCCESS = "success";


    // 支付方式图标灰度图
    public static final ImmutableMap<String, String> APP_ID_MAP = ImmutableMap.<String, String>builder()
            .put("okSzXt", "wx0fd179d3b11b7b34") //收钱吧福利社
            .put("o2dNiu", "wx899c14a139b2ba82") //收钱吧北京 beijing
            .put("oGFfks", "wx72534f3638c59073") //收钱吧上海 shanghai
            .put("oLnPgv", "wxb0bd91aa640a954c") //收钱吧广州
            .put("o7kqVu", "wx59211250e712e3fd") //收钱吧深圳 shenzhen
            .put("orMrkv", "wx37135a21fb63f366") //收钱吧优选 youxuan
            .put("ohoqX0", "wx457b9713a0878d37") //收钱吧乐生活 yudu
            .put("oqFl_w", "wxe4ab1729629e9ade") //收钱吧蓉城 rongcheng
            .put("oc5Ms1", "wxf9114ca5d9b45bd5") //收钱吧趣生活 nanjing
            .put("oGeSW0", "wxd08e44e6e6b8a218") //收钱吧爱生活 changsha
            .put("oqXrmw", "wxc6ca2c84581752bb") //收钱吧惠生活 hangzhou
            .put("o_UWa0", "wx7d44dab5b915035c") //收钱吧酷生活 wuhan
            .put("ozXYvw", "wxddde1cfa166c856a") //吧哥优选 bageyouxuan
            .build();


    public static final String HBASE_THREAD_NAME = "hbase";

    public static final String SOLR_ZKHOST = "solr.zkHost";

    public static final String HBASE_ZKHOST = "hbase.zkHost";

    public static final String LINDORM_SOLR_ZKHOST = "lindorm.solr.zkHost";
    public static final String LINDORM_HBASE_ZKHOST = "lindorm.zkHost";
    public static final String LINDORM_USER_NAME = "lindorm.username";
    public static final String LINDORM_PASSWORD = "lindorm.password";

    public static final String BATCH_GET = "batchGet";

    public static final String PUT = "put";

    public static final int HBASE_PUT_RPC_TIMEOUT = 1000;

    public static final int CONNECTION_TIMEOUT_MILLIS = 3000;

    public static final String HBASE_TIMEOUT = "hbase_timeout";

    public static final String SOLR_TIMEOUT = "solr_timeout";

    public static final String KEY_EXPORT_FILE_URL = "statement_oss_url";

    public static final String STORE_IDS = "storeIds";

    public static final String STORE_NAME = "storeName";

    public static final String STORE_ID = "storeId";

    public static final String TERMINAL_SN = "terminalSn";

    public static final String MERCHANT_ID = "merchantId";

    public static final String TERMINAL_ID = "terminalId";

    public static final String CASH_DESK = "cashDesk";

    public static final String NAME = "name";

    public static final String TERMINALS = "terminals";

    public static final String GROUP_BYS = "groupBys";

    public static final String BUYER_NAME = "buyerName";

    public static final String CLIENT_SN = "clientSn";

    public static final String LOAD_BUYER = "loadBuyer";

    public static final String CTIME = "ctime";

    public static final String BUYER_ID = "buyerId";

    public static final String CUSTOMER_NAME = "customerName";

    public static final String DEFAULT_CUSTOMER_NAME = "defaultName";

    public static final String NICK_NAME = "nickname";

    public static final String AVATAR = "avatar";

    public static final String DATA = "data";

    public static final String BUYER_ICON = "buyerIcon";

    public static final String VENDOR_APP_APP_ID_APP = "2016111100000033";//终端类型是APP

    public static final String PAGE_NUM = "pageNum";

    public static final String ALI_PAY = "ALIPAY";

    public static final String ALI_PAY_OPEN = "ALIPAY_OPEN";

    public static final String ORDER_SN = "orderSn";

    public static final String TRACE_ID = "trace_id";

    public static final String SHARING_BOOKS = "sharing_books";

    public static final String NEED_FAIL_QUERY = "need_fail_query";

    public static final String FOREIGN_CARD_TRADE_STATUS = "foreign_card_trade_status"; //商户是否开通外卡交易

    public static final int TYPE_SHARING_FAIL_CLOSED = 3; // 分账失败已关闭类型，只在分账对账单中体现

    public static final int INDEX_SHARDS = 5; // binlog_tx 商户分片数
    public static final String ACCESS_CASH_DESK = "access_cash_desk";

    public static final List<String> WOSAI_MCH_FAVORABLE_PAYMENT_TYPES = Arrays.asList(Payment.TYPE_HONGBAO_WOSAI_MCH, Payment.TYPE_DISCOUNT_WOSAI_MCH);

    public static final List<Integer> FILTER_PROFIT_TYPES = Arrays.asList(WalletType.PROFIT_SHARING_DEDUCT_GROUP.getValue(),
            WalletType.PROFIT_SHARING_INCOME_GROUP.getValue()
    );
    public static final List<Integer> QUERY_WALLET_TYPES = Arrays.stream(WalletType.values())
            .filter(r -> !FILTER_PROFIT_TYPES.contains(r.getValue()))
            .map(walletType -> walletType.getValue())
            .collect(Collectors.toList());

    public static final String SHARING_RECEIVER_ID = "id"; //分账receiver_id
    public static final String SHARING_PROFIT_FLAG = "sharing_flag"; //分账标识
    public static final String SHARING_PROFIT_NAME = "sharing_name"; //分账名称
    public static final String SHARING_AMOUNT = "amount"; //分账金额

    //msp 收款流水账本数据权限	权限code
    public static final String MSP_TRANSACTION_PERMISSION_CODE = "F98PSF13VC";
    //msp 报表数据权限 权限code
    public static final String MSP_REPORT_PERMISSION_CODE = "9EA2J7AQ2X";
    //app 收款流水账本数据权限 权限code
    public static final String APP_TRANSACTION_PERMISSION_CODE = "H2PG9G55MA";
    //app 报表账本数据权限 权限code
    public static final String APP_REPORT_PERMISSION_CODE = "H49SN3DQPH";

    /**
     * 缓存订单或交易 <id, ctime> 对的 key 前缀
     */
    public static final String HBASE_ROWKEYS_PREFIX = "hbase:rowkeys:";

    /**
     * hash 的 field：用于 -> 如果查询到数据不存在时 也需要缓存
     */
    public static final String FIELD_EXIST = "exist";

    /**
     * 不存在数据
     */
    public static final int NOT_EXIST = -1;
}
