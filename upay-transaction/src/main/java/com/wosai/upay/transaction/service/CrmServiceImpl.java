package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2025/3/21.
 */
@AutoJsonRpcServiceImpl
public class CrmServiceImpl implements CrmService {

    @Autowired
    private TransactionServiceV2 transactionServiceV2;

    @Override
    public ListResult getTransactionList(PageInfo pageInfo, Map<String, Object> queryFilter) {
        return transactionServiceV2.getTransactionList(pageInfo, queryFilter);
    }
}
