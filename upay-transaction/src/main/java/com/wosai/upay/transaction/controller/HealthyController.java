package com.wosai.upay.transaction.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/11/22.
 * <p>
 * 注意: 不要、不要、千万不要删除，这个是作为接入bingo的必要条件
 */
@Controller
@RequestMapping("")
public class HealthyController {
    /**
     * 健康检查接口
     */
    @RequestMapping(value = "/check", method = {RequestMethod.GET, RequestMethod.HEAD})
    @ResponseBody
    public String healthy() {
        return "success"; //可以自定义逻辑来验证系统已经启动成功
    }






}
