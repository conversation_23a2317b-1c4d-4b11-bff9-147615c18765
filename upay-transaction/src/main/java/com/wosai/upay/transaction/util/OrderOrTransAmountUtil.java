package com.wosai.upay.transaction.util;


/**
 * <AUTHOR>
 */
public class OrderOrTransAmountUtil {
    public static long getMerchantFavorableAmount(long hongbaoWosaiMchAmount, long discountWosaiMchAmount) {
        return hongbaoWosaiMchAmount + discountWosaiMchAmount;
    }

    public static long getWosaiFavorableAmount(long originalAmount, long effectiveAmount, long mchFavorableAmount) {
        return originalAmount - effectiveAmount - mchFavorableAmount;
    }

    public static long getChannelMchTopUpFavorableAmount(long discountChannelMchTopUpAmount, long hongbaoChannelMchTopUpAmount) {
        return discountChannelMchTopUpAmount + hongbaoChannelMchTopUpAmount;
    }

    public static long getChannelMchFavorableAmount(long discountChannelMchAmount, long hongbaoChannelMchAmount) {
        return discountChannelMchAmount + hongbaoChannelMchAmount;
    }

    public static long getChannelAgentFavorableAmount(long hongbaoChannelAmount, long discountChannelAmount) {
        return hongbaoChannelAmount + discountChannelAmount;
    }

    public static long getActualReceiveAmount(long originalAmount, long merchantFavorableAmount, long channelMchFavorableAmount) {
        return originalAmount - merchantFavorableAmount - channelMchFavorableAmount;
    }

    public static long getClearingAmount(long actualReceiveAmount, long fee) {
        return actualReceiveAmount - fee;
    }


    /**
     * 收款通道优惠
     *
     * @param channelAgentFavorableAmount channelMchFavorableAmount
     *                                    channelMchTopUpFavorableAmount
     * @return
     */
    public static long getChannelFavorableAmount(long channelAgentFavorableAmount, long channelMchFavorableAmount, long channelMchTopUpFavorableAmount) {
        return channelAgentFavorableAmount + channelMchFavorableAmount + channelMchTopUpFavorableAmount;
    }
}
