package com.wosai.upay.transaction.service.mq;

import com.wosai.common.utils.Converter;
import com.wosai.data.dao.DaoConstants;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.model.Transaction;
import com.wosai.upay.transaction.service.mq.handler.OrderCacheHandler;
import com.wosai.upay.transaction.util.ApolloUtil;
import io.confluent.kafka.serializers.KafkaAvroDeserializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Component
public class TradeConsumer {

    @Value("${trade.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${trade.kafka.consumer.registry-url}")
    private String registryUrl;

    @Value("${trade.kafka.consumer.group-id}")
    private String groupId;

    @Value("${trade.kafka.consumer.topic}")
    private String topic;

    @Value("${trade.kafka.consumer.max-poll-records}")
    private Integer maxPollRecords;

    @Value("${mq.consumer.enabled}")
    private boolean consumerEnabled;

    @Resource
    private OrderCacheHandler orderCacheHandler;

    private static final ExecutorService POLL_MSG_EXECUTOR = Executors.newSingleThreadExecutor();

    @PostConstruct
    private void init() {
        if (consumerEnabled) {
            startConsume();
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                consumerEnabled = false;
            }));
        }
    }

    private KafkaConsumer<?, GenericRecord> buildKafkaConsumer() {
        Properties props = new Properties();
        props.put("bootstrap.servers", bootstrapServers);
        props.put("schema.registry.url", registryUrl);
        props.put("group.id", groupId);
        props.put("enable.auto.commit", "true");
        props.put("max.poll.records", maxPollRecords);
        props.put("key.deserializer", StringDeserializer.class);
        props.put("value.deserializer", KafkaAvroDeserializer.class);
//        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        KafkaConsumer<?, GenericRecord> consumer = new KafkaConsumer<>(props);
        consumer.subscribe(Collections.singletonList(topic));
        return consumer;
    }

    private void startConsume() {
        KafkaConsumer<?, GenericRecord> consumer = buildKafkaConsumer();
        POLL_MSG_EXECUTOR.execute(() -> {
            while (consumerEnabled) {
                try {
                    ConsumerRecords<?, GenericRecord> consumerRecords = consumer.poll(2000L);
                    if (consumerRecords == null || consumerRecords.isEmpty() || !ApolloUtil.isGatewayNotExistRestCacheEnable()) {
                        continue;
                    }

                    List<Map> transactions = new ArrayList<>();
                    for (ConsumerRecord<?, GenericRecord> record : consumerRecords) {
                        GenericRecord transaction = record.value();
                        if (null == transaction) {
                            continue;
                        }
                        String orderId = Converter.getAsString(record.value().get(Transaction.ORDER_ID));
                        long ctime = Converter.getAsLong(record.value().get(DaoConstants.CTIME));
                        String tsn = Converter.getAsString(record.value().get(Transaction.TSN));
                        String merchantId = Converter.getAsString(record.value().get(Transaction.MERCHANT_ID));
                        String clientTsn = Converter.getAsString(record.value().get(Transaction.CLIENT_TSN));
                        int type = Converter.getAsIntValue(record.value().get(Transaction.TYPE));
                        int subPayway = Converter.getAsIntValue(record.value().get(Transaction.SUB_PAYWAY));

                        transactions.add(MapUtil.hashMap(Transaction.ORDER_ID, orderId, DaoConstants.CTIME, ctime,
                                Transaction.MERCHANT_ID, merchantId, Transaction.CLIENT_TSN, clientTsn,
                                Transaction.TYPE, type, Transaction.SUB_PAYWAY, subPayway,
                                Transaction.TSN, tsn
                                ));
                    }
                    orderCacheHandler.handle(transactions);
                } catch (Exception e) {
                    log.error("kafka consumer error: {}", e.getMessage(), e);
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e1) {
                    }
                }
            }
        });
    }
}