package com.wosai.upay.transaction.util;

import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.model.Transaction;

import com.wosai.upay.transaction.service.model.OrderExportSummaryInfo;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 */
public class StatementOrderUtil {

    private static final String SHEET_NAME = "订单数据";

    /**
     * 导出订单字段
     */
    public final static LinkedHashMap<String, String> ORDER_EXPORT_COLUMN_MAP = new LinkedHashMap<String, String>() {
        {
            put("day", "日期");
            put("time", "时间");
            put("sn", "商户订单号");
            put("status", "订单状态");
            put("payway", "收款通道");
            put("sub_payway", "交易模式");
            put("original_total", "订单金额");
            put("net_original", "实收金额");
            put("liquidation_next_day", "是否试用");
            put("fee_rate", "扣率");
            put("fee", "手续费");
            put("merchant_name", "商户名称");
            put("merchant_sn", "商户号");
            put("store_name", "门店名称");
            put("store_sn", "门店号");
            put("terminal_name", "终端名称");
            put("terminal_sn", "终端号");
            put("terminal_type", "终端类型");
            put("terminal_device_fingerprint", "设备号");
            put("buyer_login", "付款账户");
            put("trade_no", "收款通道订单号");
            put("operator", "操作员");
            put("operator_name", "收银员");
            put("client_sn", "商户内部订单号");
            put("mch_discount_origin_type", "商户优惠类型");
        }

    };


    /**
     * 银行卡订单字段
     */
    public final static LinkedHashMap<String, String> BANK_CARD_ORDER_EXPORT_COLUMN_MAP = new LinkedHashMap<String, String>() {
        {
            put("day", "日期");
            put("time", "时间");
            put("channel_finish_time", "刷卡完成时间");
            put("sn", "商户订单号");
            put("status", "订单状态");
            put("payway", "收款通道");
            put("original_total", "订单金额");
            put("net_original", "实收金额");
            put("fee", "手续费");
            put("liquidation_next_day", "是否试用");
            put("merchant_name", "商户名称");
            put("merchant_sn", "商户号");
            put("store_name", "门店名称");
            put("store_sn", "门店号");
            put("terminal_name", "终端名称");
            put("terminal_sn", "终端号");
            put("terminal_type", "终端类型");
            put("terminal_device_fingerprint", "设备号");
            put("buyer_login", "付款账户");
            put("trade_no", "收款通道订单号");
            put("client_sn", "商户内部订单号");
            put("pay_type", "支付方式");
            put("batchbillno", "批次号");
            put("systraceno", "凭证号");
            put("bank_type", "付款银行");
            put("lakala_merc_id", "拉卡拉商户号");
            put("lakala_term_id", "拉卡拉终端号");
            put("mch_discount_origin_type", "收钱吧商户优惠类型");
        }

    };

    private static double buildOrderExcelMap(Map<String, Object> orderInfo) {
        double feeRate = 0.0;
        Long cTimeLong = (Long) orderInfo.get(DaoConstants.CTIME);
        Long channelFinishTime = (Long) orderInfo.get(Transaction.CHANNEL_FINISH_TIME);
        Date cTime = Objects.isNull(cTimeLong) ? null : new Date(cTimeLong);
        String feeRateString = BeanUtil.getPropString(orderInfo, Order.FEE_RATE, "0.0");
        if (StringUtils.hasText(feeRateString)) {
            feeRate = CommonUtil.formatMoney(feeRateString, 3);
        }
        //手续费为负数
        long tradeFee = BeanUtil.getPropLong(orderInfo, Order.FEE) * -1;
        Boolean liquidationNextDay = Boolean.parseBoolean(BeanUtil.getPropString(orderInfo, "liquidation_next_day"));
        double netOriginal = CommonUtil.formatMoney(BeanUtil.getPropLong(orderInfo, "actual_receive_amount", 0) / 100.0, 2);
        orderInfo.put("day", cTime != null ? CommonConstant.DAY_SDF.get().format(cTime) : null);
        orderInfo.put("channel_finish_time", channelFinishTime != null ? CommonConstant.DETAIL_SDF.get().format(channelFinishTime) : null);
        orderInfo.put("time", cTime != null ? CommonConstant.TIME_SDF.get().format(cTime) : null);
        orderInfo.put("payway", OrderUtil.getPaywayDesc(BeanUtil.getPropString(orderInfo, "payway")));
        orderInfo.put("sub_payway", OrderUtil.getSubPaywayDesc(BeanUtil.getPropString(orderInfo, "sub_payway")));
        orderInfo.put("status", OrderUtil.getOrderStatusDesc(BeanUtil.getPropString(orderInfo, "status")));
        orderInfo.put("terminal_type", OrderUtil.getTerminallTypeDesc(BeanUtil.getPropString(orderInfo, "terminal_type")));
        orderInfo.put("original_total", CommonUtil.formatMoney(BeanUtil.getPropLong(orderInfo, "original_total", 0) / 100.0, 2));
        orderInfo.put("net_original", netOriginal);
        orderInfo.put("liquidation_next_day", liquidationNextDay ? "是" : "否");
        //费率
        orderInfo.put("fee_rate", feeRate);
        //手续费
        orderInfo.put("fee", CommonUtil.formatMoney(tradeFee / 100.0, 2));
        return netOriginal;
    }

    public static SheetUtil initSheetAppendHeader() {
        SXSSFWorkbook workbook = new SXSSFWorkbook(CommonConstant.ROW_ACCESS_WINDOW_SIZE);
        SXSSFSheet sheet = (SXSSFSheet) workbook.createSheet(SHEET_NAME);
        SheetUtil sheetUtil = new SheetUtil(sheet, workbook);
        List values = new ArrayList();
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            values.addAll(BANK_CARD_ORDER_EXPORT_COLUMN_MAP.values());
        } else {
            values.addAll(ORDER_EXPORT_COLUMN_MAP.values());
        }
        StatementCommonUtil.appendLine(sheetUtil.getSheet(), values);
        return sheetUtil;

    }

    public static void appendRowsAndRecordSummary(List<Map<String, Object>> orderInfos, SheetUtil sheetUtil, OrderExportSummaryInfo summaryInfo) {
        List values;
        Set<String> columns = new LinkedHashSet<>();
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            columns.addAll(BANK_CARD_ORDER_EXPORT_COLUMN_MAP.keySet());
        } else {
            columns.addAll(ORDER_EXPORT_COLUMN_MAP.keySet());
        }

        for (Map orderInfo : orderInfos) {
            values = new ArrayList(columns.size());
            double amount = buildOrderExcelMap(orderInfo);
            summaryInfo.recordAmountByFlag(amount, MapUtil.getString(orderInfo, Transaction.PRODUCT_FLAG));
            for (String column : columns) {
                Object value = orderInfo.get(column);
                values.add(Objects.isNull(value) ? "" : value);
            }
            sheetUtil.appendRow(values);
        }
    }

    /**
     * 计算订单的汇总数据
     *
     * @param orderInfos
     * @param summary
     * @return
     */
    public static Map updateOrderSummaryInfo(List<Map<String, Object>> orderInfos, Map summary) {
        //交易总金额
        double originalAmount = CommonUtil.formatMoney(BeanUtil.getProperty(summary, Order.ORIGINAL_TOTAL), 2);
        //商户优惠金额
        double merchantDiscountAmount = CommonUtil.formatMoney(BeanUtil.getProperty(summary, Order.MCH_FAVORABLE_AMOUNT), 2);
        //实收金额
        double actualReceiveAmount = CommonUtil.formatMoney(BeanUtil.getProperty(summary, Order.ACTUAL_RECEIVE_AMOUNT), 2);
        //手续费
        double fee = CommonUtil.formatMoney(BeanUtil.getProperty(summary, Order.FEE), 2);
        //结算金额
        double clearingAmount = CommonUtil.formatMoney(BeanUtil.getProperty(summary, Order.CLEARING_AMOUNT), 2);
        //分账金额
        double sharingAmount = CommonUtil.formatMoney(BeanUtil.getProperty(summary, Transaction.SHARING_AMOUNT), 2);

        for (Map orderInfo : orderInfos) {
            //交易总金额
            originalAmount += CommonUtil.formatMoney(BeanUtil.getPropLong(orderInfo, Order.ORIGINAL_TOTAL) / 100.0, 2);
            //商户优惠金额
            merchantDiscountAmount += CommonUtil.formatMoney(BeanUtil.getPropLong(orderInfo, Order.MCH_FAVORABLE_AMOUNT) / 100.0, 2);
            //实收金额
            actualReceiveAmount += CommonUtil.formatMoney(BeanUtil.getPropLong(orderInfo, Order.ACTUAL_RECEIVE_AMOUNT, 0) / 100.0, 2);
            //手续费
            fee += CommonUtil.formatMoney(BeanUtil.getPropLong(orderInfo, Order.FEE, 0) / 100.0, 2);
            //结算金额
            clearingAmount += CommonUtil.formatMoney(BeanUtil.getPropLong(orderInfo, Order.CLEARING_AMOUNT, 0) / 100.0, 2);
            //分账金额
            sharingAmount += CommonUtil.formatMoney(BeanUtil.getPropLong(orderInfo, Transaction.SHARING_AMOUNT) / 100.0, 2);
        }

        summary = CollectionUtil.hashMap(
                Order.ORIGINAL_TOTAL, originalAmount,
                Order.MCH_FAVORABLE_AMOUNT, merchantDiscountAmount,
                Order.ACTUAL_RECEIVE_AMOUNT, actualReceiveAmount,
                Order.FEE, fee,
                Transaction.SHARING_AMOUNT, sharingAmount,
                Order.CLEARING_AMOUNT, clearingAmount
        );

        return summary;
    }


    public static void appendSummaryRow(List<Map<String, Object>> orderInfos, SheetUtil sheetUtil, OrderExportSummaryInfo summaryInfo) {
        appendRowsAndRecordSummary(orderInfos, sheetUtil, summaryInfo);
        String line = "总交易成功金额为：" + formatMoneyToString(summaryInfo.getTotalAmount(), 2) + ", 总交易成功笔数为: " + summaryInfo.getTotalCount();
        if (!(DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE)) {
            line += "，储值卡充值交易金额为：" + formatMoneyToString(summaryInfo.getStoreInAmount(), 2) + ", 储值卡充值交易笔数为: " + summaryInfo.getStoreInCount();
        }
        sheetUtil.appendRow(Arrays.asList(line));
        sheetUtil.mergeCell(sheetUtil.getCurrentRow(), 0, sheetUtil.getCurrentRow(), 8);
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            sheetUtil.appendRow(Arrays.asList("说明：1.总实收金额为：付款金额+预授权完成金额-退款金额-预授权完成撤销；2.预授权和预授权撤销的金额不计算在内"));
            sheetUtil.appendRow(Arrays.asList("说明：银行卡交易隔日根据拉卡拉对账单进行勾兑，每日15:00前更新前一天交易的手续费和结算金额"));
        } else {
            sheetUtil.appendRow(Arrays.asList("说明：1.总交易成功金额为：收款金额+预授权完成金额-退款金额，且都为成功状态；2.储值卡充值交易金额为：充值金额-充值退款金额；3.预授权和预授权撤销的金额不计算在内"));
        }
        sheetUtil.mergeCell(sheetUtil.getCurrentRow(), 0, sheetUtil.getCurrentRow(), 8);
    }


    /**
     * 金额转为字符串
     *
     * @param money
     * @param scaleSize
     * @return
     */
    public static String formatMoneyToString(Object money, int scaleSize) {
        if (money == null) {
            return "";
        } else {
            return new BigDecimal(money.toString()).setScale(scaleSize, BigDecimal.ROUND_HALF_UP).toString();
        }
    }

}
