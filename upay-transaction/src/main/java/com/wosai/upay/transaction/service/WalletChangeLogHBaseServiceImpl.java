package com.wosai.upay.transaction.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.transaction.model.ExportWalletChangeLogVO;
import com.wosai.upay.transaction.model.param.WalletChangeLogParam;
import com.wosai.upay.transaction.service.dao.WalletChangeLogHBaseDao;
import com.wosai.upay.transaction.service.model.po.WalletChangeLogPo;
import com.wosai.upay.transaction.service.model.query.WalletChangeLogQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

@AutoJsonRpcServiceImpl
public class WalletChangeLogHBaseServiceImpl implements WalletChangeLogHBaseService {

    public static final Logger logger = LoggerFactory.getLogger(WalletChangeLogHBaseServiceImpl.class);

    @Autowired
    private WalletChangeLogHBaseDao walletChangeLogHBaseDao;

    @Override
    public List<ExportWalletChangeLogVO> getWalletChangeLogList(WalletChangeLogParam walletChangeLogParam) {

        WalletChangeLogQuery query = new WalletChangeLogQuery()
                .setMerchantId(walletChangeLogParam.getMerchantId())
                .setStartTime(walletChangeLogParam.getStartTime())
                .setEndTime(walletChangeLogParam.getEndTime())
                .setLimit(walletChangeLogParam.getLimit());
        int[] orders = walletChangeLogParam.getOrders();
        WalletChangeLogQuery.OrderBy[] orderBys = new WalletChangeLogQuery.OrderBy[orders.length];
        for (int i = 0; i < orders.length; i++) {
            if (orders[i] == 0) {
                orderBys[i] = WalletChangeLogQuery.OrderBy.CTIME_DESC;
            } else {
                orderBys[i] = WalletChangeLogQuery.OrderBy.CTIME_ASC;
            }
        }
        query.setOrderBys(orderBys);
        List<WalletChangeLogPo> walletChangeLogPoList = walletChangeLogHBaseDao.queryForList(query);

        return walletChangeLogPoList.stream().map(walletChangeLogPo -> {
            return ExportWalletChangeLogVO.builder().id(walletChangeLogPo.getId())
                    .type(walletChangeLogPo.getType())
                    .balance(walletChangeLogPo.getBalance())
                    .amount(walletChangeLogPo.getAmount())
                    .sign(walletChangeLogPo.getSign())
                    .remark(walletChangeLogPo.getRemark())
                    .detail(walletChangeLogPo.getDetail())
                    .merchantId(walletChangeLogPo.getMerchantId())
                    .actionId(walletChangeLogPo.getActionId())
                    .ctime(walletChangeLogPo.getCtime())
                    .mtime(walletChangeLogPo.getMtime())
                    .version(walletChangeLogPo.getVersion())
                    .name(walletChangeLogPo.getName()).build();

        }).collect(Collectors.toList());
    }

    @Override
    public long getWalletChangeLogCount(WalletChangeLogParam walletChangeLogParam) {
        WalletChangeLogQuery query = new WalletChangeLogQuery()
                .setMerchantId(walletChangeLogParam.getMerchantId())
                .setTypes(walletChangeLogParam.getTypes())
                .setStartTime(walletChangeLogParam.getStartTime())
                .setEndTime(walletChangeLogParam.getEndTime());
        return walletChangeLogHBaseDao.count(query);
    }
}
