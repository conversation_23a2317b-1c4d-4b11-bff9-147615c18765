package com.wosai.upay.transaction.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.concurrent.*;

public class ExecutorServiceSupport {

    public static final Logger logger = LoggerFactory.getLogger(ExecutorServiceSupport.class);

    private static final int CORE_POOL_SIZE = 300;

    private static final int MAXIMUM_POOL_SIZE = 500;

    private static final BlockingQueue<Runnable> queue =  new ArrayBlockingQueue<>(1000);;

    private static ThreadPoolExecutor defaultExecutor;

    private static final ExecutorServiceSupport support = new ExecutorServiceSupport();


    private ExecutorServiceSupport() {
        defaultExecutor = new ThreadPoolExecutor(CORE_POOL_SIZE, MAXIMUM_POOL_SIZE, 1000L, TimeUnit.MILLISECONDS, this.queue, new NamedThreadFactory("default-Executor"));
        defaultExecutor.prestartAllCoreThreads();
       // this.registerStatisticCallback();
        this.addShutdownHook();
    }

    public static ExecutorService getDefaultExecutorService() {
        return defaultExecutor;
    }


    private void registerStatisticCallback() {
        StatisUtil.registerStatisticCallback(() -> {
            
        });
    }

    private void addShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread() {
            public void run() {
                ExecutorServiceSupport.this.shutdown();
            }
        });
    }

    public void shutdown() {
        logger.info("JVM正在进行关闭");
        logger.info("defaultExecutor线程池：{}", new Object[]{defaultExecutor.toString()});

        try {
            defaultExecutor.shutdown();
            defaultExecutor.awaitTermination(6000L, TimeUnit.MILLISECONDS);
        } catch (InterruptedException ex) {
            logger.error("关闭线程池时出现异常", ex);
        }

    }

}
