package com.wosai.upay.transaction.service.service;

import com.wosai.upay.transaction.service.model.query.TransactionQuery;
import com.wosai.upay.transaction.service.model.vo.TransactionDaySumV;
import com.wosai.upay.transaction.service.model.vo.TransactionSumV;
import com.wosai.upay.transaction.service.model.vo.TransactionVo;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface IAccountBookBaseService {

    /**
     * 交易筛选
     * @param query
     * @return
     */
    List<TransactionVo> queryList(TransactionQuery query);

    /**
     * 天分组汇总
     * @param query
     * @param days
     * @return
     */
    Map<String, TransactionDaySumV> summaryByDay(TransactionQuery query,Set<String> days );

    /**
     * 汇总
     * @param query
     * @return
     */
    List<TransactionSumV> summary(TransactionQuery query);

    /**
     * 查询一条交易
     * @param query
     * @return
     */
    TransactionVo queryObj(TransactionQuery query);

    Map<String, Object> queryOneByTid(@NotNull String merchantId, @NotNull Long ctime, @NotNull String tid, Boolean isSimple);

    Map<String, Object> queryOneByTid(@NotNull String merchantId, @NotNull Long ctime, @NotNull String tid);

    Map<String, Object> queryOneByOid(@NotNull String merchantId, @NotNull Long ctime, @NotNull String tid, Boolean isSimple);

}
