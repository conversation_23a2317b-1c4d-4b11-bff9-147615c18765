package com.wosai.upay.transaction.util;

import avro.shaded.com.google.common.collect.Maps;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wosai.data.bean.BeanUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.common.util.SpringContextHolder;
import com.wosai.upay.core.service.BusinssCommonService;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.StatementObjectConfigConstant;
import com.wosai.upay.transaction.enums.UpayQueryType;
import com.wosai.upay.transaction.export.base.RunContext;
import com.wosai.upay.transaction.helper.DBSelectContext;
import com.wosai.upay.transaction.model.StatementObjectConfig;
import com.wosai.upay.transaction.model.StatementSummary;

import com.wosai.upay.transaction.model.StatementTaskLog;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.wosai.upay.transaction.constant.CommonConstant.BANK_CARD;
import static com.wosai.upay.transaction.util.LanguageUtil.*;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unchecked")
public class StatementGroupSummaryUtil {
    /**
     * 初始化汇总数据
     *
     * @param summary
     * @param key
     * @return
     */
    private static Map getSummaryInitialData(Map summary, String key) {
        Map initialData = (Map) BeanUtil.getProperty(summary, key);
        if (initialData == null) {
            initialData = new HashMap();
        }
        return initialData;
    }

    /**
     * 生成对账单汇总 excel 文件内容
     *
     * @param context
     * @param summarySheet
     * @param summary
     * @return
     */
    public static void buildUpayStatementSummary(Map context, SXSSFSheet summarySheet, Map summary, Map sheetParams) {
        //保存业务对象名称等基本信息
        Map merchantBasicInfoMap = getSummaryInitialData(summary, StatementSummary.MERCHANT_BASIC_INFO_MAP);
        Map storeBasicInfoMap = getSummaryInitialData(summary, StatementSummary.STORE_BASIC_INFO_MAP);
        Map terminalBasicInfoMap = getSummaryInitialData(summary, StatementSummary.TERMINAL_BASIC_INFO_MAP);
        Map operatorBasicInfoMap = getSummaryInitialData(summary, StatementSummary.OPERATOR_BASIC_INFO_MAP);

        //保存业务对象上下级关联关系
        Map<String, List> merchantSnStoresMap = getSummaryInitialData(summary, StatementSummary.MERCHANT_SN_STORES_MAP);
        Map<String, List> storeSnTerminalsMap = getSummaryInitialData(summary, StatementSummary.STORE_SN_TERMINALS_MAP);
        Map<String, List> terminalSnOperatorsMap = getSummaryInitialData(summary, StatementSummary.TERMINAL_SN_OPERATORS_MAP);

        //保存业务对象汇总数据
        Map paywaySummaryMap = getSummaryInitialData(summary, StatementSummary.PAYWAY_SUMMARY_MAP);
        Map<String, Object> merchantSummaryMap = getSummaryInitialData(summary, StatementSummary.MERCHANT_SUMMARY_MAP);
        Map<String, Object> storeSummaryMap = getSummaryInitialData(summary, StatementSummary.STORE_SUMMARY_MAP);
        Map terminalSummaryMap = getSummaryInitialData(summary, StatementSummary.TERMINAL_SUMMARY_MAP);
        Map operatorSummaryMap = getSummaryInitialData(summary, StatementSummary.OPERATOR_SUMMARY_MAP);
        Map unionSummaryMap = StatementTransactionSummaryUtil.getSummaryInitialData(summary, StatementSummary.UNION_SUMMARY_MAP);

        appendSummaryTitle(context, summarySheet);

        int sheetType = BeanUtil.getPropInt(sheetParams, StatementObjectConfig.SHEET_TYPE);
        List<String> croList = (List) BeanUtil.getProperty(sheetParams, StatementObjectConfig.CRO);
        //是否是KA对账单
        boolean isKAStatement = RunContext.currentContext().getIsKAStatement();
        //如果是ka账单，那么不需要导出技术服务费和分账金额列； 反之不需要导出分账金额（含技术服务费） 列
        if (isKAStatement) {
            croList.removeAll(Lists.newArrayList(StatementObjectConfig.TRADE_SERVICE_SHARING_AMOUNT, StatementObjectConfig.MERCHANT_SHARING_AMOUNT, StatementObjectConfig.DEAL_TO_ACCOUNT_AMOUNT));
        } else {
            croList.remove(StatementObjectConfig.SHARING_AMOUNT);
        }
        List<Integer> paywayList = (List) BeanUtil.getProperty(sheetParams, StatementObjectConfig.PAYWAY);
        int terminalType = BeanUtil.getPropInt(sheetParams, StatementObjectConfig.TERMINAL_TYPE);
        Map<String, Object> sortMerchantSnStoresMap = Maps.newLinkedHashMap();
        merchantSnStoresMap.entrySet().stream().sorted((v1, v2) -> StringUtils.compare(v1.getKey(), v2.getKey())).forEach(v -> sortMerchantSnStoresMap.put(v.getKey(), v.getValue()));

        //忽略关闭、禁用状态且数据为0的商户、门店
        BusinssCommonService businssCommonService = SpringContextHolder.getBean(BusinssCommonService.class);
        List<String> removeSns = StatementCommonUtil.removeUndisplayMerchant(merchantSummaryMap, businssCommonService);
        removeSns.stream().forEach(k -> {
            merchantSummaryMap.remove(k);
            sortMerchantSnStoresMap.remove(k);
        });
        removeSns = StatementCommonUtil.removeUndisplayStore(storeSummaryMap, businssCommonService);
        removeSns.forEach(storeSummaryMap::remove);

        if (sheetType == StatementObjectConfigConstant.CONCISE_TYPE) {
            //简洁版
            appendMerchantAloneSummarySheet(summarySheet, merchantSummaryMap, sortMerchantSnStoresMap, merchantBasicInfoMap, croList);
            return;
        }
        if (sheetType == StatementObjectConfigConstant.DETAIL_TYPE) {
            //详细版
            appendMerchantAloneSummary(summarySheet, merchantSummaryMap, sortMerchantSnStoresMap, merchantBasicInfoMap, isKAStatement);
            appendPaywaySummary(summarySheet, paywaySummaryMap, isKAStatement);
        }
        if (sheetType == StatementObjectConfigConstant.CUSTOM_TYPE) {
            //自定义版
            appendMerchantAloneSummarySheet(summarySheet, merchantSummaryMap, sortMerchantSnStoresMap, merchantBasicInfoMap, croList);
            if ((paywayList != null && paywayList.size() > 0)) {
                appendPaywaySummarySheet(summarySheet, paywaySummaryMap, croList);
            }
        }

        if (terminalType == StatementObjectConfigConstant.NO_STORE_TERMINAL_TYPE) {
            //无门店终端汇总
            return;
        }
        StatementCommonUtil.appendLine(summarySheet, getValues(STORE_TERMINAL_TRANSACTION_SUMMARY_LIST));
        List columnDesc = new LinkedList();

        columnDesc.addAll(getValueList(Lists.newArrayList(MERCHANT_NO, MERCHANT_NAME, STORE_NO, MERCHANT_STORE_NO, STORE_NAME, TERMINAL_NO, MERCHANT_TERMINAL_NO,
                TERMINAL_NAME, TERMINAL_TYPE, DEVICE_ID, OPERATOR, CASHIER)));
        columnDesc.addAll(StatementSummaryStyleUtil.getGroupTerminalColumnDescByTaskStyle());
        StatementSummaryStyleUtil.resetColumns(columnDesc, isKAStatement);
        StatementCommonUtil.appendLine(summarySheet, columnDesc);

        Iterator<String> merchantSnIterator = sortMerchantSnStoresMap.keySet().iterator();

        while (merchantSnIterator.hasNext()) {
            //?？ 判断行数是否超出范围
            String merchantSn = merchantSnIterator.next();
            appendMerchantSummary(summarySheet, merchantBasicInfoMap, merchantSummaryMap, merchantSn, isKAStatement);
            ArrayList<String> storeSnList = (ArrayList<String>) BeanUtil.getProperty(sortMerchantSnStoresMap, merchantSn);
            Collections.sort(storeSnList);
            Iterator<String> storeSnIterator = storeSnList.iterator();
            while (storeSnIterator.hasNext()) {
                String storeSn = storeSnIterator.next();
                if (MapUtils.isEmpty(MapUtil.getMap(storeSummaryMap, storeSn))){
                    continue;
                }
                appendStoreSummary(summarySheet, storeBasicInfoMap, storeSummaryMap, storeSn, isKAStatement);
                ArrayList<String> terminalSnList = (ArrayList<String>) BeanUtil.getProperty(storeSnTerminalsMap, storeSn);

                Collections.sort(terminalSnList);
                Iterator<String> terminalSnSnIterator = terminalSnList.iterator();
                while (terminalSnSnIterator.hasNext()) {
                    String terminalSn = terminalSnSnIterator.next();

                    appendTerminalSummary(summarySheet, terminalBasicInfoMap, terminalSummaryMap, terminalSn, isKAStatement);

                    ArrayList<String> operatorList = (ArrayList<String>) BeanUtil.getProperty(terminalSnOperatorsMap, terminalSn);
                    Collections.sort(operatorList, new StatementTransactionSummaryUtil.ByLength());
                    Iterator<String> iterator = operatorList.iterator();
                    while (iterator.hasNext()) {
                        String operatorSn = iterator.next();
                        appendOperatorSummary(summarySheet, operatorBasicInfoMap, operatorSummaryMap, operatorSn, isKAStatement);
                    }
                }
            }

        }

        appendUnionSummary(summarySheet, unionSummaryMap, croList, paywayList, Lists.newArrayList(sortMerchantSnStoresMap.keySet()),
                merchantBasicInfoMap, sortMerchantSnStoresMap, storeBasicInfoMap);
    }

    private static void appendOperatorSummary(SXSSFSheet summarySheet, Map operatorBasicInfoMap, Map operatorSummaryMap, String operatorSn, boolean isKAStatement) {
        Map operatorSummary = (Map) operatorSummaryMap.get(operatorSn);
        String[] operators = operatorSn.split("@_@");
        int length = operators.length;
        if (length > 0) {
            operatorSn = operators[length - 1];
        }

        Map<String, Object> target = (Map<String, Object>) operatorBasicInfoMap.get(operatorSn);
        List<String> basicValues = Lists.newArrayList(
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                operatorSn,
                BeanUtil.getPropString(operatorBasicInfoMap.get(operatorSn), "operator_name"));
        StatementSummaryStyleUtil.appendByStyle(summarySheet, operatorSummary, target, basicValues, true, true, isKAStatement);

    }

    private static void appendTerminalSummary(SXSSFSheet summarySheet, Map terminalBasicInfoMap, Map terminalSummaryMap, String terminalSn, boolean isKAStatement) {
        Map terminalSummary = (Map) terminalSummaryMap.get(terminalSn);
        Map<String, Object> target = (Map<String, Object>) terminalSummaryMap.get(terminalSn);
        List<String> basicValues = Lists.newArrayList(
                "",
                "",
                "",
                "",
                "",
                terminalSn,
                BeanUtil.getPropString(terminalBasicInfoMap.get(terminalSn), "terminal_client_sn"),
                BeanUtil.getPropString(terminalBasicInfoMap.get(terminalSn), "terminal_name"),
                BeanUtil.getPropString(terminalBasicInfoMap.get(terminalSn), "terminal_type"),
                BeanUtil.getPropString(terminalBasicInfoMap.get(terminalSn), "terminal_device_fingerprint"),
                "",
                "");
        StatementSummaryStyleUtil.appendByStyle(summarySheet, terminalSummary, target, basicValues, true, true, isKAStatement);

    }

    private static void appendStoreSummary(SXSSFSheet summarySheet, Map storeBasicInfoMap, Map storeSummaryMap, String storeSn, boolean isKAStatement) {
        Map storeSummary = (Map) storeSummaryMap.get(storeSn);
        Map<String, Object> target = (Map<String, Object>) storeBasicInfoMap.get(storeSn);
        List<String> basicValues = Lists.newArrayList("",
                "",
                storeSn,
                BeanUtil.getPropString(storeBasicInfoMap.get(storeSn), "store_client_sn"),
                BeanUtil.getPropString(storeBasicInfoMap.get(storeSn), "store_name"),
                "",
                "",
                "",
                "",
                "",
                "",
                "");
        StatementSummaryStyleUtil.appendByStyle(summarySheet, storeSummary, target, basicValues, true, true, isKAStatement);
    }

    private static void appendMerchantSummary(SXSSFSheet summarySheet, Map merchantBasicInfoMap, Map merchantSummaryMap, String merchantSn, boolean isKAStatement) {
        Map merchantSummary = (Map) merchantSummaryMap.get(merchantSn);
        Map<String, Object> target = (Map<String, Object>) merchantBasicInfoMap.get(merchantSn);
        List<String> basicValues = Lists.newArrayList(
                merchantSn,
                BeanUtil.getPropString(target, ConstantUtil.KEY_MERCHANT_NAME),
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "");
        StatementSummaryStyleUtil.appendByStyle(summarySheet, merchantSummary, target, basicValues, true, true, isKAStatement);
    }

    private static void appendMerchantAloneSummarySheet(SXSSFSheet summarySheet, Map merchantSummaryMap, Map merchantSnStoresMap, Map merchantBasicInfoMap, List<String> croList) {

        StatementCommonUtil.appendLine(summarySheet, getValues(MERCHANT_TRANSACTION_SUMMARY_LIST));
        List columnDesc = new LinkedList();
        columnDesc.add(getValue(MERCHANT_NO));
        columnDesc.add(getValue(MERCHANT_NAME));
        croList = StatementSummaryStyleUtil.reSetColumnsByStyle(croList);
        columnDesc.addAll(getValueList(croList));
        StatementCommonUtil.appendLine(summarySheet, columnDesc);

        Iterator<String> merchantSnIterator = merchantSnStoresMap.keySet().iterator();
        Map sumMap = new LinkedHashMap();

        while (merchantSnIterator.hasNext()) {
            String merchantSn = merchantSnIterator.next();
            Map merchantSummary = (Map) merchantSummaryMap.get(merchantSn);
            List croParam = new LinkedList();
            croParam.add(merchantSn);
            croParam.add(BeanUtil.getPropString(merchantBasicInfoMap.get(merchantSn), ConstantUtil.KEY_MERCHANT_NAME));
            for (String cro : croList) {
                switch (cro) {
                    case TRANSACTION_NO:
                        long tradeCount = BeanUtil.getPropLong(sumMap, TRANSACTION_NO) + BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_COUNT);
                        sumMap.put(TRANSACTION_NO, tradeCount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_COUNT));
                        break;
                    case TRANSACTION_AMOUNT:
                        double tradeAmount = CommonUtil.getDoubleValue(sumMap, TRANSACTION_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_AMOUNT) / 100.0;
                        sumMap.put(TRANSACTION_AMOUNT, tradeAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_AMOUNT) / 100.0);
                        break;
                    case REFUND_NO:
                        long refundCount = BeanUtil.getPropLong(sumMap, REFUND_NO) + BeanUtil.getPropLong(merchantSummary, StatementSummary.REFUND_COUNT);
                        sumMap.put(REFUND_NO, refundCount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.REFUND_COUNT));
                        break;
                    case REFUND_AMOUNT:
                        double refundAmount = CommonUtil.getDoubleValue(sumMap, REFUND_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.REFUND_AMOUNT) / 100.0;
                        sumMap.put(REFUND_AMOUNT, refundAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.REFUND_AMOUNT) / 100.0);
                        break;
                    case TRANSACTION_NET_AMOUNT:
                        double tradeNetAmount = CommonUtil.getDoubleValue(sumMap, TRANSACTION_NET_AMOUNT) + (BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.REFUND_AMOUNT)) / 100.0;
                        sumMap.put(TRANSACTION_NET_AMOUNT, tradeNetAmount);
                        croParam.add((BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.REFUND_AMOUNT)) / 100.0);
                        break;
                    case MERCHANT_DISCOUNT:
                        double merchantDiscount = CommonUtil.getDoubleValue(sumMap, MERCHANT_DISCOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.MERCHANT_DISCOUNT) / 100.0;
                        sumMap.put(MERCHANT_DISCOUNT, merchantDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                        break;
                    case WOSAI_DISCOUNT:
                        double shouqianbaDiscount = CommonUtil.getDoubleValue(sumMap, WOSAI_DISCOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0;
                        sumMap.put(WOSAI_DISCOUNT, shouqianbaDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                        break;
                    case PAYMENT_TYPE_DISCOUNT:
                        double channelDiscount = CommonUtil.getDoubleValue(sumMap, PAYMENT_TYPE_DISCOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.CHANNEL_DISCOUNT) / 100.0;
                        sumMap.put(PAYMENT_TYPE_DISCOUNT, channelDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                        break;
                    case MERCHANT_DISCOUNT_PREPAID_MODE:
                        double channelMchTopUpDiscount = CommonUtil.getDoubleValue(sumMap, MERCHANT_DISCOUNT_PREPAID_MODE) + BeanUtil.getPropLong(merchantSummary, StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0;
                        sumMap.put(MERCHANT_DISCOUNT_PREPAID_MODE, channelMchTopUpDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                        break;
                    case MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                        double channelMchDiscount = CommonUtil.getDoubleValue(sumMap, MERCHANT_DISCOUNT_NON_PREPAID_MODE) + BeanUtil.getPropLong(merchantSummary, StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0;
                        sumMap.put(MERCHANT_DISCOUNT_NON_PREPAID_MODE, channelMchDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                        break;
                    case PAID_AMOUNT:
                        double receiveAmount = CommonUtil.getDoubleValue(sumMap, PAID_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.RECEIVE_AMOUNT) / 100.0;
                        sumMap.put(PAID_AMOUNT, receiveAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.RECEIVE_AMOUNT) / 100.0);
                        break;
                    case CHARGE:
                        double tradeFee = CommonUtil.getDoubleValue(sumMap, CHARGE) + BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_FEE) / 100.0;
                        sumMap.put(CHARGE, tradeFee);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_FEE) / 100.0);
                        break;
                    case SHARING_AMOUNT:
                        double sharingAmount = CommonUtil.getDoubleValue(sumMap, SHARING_AMOUNT) + (BeanUtil.getPropLong(merchantSummary, StatementSummary.SHARING_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT)) / 100.0;
                        sumMap.put(SHARING_AMOUNT, sharingAmount);
                        croParam.add((BeanUtil.getPropLong(merchantSummary, StatementSummary.SHARING_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT)) / 100.0);
                        break;
                    case TRADE_SERVICE_SHARING_AMOUNT:
                        double tradeServiceSharingAmount = CommonUtil.getDoubleValue(sumMap, TRADE_SERVICE_SHARING_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT) / 100.0;
                        sumMap.put(TRADE_SERVICE_SHARING_AMOUNT, tradeServiceSharingAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT) / 100.0);
                        break;
                    case MERCHANT_SHARING_AMOUNT:
                        double merchantSharingAmount = CommonUtil.getDoubleValue(sumMap, MERCHANT_SHARING_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.SHARING_AMOUNT) / 100.0;
                        sumMap.put(MERCHANT_SHARING_AMOUNT, merchantSharingAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.SHARING_AMOUNT) / 100.0);
                        break;
                    case SETTLEMENT_AMOUNT:
                        double incomeAmount = CommonUtil.getDoubleValue(sumMap, SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.INCOME_AMOUNT) / 100.0;
                        sumMap.put(SETTLEMENT_AMOUNT, incomeAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.INCOME_AMOUNT) / 100.0);
                        break;
                    case DEAL_TO_ACCOUNT_AMOUNT:
                        double dealToAccountAmount = CommonUtil.getDoubleValue(sumMap, DEAL_TO_ACCOUNT_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0;
                        sumMap.put(DEAL_TO_ACCOUNT_AMOUNT, dealToAccountAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                        break;

                    case StatementSummary.PREFIX + TRANSACTION_NO:
                        long merchantTradeCount = BeanUtil.getPropLong(sumMap, StatementSummary.PREFIX + TRANSACTION_NO) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_COUNT);
                        sumMap.put(StatementSummary.PREFIX + TRANSACTION_NO, merchantTradeCount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_COUNT));
                        break;
                    case StatementSummary.PREFIX + TRANSACTION_AMOUNT:
                        double merchantTradeAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + TRANSACTION_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + TRANSACTION_AMOUNT, merchantTradeAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + REFUND_NO:
                        long merchantRefundCount = BeanUtil.getPropLong(sumMap, StatementSummary.PREFIX + REFUND_NO) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.REFUND_COUNT);
                        sumMap.put(StatementSummary.PREFIX + REFUND_NO, merchantRefundCount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.REFUND_COUNT));
                        break;
                    case StatementSummary.PREFIX + REFUND_AMOUNT:
                        double merchantRefundAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + REFUND_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + REFUND_AMOUNT, merchantRefundAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + TRANSACTION_NET_AMOUNT:
                        double storeTradeNetAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + TRANSACTION_NET_AMOUNT) + (BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT)) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + TRANSACTION_NET_AMOUNT, storeTradeNetAmount);
                        croParam.add((BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT)) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_DISCOUNT:
                        double merchantMerchantDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + MERCHANT_DISCOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.MERCHANT_DISCOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + MERCHANT_DISCOUNT, merchantMerchantDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + WOSAI_DISCOUNT:
                        double merchantShouqianbaDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + WOSAI_DISCOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + WOSAI_DISCOUNT, merchantShouqianbaDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + PAYMENT_TYPE_DISCOUNT:
                        double merchantChannelDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + PAYMENT_TYPE_DISCOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_DISCOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + PAYMENT_TYPE_DISCOUNT, merchantChannelDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_DISCOUNT_PREPAID_MODE:
                        double merchantChannelMchTopUpDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + MERCHANT_DISCOUNT_PREPAID_MODE) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + MERCHANT_DISCOUNT_PREPAID_MODE, merchantChannelMchTopUpDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                        double merchantChannelMchDiscount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + MERCHANT_DISCOUNT_NON_PREPAID_MODE) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + MERCHANT_DISCOUNT_NON_PREPAID_MODE, merchantChannelMchDiscount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + PAID_AMOUNT:
                        double merchantReceiveAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + PAID_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.RECEIVE_AMOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + PAID_AMOUNT, merchantReceiveAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.RECEIVE_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + CHARGE:
                        double merchantTradeFee = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + CHARGE) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_FEE) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + CHARGE, merchantTradeFee);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_FEE) / 100.0);
                        break;
                    case StatementSummary.PREFIX + SHARING_AMOUNT:
                        double storeSharingAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + SHARING_AMOUNT) + (BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT)) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + SHARING_AMOUNT, storeSharingAmount);
                        croParam.add((BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT)) / 100.0);
                        break;
                    case StatementSummary.PREFIX + TRADE_SERVICE_SHARING_AMOUNT:
                        double storeTradeServiceSharingAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + TRADE_SERVICE_SHARING_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + TRADE_SERVICE_SHARING_AMOUNT, storeTradeServiceSharingAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_SHARING_AMOUNT:
                        double storeMerchantSharingAmount = CommonUtil.getDoubleValue(sumMap, MERCHANT_SHARING_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + MERCHANT_SHARING_AMOUNT, storeMerchantSharingAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + SETTLEMENT_AMOUNT:
                        double merchantIncomeAmount = CommonUtil.getDoubleValue(sumMap, StatementSummary.PREFIX + SETTLEMENT_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.INCOME_AMOUNT) / 100.0;
                        sumMap.put(StatementSummary.PREFIX + SETTLEMENT_AMOUNT, merchantIncomeAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.INCOME_AMOUNT) / 100.0);
                        break;
                    case STORE_IN_DEAL_TO_ACCOUNT_AMOUNT:
                        double storeInDealToAccountAmount = CommonUtil.getDoubleValue(sumMap, STORE_IN_DEAL_TO_ACCOUNT_AMOUNT) + BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0;
                        sumMap.put(STORE_IN_DEAL_TO_ACCOUNT_AMOUNT, storeInDealToAccountAmount);
                        croParam.add(BeanUtil.getPropLong(merchantSummary, StatementSummary.PREFIX + StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                        break;
                }
            }
            StatementCommonUtil.appendLine(summarySheet, croParam);
        }
        List sumParam = new LinkedList();
        sumParam.add(getValue(SUMMARY));
        sumParam.add("");
        for (String cro : croList) {
            sumParam.add(sumMap.get(cro));
        }
        StatementCommonUtil.appendLine(summarySheet, sumParam);
        StatementCommonUtil.appendLine(summarySheet, Arrays.asList());
    }


    private static void appendMerchantAloneSummary(SXSSFSheet summarySheet, Map merchantSummaryMap, Map merchantSnStoresMap, Map merchantBasicInfoMap, boolean isKAStatement) {
        StatementCommonUtil.appendLine(summarySheet, getValues(MERCHANT_TRANSACTION_SUMMARY_LIST));
        //集团对账单-商户交易汇总清单中只保留支付宝、微信、银联云闪付的结算金额
        List<Integer> payWayList = Lists.newArrayList(StatementObjectConfig.ALIPAY_OLD, StatementObjectConfig.ALIPAY_NEW, StatementObjectConfig.WEIXIN, StatementObjectConfig.YLCODEPAY);
        List columnDesc = StatementSummaryStyleUtil.groupDetailHeaderByStyle(payWayList);
        StatementSummaryStyleUtil.resetColumns(columnDesc, isKAStatement);
        StatementCommonUtil.appendLine(summarySheet, columnDesc);
        Iterator<String> merchantSnIterator = merchantSnStoresMap.keySet().iterator();
        while (merchantSnIterator.hasNext()) {
            String merchantSn = merchantSnIterator.next();
            Map merchantSummary = (Map) merchantSummaryMap.get(merchantSn);
            Map<String, Object> target = (Map<String, Object>) merchantBasicInfoMap.get(merchantSn);
            List<String> basicValues = Lists.newArrayList(
                    merchantSn,
                    BeanUtil.getPropString(target, ConstantUtil.KEY_MERCHANT_NAME));
            StatementSummaryStyleUtil.appendByStyle(summarySheet, merchantSummary, merchantBasicInfoMap, basicValues,  payWayList, false, false, true, isKAStatement);
        }
        StatementCommonUtil.appendLine(summarySheet, Arrays.asList());
    }

    private static void appendPaywaySummary(SXSSFSheet summarySheet, Map paywaySummaryMap, boolean isKAStatement) {
        StatementCommonUtil.appendLine(summarySheet, getValues(GROUP_TRANSACTION_SUMMARY));
        List<String> finalColumns = StatementSummaryStyleUtil.groupPayWayHeaderByStyle();

        List<String> descColumns = Lists.newArrayList();
        descColumns.add(PAYMENT_TYPE);
        descColumns.addAll(finalColumns);
        List<String> valueList = getValueList(descColumns);
        StatementSummaryStyleUtil.resetColumns(valueList, isKAStatement);
        StatementCommonUtil.appendLine(summarySheet, valueList);


        Set<Integer> paywaySet = new HashSet<>(StatementSummaryStyleUtil.getPayWayListByInclude());
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            paywaySet = Sets.newHashSet(BANK_CARD);
        }
        for (Integer payway : paywaySet) {
            Map paywaySummary = (Map) paywaySummaryMap.get(payway.toString());
            if (paywaySummary == null || paywaySummary.size() == 0) {
                paywaySummary = StatementTransactionSummaryUtil.getRecordTemplate();
            }
            List<String> basicValues = Lists.newArrayList(OrderUtil.getPaywayDesc(payway, getLanguage()));
            StatementSummaryStyleUtil.appendByStyle(summarySheet, paywaySummary, null, basicValues, false, false, false, isKAStatement);

        }

        StatementCommonUtil.appendLine(summarySheet, Arrays.asList());

    }


    private static void appendPaywaySummarySheet(SXSSFSheet summarySheet, Map paywaySummaryMap, List<String> croList) {
        StatementCommonUtil.appendLine(summarySheet, getValues(GROUP_TRANSACTION_SUMMARY));
        croList = StatementSummaryStyleUtil.reSetColumnsByStyle(croList);
        List columnDesc = new LinkedList();
        columnDesc.add(getValue(PAYMENT_TYPE));
        columnDesc.addAll(getValueList(croList));
        StatementCommonUtil.appendLine(summarySheet, columnDesc);
        Set<Integer> paywaySet = new HashSet<>(StatementSummaryStyleUtil.getPayWayListByInclude());
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            paywaySet = Sets.newHashSet(BANK_CARD);
        }

        for (Integer payway : paywaySet) {
            Map paywaySummary = (Map) paywaySummaryMap.get(payway.toString());
            if (paywaySummary == null || paywaySummary.size() == 0) {
                paywaySummary = StatementTransactionSummaryUtil.getRecordTemplate();
            }

            List croParam = new LinkedList();
            croParam.add(OrderUtil.getPaywayDesc(payway, getLanguage()));
            for (String cro : croList) {
                switch (cro) {
                    case TRANSACTION_NO:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "tradeCount"));
                        break;
                    case TRANSACTION_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "tradeAmount") / 100.0);
                        break;
                    case REFUND_NO:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "refundCount"));
                        break;
                    case REFUND_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "refundAmount") / 100.0);
                        break;
                    case TRANSACTION_NET_AMOUNT:
                        croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_AMOUNT)) / 100.0);
                        break;
                    case MERCHANT_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "merchantDiscount") / 100.0);
                        break;
                    case WOSAI_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "shouqianbaDiscount") / 100.0);
                        break;
                    case PAYMENT_TYPE_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "channelDiscount") / 100.0);
                        break;
                    case MERCHANT_DISCOUNT_PREPAID_MODE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "channelMchTopUpDiscount") / 100.0);
                        break;
                    case MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "channelMchDiscount") / 100.0);
                        break;
                    case PAID_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "receiveAmount") / 100.0);
                        break;
                    case CHARGE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "tradeFee") / 100.0);
                        break;
                    case SHARING_AMOUNT:
                        croParam.add((BeanUtil.getPropLong(paywaySummary, "sharingAmount") + BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT))/ 100.0);
                        break;
                    case TRADE_SERVICE_SHARING_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT) / 100.0);
                        break;
                    case MERCHANT_SHARING_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.SHARING_AMOUNT) / 100.0);
                        break;
                    case SETTLEMENT_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, "incomeAmount") / 100.0);
                        break;
                    case DEAL_TO_ACCOUNT_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                        break;

                    case StatementSummary.PREFIX + TRANSACTION_NO:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_COUNT));
                        break;
                    case StatementSummary.PREFIX + TRANSACTION_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + REFUND_NO:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.REFUND_COUNT));
                        break;
                    case StatementSummary.PREFIX + REFUND_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + TRANSACTION_NET_AMOUNT:
                        croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.REFUND_AMOUNT)) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + WOSAI_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + PAYMENT_TYPE_DISCOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_DISCOUNT_PREPAID_MODE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + PAID_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.RECEIVE_AMOUNT) / 100.0);
                        break;
                    case StatementSummary.PREFIX + CHARGE:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_FEE) / 100.0);
                        break;
                    case StatementSummary.PREFIX + SHARING_AMOUNT:
                        croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT, 0) + BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0)) / 100.0);
                        break;
                    case StatementSummary.PREFIX + TRADE_SERVICE_SHARING_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0) / 100.0);
                        break;
                    case StatementSummary.PREFIX + MERCHANT_SHARING_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.SHARING_AMOUNT, 0) / 100.0);
                        break;
                    case StatementSummary.PREFIX + SETTLEMENT_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.INCOME_AMOUNT) / 100.0);
                        break;
                    case STORE_IN_DEAL_TO_ACCOUNT_AMOUNT:
                        croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.PREFIX + StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                        break;
                }
            }

            StatementCommonUtil.appendLine(summarySheet, croParam);
        }

        StatementCommonUtil.appendLine(summarySheet, Arrays.asList());

    }


    private static void appendUnionSummary(SXSSFSheet summarySheet, Map unionSummaryMap, List<String> croList, List<Integer> paywayList, List<String> merchantSns,
                                           Map merchantBasicInfoMap, Map merchantSnStoresMap, Map storeBasicInfoMap) {
        //暂时不涉及银行卡报表
        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            return;
        }

        SheetHelper.appendLine(summarySheet, Arrays.asList(""));
        SheetHelper.appendLine(summarySheet, Arrays.asList(""));

        SheetHelper.appendLine(summarySheet, getValues(MERCHANT_STORE_PAYWAY_TRANSACTION_SUMMARY));
        List columnPayway = new LinkedList();

        columnPayway.add(getValue(MERCHANT_NAME));
        columnPayway.add(getValue(STORE_NAME));
        columnPayway.add(getValue(PAYMENT_TYPE));
        columnPayway.addAll(getValueList(croList));

        SheetHelper.appendLine(summarySheet, columnPayway);

        for (String merchantSn : merchantSns) {

            String merchantName = BeanUtil.getPropString(merchantBasicInfoMap.get(merchantSn), ConstantUtil.KEY_MERCHANT_NAME);
            SheetHelper.appendLine(summarySheet, Arrays.asList(merchantName));

            ArrayList<String> storeSnList = (ArrayList<String>) BeanUtil.getProperty(merchantSnStoresMap, merchantSn);

            for (String storeSn : storeSnList) {
                String storeName = BeanUtil.getPropString(storeBasicInfoMap.get(storeSn), "store_name");
                SheetHelper.appendLine(summarySheet, Arrays.asList("", storeName));

                for (Integer payway : paywayList) {
                    Map paywaySummary = (Map) unionSummaryMap.get(merchantSn + storeSn + payway);
                    if (paywaySummary == null || paywaySummary.size() == 0) {
                        paywaySummary = StatementTransactionSummaryUtil.getRecordTemplate();
                    }
                    List croParam = new LinkedList();
                    croParam.add("");
                    croParam.add("");
                    croParam.add(OrderUtil.getPaywayDesc(payway, getLanguage()));
                    for (String cro : croList) {
                        switch (cro) {
                            case TRANSACTION_NO:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_COUNT));
                                break;
                            case TRANSACTION_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_AMOUNT) / 100.0);
                                break;
                            case REFUND_NO:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_COUNT));
                                break;
                            case REFUND_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_AMOUNT) / 100.0);
                                break;
                            case TRANSACTION_NET_AMOUNT:
                                croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_AMOUNT) + BeanUtil.getPropLong(paywaySummary, StatementSummary.REFUND_AMOUNT)) / 100.0);
                                break;
                            case MERCHANT_DISCOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.MERCHANT_DISCOUNT) / 100.0);
                                break;
                            case WOSAI_DISCOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.SHOUQIANBA_DISCOUNT) / 100.0);
                                break;
                            case PAYMENT_TYPE_DISCOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CHANNEL_DISCOUNT) / 100.0);
                                break;
                            case MERCHANT_DISCOUNT_PREPAID_MODE:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CHANNEL_MCH_TOP_UP_DISCOUNT) / 100.0);
                                break;
                            case MERCHANT_DISCOUNT_NON_PREPAID_MODE:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CHANNEL_MCH_DISCOUNT) / 100.0);
                                break;
                            case PAID_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.RECEIVE_AMOUNT) / 100.0);
                                break;
                            case CHARGE:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_FEE) / 100.0);
                                break;
                            case SHARING_AMOUNT:
                                croParam.add((BeanUtil.getPropLong(paywaySummary, StatementSummary.SHARING_AMOUNT, 0) + BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0)) / 100.0);
                                break;
                            case TRADE_SERVICE_SHARING_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.TRADE_SERVICE_SHARING_AMOUNT, 0) / 100.0);
                                break;
                            case MERCHANT_SHARING_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.SHARING_AMOUNT, 0) / 100.0);
                                break;
                            case SETTLEMENT_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.INCOME_AMOUNT) / 100.0);
                                break;
                            case DEAL_TO_ACCOUNT_AMOUNT:
                                croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.DEAL_TO_ACCOUNT_AMOUNT) / 100.0);
                                break;
                            case CROSS_MERCHANT_REFUND_NO:
                                if (RunContext.currentContext().crossMchRefundEnable()) {
                                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CROSS_MCH_REFUND_COUNT));
                                }
                                break;
                            case CROSS_MERCHANT_REFUND_AMOUNT:
                                if (RunContext.currentContext().crossMchRefundEnable()) {
                                    croParam.add(BeanUtil.getPropLong(paywaySummary, StatementSummary.CROSS_MCH_REFUND_AMOUNT) / 100.0);
                                }
                                break;
                        }
                    }
                    SheetHelper.appendLine(summarySheet, croParam);
                }
            }
        }


        SheetHelper.appendLine(summarySheet, Arrays.asList());

    }

    private static void appendSummaryTitle(Map context, SXSSFSheet summarySheet) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat detailSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = (Date) context.get("start");
        Date end = (Date) context.get("end");
        String groupSn = BeanUtil.getPropString(context, "group_sn");
        String groupName = BeanUtil.getPropString(context, "group_name");
        String sid = String.format("SQB%s-%s-%s",
                groupSn,
                sdf.format(start),
                sdf.format(end)
        );

        StatementCommonUtil.appendLine(summarySheet, Arrays.asList(String.format(
                getValue(PERIOD) + ":[%s]—:[%s]",
                detailSdf.format(start) + CommonConstant.TIME_ZONE_TL.get(),
                detailSdf.format(end) + CommonConstant.TIME_ZONE_TL.get()
                ))
        );
        StatementCommonUtil.appendLine(summarySheet, Arrays.asList(getValue(STATEMENT_NO), sid));
        StatementCommonUtil.appendLine(summarySheet, getValues(GROUP_INFORMATION));
        StatementCommonUtil.appendLine(summarySheet, getValues(GROUP_NO, GROUP_NAME,STATEMENT_STYLE));
        StatementCommonUtil.appendLine(summarySheet, Arrays.asList(groupSn, groupName,SheetHelper.getStatementTypeByIncludes()));
        StatementCommonUtil.appendLine(summarySheet, Arrays.asList());

        if (DBSelectContext.getContext().getSelectDb() == UpayQueryType.UPAY_SWIPE) {
            StatementCommonUtil.appendLine(summarySheet, Arrays.asList("注意事项：每日15点更新昨日交易的手续费和结算金额，如需查看昨日交易的手续费和结算金额请于当日15点后下载对账单查询。"));
        }
        StatementCommonUtil.appendLine(summarySheet, Arrays.asList());
    }


}