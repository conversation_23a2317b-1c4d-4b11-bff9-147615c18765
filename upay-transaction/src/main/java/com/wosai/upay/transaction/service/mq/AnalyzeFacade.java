package com.wosai.upay.transaction.service.mq;

import com.wosai.common.utils.WosaiJsonUtils;
import com.wosai.upay.transaction.service.model.TransactionBean;
import com.wosai.upay.transaction.service.mq.handler.OrderCacheHandler;
import com.wosai.upay.transaction.service.mq.handler.TranIndexHandler;
import com.wosai.upay.transaction.util.PartitionConcurrentLinkedQueue;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/***
 * @ClassName: AnalyzeFacade
 * @Description:
 * @Auther: dabuff
 * @Date: 2024/8/27 15:37
 */
@Slf4j
@Component
public class AnalyzeFacade {

    @Resource
    private TranIndexHandler handler;

    @Autowired
    public PartitionConcurrentLinkedQueue<Map> partitionConcurrentLinkedQueue;

    public void analyzeTradeData(ConsumerRecord<?, GenericRecord> record) {

        TransactionBean transactionBean = AvroBeanHelper.getTransactionMapFromBean(record);
        //空过滤掉
        if(transactionBean == null){
            return;
        }

        process(transactionBean);
    }

    public void process(TransactionBean transactionBean) {

        log.info("transactionBean ->{}", WosaiJsonUtils.toJSONString(transactionBean));
        try {
            AvroBeanHelper.enhanceTransactionBean(transactionBean);
        } catch (Exception exception) {
            log.error("数据处理出错, tsn is {}", transactionBean.getTsn(), exception);
            return;
        }
        partitionConcurrentLinkedQueue.add(transactionBean.build2Map());
    }

    @SneakyThrows
    public void batchExecuteAndWaitExec() {
        if (!partitionConcurrentLinkedQueue.isEmpty()) {
            batchExecute();
        }
    }

    public void batchExecute(){
        List<Map> batch = partitionConcurrentLinkedQueue.getBatch();
        handler.handle(batch);
    }

}