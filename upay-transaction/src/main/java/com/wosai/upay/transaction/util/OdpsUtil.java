package com.wosai.upay.transaction.util;


import com.aliyun.odps.Column;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import com.wosai.middleware.aliyun.odps.DynamicAliyunAccount;
import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class OdpsUtil {
    private static Vault vault;

    public static Odps odps;

    @Value("${odps.endPoint}")
    private String endPoint;
    @Value("${odps.projectName}")
    private String projectName;

    static {
        try {
            loadVault();
        } catch (Exception e) {
            log.info(e.getMessage());
        }

    }

    private static void loadVault() {
        try {
            vault = Vault.autoload();
        } catch (VaultException e) {
            log.error("load vault error!", e);
        }
    }

    @PostConstruct
    private void init() {
        Account account = new DynamicAliyunAccount(vault);
        odps = new Odps(account);
        odps.setEndpoint(endPoint);
        odps.setDefaultProject(projectName);
        log.info("【OdpsUtils初始化】 endPoint: {}, projectName: {}" , endPoint, projectName);
    }

    @SneakyThrows
    /**
     *  获取sql的执行结果，适用于少量数据
     */
    public List<Map<String,Object>> getResult(String sql){
        Instance i = SQLTask.run(odps, sql);
        i.waitForSuccess();
        List<Record> result = SQLTask.getResult(i);
        if(result == null){
            return Collections.emptyList();
        }
        return result.stream().map(r -> {
            Map<String,Object> map = new LinkedHashMap<>();
            Column[] columns = r.getColumns();
            for (Column column : columns) {
                String columnName = column.getName();
                map.put(columnName, parseRecordColumnValue(r.get(columnName)));
            }
            return map;
        }).collect(Collectors.toList());
    }

    @SneakyThrows
    public String getSqlByName(String name){
        InputStream inputStream = new ClassPathResource(name).getInputStream();
        return IOUtils.readLines(inputStream, "utf8").stream().filter(line -> !line.trim().startsWith("--")).collect(Collectors.joining(" "));
    }





    private Object parseRecordColumnValue(Object columnValue){
        if(columnValue == null){
            return null;
        }
        Object value = columnValue;
        if (columnValue instanceof byte[]) {
            value = new String((byte[]) columnValue);
        } else if (columnValue instanceof java.sql.Date) {
            value = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(((Date) columnValue).getTime()));
        } else if(columnValue instanceof Date){
            value =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(columnValue);
        }
        return value.toString().replace(",", "");
    }





}
