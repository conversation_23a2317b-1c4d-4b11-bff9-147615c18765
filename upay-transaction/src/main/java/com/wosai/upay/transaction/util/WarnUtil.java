package com.wosai.upay.transaction.util;

import com.google.common.base.Joiner;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.pantheon.util.SystemUtil;
import com.wosai.upay.transaction.model.TTaskInfo;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class WarnUtil {

    private static Logger logger = LoggerFactory.getLogger(WarnUtil.class);

    public static final Cache<String, Object> CACHE_BLCOK = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(100).build();


    public static final Cache<String, Object> CACHE_TIMEOUT = CacheBuilder.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100).build();


    /**
     * 指定时间只告警一次
     */
    public static void warn(String message, String taskLogId) {
       if(Objects.isNull(CACHE_BLCOK.getIfPresent(taskLogId))){
           CACHE_BLCOK.put(taskLogId, taskLogId);
           warn(message);
       }
    }

    /**
     * 指定时间只告警一次
     */
    public static boolean warnTimeout(String message, String taskLogId) {
        if(Objects.isNull(CACHE_TIMEOUT.getIfPresent(taskLogId))){
            CACHE_TIMEOUT.put(taskLogId, taskLogId);
            warn(message);
            return true;
        }
        return false;
    }

    private static final CloseableHttpClient CLIENT = HttpClients.custom().build();

    private static final String URL = "https://open.feishu.cn/open-apis/bot/v2/hook/9c30c470-215f-42b6-93bf-36b9471cffef";
    private static final String ERROR_TEMPLATE = "{\n" +
            "    \"msg_type\": \"text\", \n" +
            "    \"content\": {\n" +
            "        \"text\": \"%s\"\n" +
            "    } \n" +
            "}";

    public static void warn(String message) {
        try {
            message = SystemUtil.getEnv() + "-" + message;
            HttpPost post = new HttpPost(URL);
            post.setConfig(RequestConfig.custom().setConnectTimeout(6000).setSocketTimeout(6000).build());
            StringEntity entity = new StringEntity(String.format(ERROR_TEMPLATE, message), "application/json", "UTF-8");
            post.setEntity(entity);
            CloseableHttpResponse response = CLIENT.execute(post);
            response.close();
            logger.warn("get lark response  url {} , message {}", URL, message);
        } catch (Exception e) {
            logger.error("response lark http error url {} ,message {}", message);
        }
    }

    public static void main(String[] args) {
        List<TTaskInfo> infoCollection = new ArrayList<>();
        TTaskInfo t1 = new TTaskInfo();
        t1.setTaskNum(100);
        t1.setType(1);
        t1.setBlockNum(2);
        WarnUtil.warn("任务详细:" + Joiner.on("-").join(infoCollection));
    }
}
