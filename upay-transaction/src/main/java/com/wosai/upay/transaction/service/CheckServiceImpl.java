package com.wosai.upay.transaction.service;

import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.OrderBy.OrderType;
import com.wosai.upay.model.Order;
import com.wosai.upay.model.Transaction;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.constant.DataPartitionConst;
import com.wosai.upay.transaction.enums.CommonStatus;
import com.wosai.upay.transaction.model.TAccountRecord;
import com.wosai.upay.transaction.model.TAccountRecordDay;
import com.wosai.upay.transaction.model.TAccountRecordDetail;
import com.wosai.upay.transaction.model.TAccountSumV;
import com.wosai.upay.transaction.model.TResultData;
import com.wosai.upay.transaction.model.param.AccountRecordParam;
import com.wosai.upay.transaction.model.param.GetTransactionDetailByIdParam;
import com.wosai.upay.transaction.service.dao.TransactionHBaseDao;
import com.wosai.upay.transaction.service.model.query.StatusTypeSubPayWayQuery;
import com.wosai.upay.transaction.service.model.query.TransactionHBaseQuery;
import com.wosai.upay.transaction.util.DateTimeUtil;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.SneakyThrows;

@AutoJsonRpcServiceImpl
public class CheckServiceImpl implements CheckService {
    @Autowired
    IAccountBookService accountBookService;
    @Autowired
    IAccountBookServiceOpen accountBookServiceOpen;
    @Autowired
    GatewaySupportService gatewaySupportService;
    @Autowired
    TransactionHBaseDao transactionHBaseDao;
    @Value("${upay_swipe.prefix}")
    String swipePrefix;

    List<SlsConfig> slsGroups = Arrays.asList(new SlsConfig("cn-hangzhou.log.aliyuncs.com", "upay-gateway", "upay-gateway")
                , new SlsConfig("cn-shenzhen.log.aliyuncs.com", "sz-qr-wap-pay", "upay-gateway")
            );

    @Override
    public void check() {
        checkAccountBook();
        checkAccountBookOpen();
        checkGatewaySupport();
    }

    @Override
    public void checkAccountBook() {
        getCheckData().stream().forEach(messageInfo -> {
            getAccountBookRecords(messageInfo);
            getMoreAccountBookRecords(messageInfo, "AccountBookService", param -> accountBookService.getMoreAccountBookRecords(param));
            getAccountSum(messageInfo, "AccountBookService", param -> accountBookService.getAccountSum(param));
            getRecordForDetail(messageInfo, "AccountBookService", () -> accountBookService.getRecordForDetail(messageInfo.getTsn(), messageInfo.getMerchantId()));
            getTransactionDetail(messageInfo);
            getTransactionDetailById(messageInfo);
        });
    }
    

    @Override
    public void checkAccountBookOpen() {
        getCheckData().stream().forEach(messageInfo -> {
            getMoreAccountBookRecords(messageInfo, "AccountBookServiceOpen", param -> accountBookServiceOpen.getMoreAccountBookRecords(JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(param), Map.class)));
            getAccountSum(messageInfo, "AccountBookServiceOpen", param -> accountBookServiceOpen.getAccountSum(JsonUtil.jsonStrToObject(JsonUtil.toJsonStr(param), Map.class)));
            getRecordForDetail(messageInfo, "AccountBookServiceOpen", () -> accountBookServiceOpen.getRecordForDetail(MapUtil.hashMap(CommonConstant.TRANSACTION_SN, messageInfo.getTsn(), CommonConstant.STORE_ID, messageInfo.getStoreId())));
        });
    }

    @Override
    public void checkGatewaySupport() {
        getCheckData().stream().forEach(messageInfo -> {
            getOrderBySn(messageInfo);
            getOrderBySnAndDate(messageInfo);
            getLatestTransactionByOrderSn(messageInfo);
            getPayOrConsumerTransaction(messageInfo);
            getSuccessTransactionList(messageInfo);
            getRefundSuccessTransactionList(messageInfo);
            getAlipayDepositConsumeTransactionList(messageInfo);
        });
    }

    private void getAlipayDepositConsumeTransactionList(MessageInfo messageInfo) {
        gatewaySupportService.getAlipayDepositConsumeTransactionList(messageInfo.getMerchantId(), messageInfo.getOrderSn(), Long.valueOf(messageInfo.getCtime()));
    }

    private void getRefundSuccessTransactionList(MessageInfo messageInfo) {
        gatewaySupportService.getRefundSuccessTransactionList(messageInfo.getMerchantId(), messageInfo.getOrderSn(), Long.valueOf(messageInfo.getCtime()));
    }

    private void getSuccessTransactionList(MessageInfo messageInfo) {
        List<Map<String, Object>> rs = gatewaySupportService.getSuccessTransactionList(messageInfo.getMerchantId(), messageInfo.getOrderSn(), Long.valueOf(messageInfo.getCtime()));
        Assert.isTrue(rs != null && rs.size() > 0 , String.format("单号: %s 调用 GatewaySupportService.getSuccessTransactionList 接口验证失败", messageInfo.getTsn()));
    }

    private void getPayOrConsumerTransaction(MessageInfo messageInfo) {
        Map<String, Object> rs = gatewaySupportService.getPayOrConsumerTransaction(messageInfo.getMerchantId(), messageInfo.getOrderSn(), Long.valueOf(messageInfo.getCtime()));
        Assert.isTrue(rs != null && messageInfo.getOrderSn().equals(MapUtil.getString(rs, Transaction.ORDER_SN)), String.format("单号: %s 调用 GatewaySupportService.getPayOrConsumerTransaction 接口验证失败", messageInfo.getTsn()));
    }

    private void getLatestTransactionByOrderSn(MessageInfo messageInfo) {
        Map<String, Object> rs = gatewaySupportService.getLatestTransactionByOrderSn(messageInfo.getMerchantId(), messageInfo.getOrderSn(), Long.valueOf(messageInfo.getCtime()));
        Assert.isTrue(rs != null && messageInfo.getOrderSn().equals(MapUtil.getString(rs, Transaction.ORDER_SN)), String.format("单号: %s 调用 GatewaySupportService.getLatestTransactionByOrderSn 接口验证失败", messageInfo.getTsn()));
    }

    private void getOrderBySnAndDate(MessageInfo messageInfo) {
        Map<String, Object> rs = gatewaySupportService.getOrderBySnAndDate(messageInfo.getMerchantId(), messageInfo.getOrderSn(), DateTimeUtil.format(DateTimeUtil.DAY_FORMAT, Long.valueOf(messageInfo.getCtime())));
        Assert.isTrue(rs != null && messageInfo.getOrderSn().equals(MapUtil.getString(rs, Order.SN)), String.format("单号: %s 调用 GatewaySupportService.getOrderBySnAndDate 接口验证失败", messageInfo.getTsn()));
    }

    private void getOrderBySn(MessageInfo messageInfo) {
        Map<String, Object> rs = gatewaySupportService.getOrderBySn(messageInfo.getMerchantId(), messageInfo.getOrderSn(), null, DataPartitionConst.HOT);
        Assert.isTrue(rs != null && messageInfo.getOrderSn().equals(MapUtil.getString(rs, Order.SN)) , String.format("单号: %s 调用 GatewaySupportService.getOrderBySn 接口验证失败", messageInfo.getTsn()));
    }

    private void getAccountBookRecords(MessageInfo messageInfo) {
        AccountRecordParam param = new AccountRecordParam();
        param.setMerchant_id(messageInfo.getMerchantId());
        param.setStartTime(new Date(Long.valueOf(messageInfo.getCtime())));
        param.setTransactionSn(messageInfo.getTsn());
        param.setToken(CommonConstant.OUTSIDE_CALL);
        param.setSimple(true);
        TResultData<List<TAccountRecordDay>> rs = accountBookService.getAccountBookRecords(param);
        Assert.isTrue(rs != null && rs.getData() != null, String.format("单号: %s 调用 AccountBookService.getAccountBookRecords 接口验证失败", messageInfo.getTsn()));
        String day = CommonConstant.DAY_SDF.get().format(param.getStartTime());
        boolean isMatch = false;
        for (TAccountRecordDay recordDay : rs.getData()) {
            if (day.equals(recordDay.getDay())) {
                Assert.isTrue(recordDay.getTransactions().size() > 0, String.format("单号: %s 调用 AccountBookService.getAccountBookRecords 接口验证失败", messageInfo.getTsn()));
                for (TAccountRecord record: recordDay.getTransactions()) {
                    if (messageInfo.getTsn().equals(record.getTransactionSn())){
                        isMatch = true;
                    }
                }
                Assert.isTrue(isMatch, String.format("单号: %s 调用 AccountBookService.getAccountBookRecords 接口验证失败", messageInfo.getTsn()));
                break;
            }
        }
    }

    private void getMoreAccountBookRecords(MessageInfo messageInfo, String service, Function<AccountRecordParam, TResultData<List<TAccountRecordDay>>> fn) {
        AccountRecordParam param = new AccountRecordParam();
        param.setMerchant_id(messageInfo.getMerchantId());
        param.setStartTime(new Date(Long.valueOf(messageInfo.getCtime())));
        param.setEndTime(new Date());
        param.setTransactionSn(messageInfo.getTsn());
        param.setToken(CommonConstant.OUTSIDE_CALL);
        TResultData<List<TAccountRecordDay>> rs = fn.apply(param);
        Assert.isTrue(rs != null && rs.getData() != null, String.format("单号: %s 调用 %s.getMoreAccountBookRecords 接口验证失败", messageInfo.getTsn(), service));
        String day = CommonConstant.DAY_SDF.get().format(param.getStartTime());
        boolean isMatch = false;
        for (TAccountRecordDay recordDay : rs.getData()) {
            if (day.equals(recordDay.getDay())) {
                Assert.isTrue(recordDay.getTransactions().size() > 0, String.format("单号: %s 调用 %s.getMoreAccountBookRecords 接口验证失败", messageInfo.getTsn(), service));
                for (TAccountRecord record: recordDay.getTransactions()) {
                    if (messageInfo.getTsn().equals(record.getTransactionSn())){
                        isMatch = true;
                    }
                }
                Assert.isTrue(isMatch, String.format("单号: %s 调用 %s.getMoreAccountBookRecords 接口验证失败", messageInfo.getTsn(), service));
                break;
            }
        }
    }

    private void getAccountSum(MessageInfo messageInfo, String service, Function<AccountRecordParam, TAccountSumV> fn) {
        AccountRecordParam param = new AccountRecordParam();
        param.setMerchant_id(messageInfo.getMerchantId());
        param.setStartTime(new Date(Long.valueOf(messageInfo.getCtime())));
        param.setTransactionSn(messageInfo.getTsn());
        param.setToken(CommonConstant.OUTSIDE_CALL);
        TAccountSumV rs = fn.apply(param);
        Assert.isTrue(rs != null && rs.getSalesCount() != null, String.format("单号: %s 调用 %s.getAccountSum 接口验证失败", messageInfo.getTsn(), service));
    }

    private void getRecordForDetail(MessageInfo messageInfo, String service, Supplier<TAccountRecordDetail> sp) {
        TAccountRecordDetail rs = sp.get();
        Assert.isTrue(rs != null && messageInfo.getTsn().equals(rs.getTransactionSn()), String.format("单号: %s 调用 %s.getMoreAccountBookRecords 接口验证失败", messageInfo.getTsn(), service));
    }

    private void getTransactionDetail(MessageInfo messageInfo) {
        TAccountRecordDetail rs = accountBookService.getTransactionDetail(messageInfo.getMerchantId(), Long.valueOf(messageInfo.getCtime()), messageInfo.getTsn(), false);
        Assert.isTrue(rs != null && messageInfo.getTsn().equals(rs.getTransactionSn()), String.format("单号: %s 调用 AccountBookService.getTransactionDetail 接口验证失败", messageInfo.getTsn()));
    }

    private void getTransactionDetailById(MessageInfo messageInfo) {
        GetTransactionDetailByIdParam param = new GetTransactionDetailByIdParam();
        param.setMerchantId(messageInfo.getMerchantId());
        param.setCtime(Long.valueOf(messageInfo.getCtime()));
        param.setTransactionId("t" + messageInfo.getTsn());
        TAccountRecordDetail rs = accountBookService.getTransactionDetailById(param);
        Assert.isTrue(rs != null && messageInfo.getTsn().equals(rs.getTransactionSn()), String.format("单号: %s 调用 AccountBookService.getTransactionDetailById 接口验证失败", messageInfo.getTsn()));
    }

    private List<MessageInfo> getCheckData() {
        int end = Long.valueOf(System.currentTimeMillis() / 1000).intValue();
        int start = end - 600;
        List<MessageInfo> messageInfos = new ArrayList<>();
        // 抽取近7天线上交易数据，测试环境不抽取
        if (swipePrefix.startsWith("7896")) {
            slsGroups.stream().forEach(info -> {
                // 抽取近10分钟交易，随机选择100笔订单（查询es数据，5个分片，不同分片数据各20笔）
                messageInfos.addAll(getMessageInfoBySls(info, "message: PRE_SUCCESS and logger_name: \"com.wosai.upay.workflow.WorkflowDriver\" not message: unable not message: deposit", start, end, 100, 5));
    
                // 抽取4天前交易，随机选择100笔订单（查询lindorm数据，20个分片）
                messageInfos.addAll(getMessageInfoBySls(info, "message: PRE_SUCCESS and logger_name: \"com.wosai.upay.workflow.WorkflowDriver\" not message: unable not message: deposit", start - 4 * 24 * 60, end - 4 * 24 * 60, 100, 20));
            });
        }
        // 抽取1个月前的交易，选择前100笔订单
        LocalDate lastMonthStart = LocalDate.now().minusDays(32);
        TransactionHBaseQuery hbaseQuery = new TransactionHBaseQuery();
        hbaseQuery.setStartTime(lastMonthStart.atStartOfDay().toInstant(ZoneOffset.ofHours(8)).toEpochMilli() + ThreadLocalRandom.current().nextInt(24) * DateTimeUtil.ONE_HOUR_MILLIS);
        hbaseQuery.setEndTime(lastMonthStart.atStartOfDay().plusDays(1).toInstant(ZoneOffset.ofHours(8)).toEpochMilli());
        hbaseQuery.setOrderBy(new OrderBy(DaoConstants.CTIME, OrderType.ASC));
        StatusTypeSubPayWayQuery statusTypeSubPayWayQuery = new StatusTypeSubPayWayQuery();
        statusTypeSubPayWayQuery.setStatusList(Lists.newArrayList(Transaction.STATUS_SUCCESS));
        statusTypeSubPayWayQuery.setTypeList(Arrays.asList(Transaction.TYPE_PAYMENT));
        hbaseQuery.getStatusTypeSubPayWayQueries().put(CommonStatus.SUCCESS, statusTypeSubPayWayQuery);
        hbaseQuery.setLimit(100);
        List<Map<String, Object>> transactions = transactionHBaseDao.queryList(hbaseQuery);
        transactions.forEach(tr -> {
            messageInfos.add(new MessageInfo(MapUtil.getString(tr, Transaction.TSN), MapUtil.getString(tr, Transaction.ORDER_SN), MapUtil.getString(tr, Transaction.MERCHANT_ID), MapUtil.getString(tr, DaoConstants.CTIME), MapUtil.getString(tr, Transaction.STORE_ID)));
        });
        return messageInfos;
    }

    @SneakyThrows
    private List<MessageInfo> getMessageInfoBySls(SlsConfig slsConfig, String query, int start, int end, int totalSize, int route) {
        if (totalSize % route != 0) {
            throw new RuntimeException("totalSize和route不匹配");
        }
        Map<Integer, List<MessageInfo>> logMap = new HashMap<>();
        int oneRouteMaxSize = totalSize / route;
        for (int i = 0; i < route; i++) {
            logMap.put(i, new ArrayList<>(oneRouteMaxSize));
        }
        Client logClient = new Client(slsConfig.getEndpoint(), "pay");
        int offset = 0;
        int cnt = 0;
        while(true){
            GetLogsResponse response = null;
            response = logClient.GetLogs(slsConfig.getProject(), slsConfig.getStore(), start, end, "", query, 100, offset, false);
            if(response.IsCompleted()){
                for (QueriedLog log : response.getLogs()) {
                    String source = log.GetLogItem().ToJsonString();
                    Map sourceMap = JsonUtil.jsonStringToObject(source, Map.class);
                    String message = MapUtil.getString(sourceMap, "message");
                    // 预授权交易无法通过关键词过滤
                    if (message.contains("deposit")) {
                        continue;
                    }
                    String tsn = StringUtils.substringBetween(message, "tsn=", ", ");
                    String orderSn = StringUtils.substringBetween(message, "order_sn=", ", ");
                    String merchantId = StringUtils.substringBetween(message, "merchant_id=", ", ");
                    String ctime = StringUtils.substringBetween(message, "ctime=", ", ");
                    String storeId = StringUtils.substringBetween(message, "store_id=", ", ");
                    if (StringUtils.isAnyEmpty(tsn, orderSn, merchantId, ctime, storeId)) {
                        continue;
                    }
                    List<MessageInfo> infos = logMap.get(Math.abs(merchantId.hashCode() % route));
                    if (infos.size() == oneRouteMaxSize) {
                        continue;
                    }
                    infos.add(new MessageInfo(tsn, orderSn, merchantId, ctime, storeId));
                    cnt++;
                }
                if(response.GetCount() < 100 || cnt == 100){
                    break;
                }
                offset += 100;
            }
        }
        return logMap.values().stream().flatMap(t -> t.stream()).collect(Collectors.toList());
    }

    @Getter
    @AllArgsConstructor
    class SlsConfig {
        String endpoint;
        String project;
        String store;
    }

    @Getter
    @AllArgsConstructor
    class MessageInfo{
        String tsn;
        String orderSn;
        String merchantId;
        String ctime;
        String storeId;
    }
}
