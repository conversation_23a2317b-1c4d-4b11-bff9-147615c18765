package com.wosai.upay.transaction.helper;

import com.wosai.upay.transaction.annotation.DBSelectService;
import org.aopalliance.aop.Advice;
import org.springframework.aop.Advisor;
import org.springframework.aop.Pointcut;
import org.springframework.aop.framework.AopInfrastructureBean;
import org.springframework.aop.framework.ProxyConfig;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.aop.support.annotation.AnnotationMatchingPointcut;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanClassLoaderAware;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.util.ClassUtils;

/**
 * <AUTHOR>
 */
public class DBSelectPostProcessor extends ProxyConfig
        implements BeanPostProcessor, BeanClassLoaderAware, Ordered, InitializingBean {

    private Advisor advisor;

    private Advice advice;

    public void setAdvice(Advice advice) {
        this.advice = advice;
    }

    private ClassLoader beanClassLoader = ClassUtils.getDefaultClassLoader();

    @Override
    public void setBeanClassLoader(ClassLoader classLoader) {
        this.beanClassLoader = classLoader;
    }

    @Override
    public void afterPropertiesSet(){
        Pointcut pointcut = new AnnotationMatchingPointcut(DBSelectService.class, true);
        this.advisor = new DefaultPointcutAdvisor(pointcut, advice);
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof AopInfrastructureBean) {
            // Ignore AOP infrastructure such as scoped proxies.
            return bean;
        }
        Class<?> targetClass = AopUtils.getTargetClass(bean);
        if (AopUtils.canApply(this.advisor, targetClass)) {
            ProxyFactory proxyFactory = new ProxyFactory(bean);
            // Copy our properties (proxyTargetClass etc) inherited from ProxyConfig.
            proxyFactory.copyFrom(this);
            proxyFactory.addAdvisor(this.advisor);
            return proxyFactory.getProxy(this.beanClassLoader);
        } else {
            // This is not a repository.
            return bean;
        }
    }

    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE;
    }
}
