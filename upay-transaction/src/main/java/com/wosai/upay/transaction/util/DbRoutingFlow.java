package com.wosai.upay.transaction.util;

import com.google.common.collect.Sets;
import com.wosai.data.bean.BeanUtil;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class DbRoutingFlow {

    private static final ConcurrentHashMap<Integer, Semaphore> RoutingRateLimiter = new ConcurrentHashMap<>();

    private static final Set<Integer> shardIds = IntStream.range(0, 32).boxed().collect(Collectors.toSet());

    public static final ConcurrentHashMap<Integer, AtomicLong> shardBlockMonitor = new ConcurrentHashMap<>();


    static {
        shardIds.stream().forEach(s ->{
            shardBlockMonitor.put(s, new AtomicLong(0));
            RoutingRateLimiter.put(s, new Semaphore(3));

        });
    }


    public static Set<String> getMerchantIds(Map queryFilter){
        if(!CollectionUtils.isEmpty(queryFilter)){
            String merchantId = BeanUtil.getPropString(queryFilter, "merchant_id");
            List<String> merchantIds = (List) BeanUtil.getProperty(queryFilter, "merchant_ids");
            Set<String> merchantIdSet = Sets.newHashSet();
            if(StringUtils.hasText(merchantId)){
                merchantIdSet.add(merchantId);
            }
            if(!CollectionUtils.isEmpty(merchantIds)){
                merchantIdSet.addAll(merchantIds);
            }
            return merchantIdSet;

        }else {
            throw new NullPointerException("查询条件不能为空!");
        }

    }

    public static void acquire(Set<String> merchantIds){
        if(CollectionUtils.isEmpty(merchantIds)){
            return;
        }
        TreeSet<Integer> shardIdSet = merchantIds.stream()
                .map(DbRoutingFlow::getShardId)
                .collect(Collectors.toCollection(TreeSet::new));
        shardIdSet.stream().forEach(m -> shardBlockMonitor.get(m).getAndIncrement());
        shardIdSet.forEach(mid -> {
            try {
                RoutingRateLimiter.get(mid).acquire();
            }catch (Exception ex){
                //
            }
        });
    }


    public static void release(Set<String> merchantIds){
        if(CollectionUtils.isEmpty(merchantIds)){
            return;
        }
        Set<Integer> shardIdSet = merchantIds.stream().map(merchantId -> getShardId(merchantId)).collect(Collectors.toSet());
        shardIdSet.forEach(mid -> {
            try {
                RoutingRateLimiter.get(mid).release();
                shardBlockMonitor.get(mid).getAndDecrement();
            }catch (Exception ex){
                //
            }
        });
    }



    private static Integer getShardId(String merchantId){
        return (Math.abs(merchantId.hashCode()) % 32);
    }


}
