package com.wosai.upay.transaction.service.service.common;


import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.transaction.constant.CommonConstant;
import com.wosai.upay.transaction.enums.OperatorType;
import com.wosai.upay.transaction.enums.SubPayWayType;
import com.wosai.upay.transaction.enums.TransactionType;
import com.wosai.upay.transaction.service.model.TerminalCode;
import com.wosai.upay.transaction.service.model.vo.TransactionVo;
import com.wosai.upay.transaction.service.service.client.IAccountStoreService;
import com.wosai.upay.transaction.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class OperatorService {

    public static final Logger logger = LoggerFactory.getLogger(OperatorService.class);

    @Autowired
    private IAccountStoreService accountStoreService;

    public TransactionVo setOperatorAndTerminalType(TransactionVo transactionVo, Map<String, String> storeStaffMap){
        setOperator(transactionVo, storeStaffMap);
        setTerminalType(transactionVo);
        return transactionVo;
    }

    public void setOperator(TransactionVo transactionVo, Map<String, String> storeStaffMap){
        String operator = OperatorType.CASHIER.getDesc();
        if(SubPayWayType.SUB_PAYWAY_WAP.getCode().equals(transactionVo.getSubPayWay()) && TransactionType.PAYMENT.getCode().equals(transactionVo.getType())){
            String vendorAppAppid = transactionVo.getVendorAppAppid();
            Pair<String, String> pair = Pair.of(transactionVo.getTerminalName(), vendorAppAppid);
            if (StringUtils.isEmpty(vendorAppAppid) || StringUtils.isEmpty(transactionVo.getTerminalName())){
                pair = getTerminal(transactionVo.getTerminalId(), false);
            }
            if(Objects.equals(CommonConstant.VENDOR_APP_APP_ID_APP, pair.snd)){
                operator = getOperatorForStaff(transactionVo.getOperator(), storeStaffMap);//收银员
            }else {
                operator = pair.fst;
            }
        } else if(WosaiMapUtils.isNotEmpty(storeStaffMap) && storeStaffMap.containsKey(transactionVo.getOperator())) {
            //收银员
            operator = getOperatorForStaff(transactionVo.getOperator(), storeStaffMap);//收银员
        } else if(!StringUtils.isEmpty(transactionVo.getTerminalId())) {
            operator = StringUtils.isEmpty(transactionVo.getTerminalName()) ? getTerminal(transactionVo.getTerminalId(), true).fst : transactionVo.getTerminalName();
        }
        transactionVo.setOperator(operator);
    }

    public TransactionVo setTerminalType(TransactionVo transactionVo){
         if (!StringUtils.isEmpty(transactionVo.getTerminalId())) {
             TerminalCode terminalCode = accountStoreService.findTerminalByTerminalId(transactionVo.getTerminalId());
             if(terminalCode != null){
                 String categoryName = terminalCode.getCategoryName();
                 String vendorAppName = terminalCode.getVendorAppName();
                 String typeName = terminalCode.getTypeName();
                 String terminalType = typeName;
                 if(categoryName != null){
                     terminalType = categoryName;
                 }else if(vendorAppName != null){
                     terminalType = vendorAppName;
                 }
                 transactionVo.setTerminalType(terminalType);
                 transactionVo.setTerminalSn(terminalCode.getSn());
             }
         }
        return transactionVo;
    }

    private String getLast8Characters(String params) {
        if (WosaiStringUtils.isBlank(params)) {
            return "";
        }
        if (params.length() <= 8) {
            return params;
        }
        return params.substring(params.length() - 8);
    }

    private Pair<String, String> getTerminal(String terminalId, boolean isTerminal) {
        try {
            if (WosaiStringUtils.isBlank(terminalId)) {
                return Pair.of(isTerminal ? OperatorType.TERMINAL.getDesc() : OperatorType.QR_CODE.getDesc(), null);
            }
            TerminalCode terminalCode = accountStoreService.findTerminalByTerminalId(terminalId);
            if (WosaiStringUtils.isNotBlank(terminalCode.getName())) {
                return Pair.of(terminalCode.getName(), terminalCode.getVendorAppAppid());
            }
            return Pair.of(getLast8Characters(terminalCode.getDeviceFingerprint()), null);
        } catch (Exception e) {
            logger.error("getTerminal error:" + e.getMessage());
        }
        return Pair.of(isTerminal ? OperatorType.TERMINAL.getDesc() : OperatorType.QR_CODE.getDesc(), null);
    }

    private String getOperatorForStaff(String defaultOperator, Map<String, String> storeStaffMap) {
        String resultOperator = "";
        if (WosaiMapUtils.isNotEmpty(storeStaffMap) && storeStaffMap.containsKey(defaultOperator)) {
            resultOperator = storeStaffMap.get(defaultOperator);
        }
        return WosaiStringUtils.isBlank(resultOperator) ? OperatorType.CASHIER.getDesc() : resultOperator;
    }
}
